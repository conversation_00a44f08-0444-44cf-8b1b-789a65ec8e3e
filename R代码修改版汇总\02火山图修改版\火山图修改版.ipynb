{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>'Protein'</li><li>'Protein.Name'</li><li>'Genename'</li><li>'Function'</li><li>'ProteinGroup'</li><li>'FastaHeaders'</li><li>'Proteins'</li><li>'Peptides'</li><li>'RazorUniquePeptides'</li><li>'UniquePeptides'</li><li>'Coverage'</li><li>'MolWeight'</li><li>'LFQ.intensity.gushidai.1'</li><li>'LFQ.intensity.lilongfeng.1'</li><li>'LFQ.intensity.shenli.1'</li><li>'LFQ.intensity.sunxing.1'</li><li>'LFQ.intensity.weizhengshu.1'</li><li>'LFQ.intensity.zhangpingai.1'</li><li>'LFQ.intensity.zhourongchang.1'</li><li>'average.MVI.P'</li><li>'LFQ.intensity.huyunzhen.0'</li><li>'LFQ.intensity.lixiaonan.0'</li><li>'LFQ.intensity.panzhihua.0'</li><li>'LFQ.intensity.qinliqiang.0'</li><li>'LFQ.intensity.xuhuolai.0'</li><li>'LFQ.intensity.yemeitou.0'</li><li>'LFQ.intensity.yuanguiwei.0'</li><li>'LFQ.intensity.zhousanman.0'</li><li>'average.MVI.N'</li><li>'MVI.P.MVI.N'</li><li>'pvalue'</li><li>'log2FC'</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 'Protein'\n", "\\item 'Protein.Name'\n", "\\item 'Genename'\n", "\\item 'Function'\n", "\\item 'ProteinGroup'\n", "\\item 'FastaHeaders'\n", "\\item 'Proteins'\n", "\\item 'Peptides'\n", "\\item 'RazorUniquePeptides'\n", "\\item 'UniquePeptides'\n", "\\item 'Coverage'\n", "\\item 'MolWeight'\n", "\\item 'LFQ.intensity.gushidai.1'\n", "\\item 'LFQ.intensity.lilongfeng.1'\n", "\\item 'LFQ.intensity.shenli.1'\n", "\\item 'LFQ.intensity.sunxing.1'\n", "\\item 'LFQ.intensity.weizhengshu.1'\n", "\\item 'LFQ.intensity.zhangpingai.1'\n", "\\item 'LFQ.intensity.zhourongchang.1'\n", "\\item 'average.MVI.P'\n", "\\item 'LFQ.intensity.huyunzhen.0'\n", "\\item 'LFQ.intensity.lixiaonan.0'\n", "\\item 'LFQ.intensity.panzhihua.0'\n", "\\item 'LFQ.intensity.qinliqiang.0'\n", "\\item 'LFQ.intensity.xuhuolai.0'\n", "\\item 'LFQ.intensity.yemeitou.0'\n", "\\item 'LFQ.intensity.yuanguiwei.0'\n", "\\item 'LFQ.intensity.zhousanman.0'\n", "\\item 'average.MVI.N'\n", "\\item 'MVI.P.MVI.N'\n", "\\item 'pvalue'\n", "\\item 'log2FC'\n", "\\end{enumerate*}\n"], "text/markdown": ["1. '<PERSON><PERSON>'\n", "2. 'Protein.Name'\n", "3. 'Genename'\n", "4. 'Function'\n", "5. 'ProteinGroup'\n", "6. '<PERSON><PERSON><PERSON><PERSON><PERSON>'\n", "7. '<PERSON>tein<PERSON>'\n", "8. 'Peptides'\n", "9. 'RazorUniquePeptides'\n", "10. 'UniquePeptides'\n", "11. 'Coverage'\n", "12. 'Mo<PERSON>Weight'\n", "13. 'LFQ.intensity.gushidai.1'\n", "14. 'LFQ.intensity.lilongfeng.1'\n", "15. 'LFQ.intensity.shenli.1'\n", "16. 'LFQ.intensity.sunxing.1'\n", "17. 'LFQ.intensity.weizhengshu.1'\n", "18. 'LFQ.intensity.zhangpingai.1'\n", "19. 'LFQ.intensity.zhourongchang.1'\n", "20. 'average.MVI.P'\n", "21. 'LFQ.intensity.huyunzhen.0'\n", "22. 'LFQ.intensity.lixiaonan.0'\n", "23. 'LFQ.intensity.panzhihua.0'\n", "24. 'LFQ.intensity.qinliqiang.0'\n", "25. 'LFQ.intensity.xuhuolai.0'\n", "26. 'LFQ.intensity.yemeitou.0'\n", "27. 'LFQ.intensity.yuanguiwei.0'\n", "28. 'LFQ.intensity.zhousanman.0'\n", "29. 'average.MVI.N'\n", "30. 'MVI.P.MVI.N'\n", "31. 'pvalue'\n", "32. 'log2FC'\n", "\n", "\n"], "text/plain": [" [1] \"Protein\"                       \"Protein.Name\"                 \n", " [3] \"Genename\"                      \"Function\"                     \n", " [5] \"ProteinGroup\"                  \"FastaHeaders\"                 \n", " [7] \"Proteins\"                      \"Peptides\"                     \n", " [9] \"RazorUniquePeptides\"           \"UniquePeptides\"               \n", "[11] \"Coverage\"                      \"MolWeight\"                    \n", "[13] \"LFQ.intensity.gushidai.1\"      \"LFQ.intensity.lilongfeng.1\"   \n", "[15] \"LFQ.intensity.shenli.1\"        \"LFQ.intensity.sunxing.1\"      \n", "[17] \"LFQ.intensity.weizhengshu.1\"   \"LFQ.intensity.zhangpingai.1\"  \n", "[19] \"LFQ.intensity.zhourongchang.1\" \"average.MVI.P\"                \n", "[21] \"LFQ.intensity.huyunzhen.0\"     \"LFQ.intensity.lixiaonan.0\"    \n", "[23] \"LFQ.intensity.panzhihua.0\"     \"LFQ.intensity.qinliqiang.0\"   \n", "[25] \"LFQ.intensity.xuhuolai.0\"      \"LFQ.intensity.yemeitou.0\"     \n", "[27] \"LFQ.intensity.yuanguiwei.0\"    \"LFQ.intensity.zhousanman.0\"   \n", "[29] \"average.MVI.N\"                 \"MVI.P.MVI.N\"                  \n", "[31] \"pvalue\"                        \"log2FC\"                       "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>'Protein'</li><li>'Protein.Name'</li><li>'Genename'</li><li>'Function'</li><li>'ProteinGroup'</li><li>'FastaHeaders'</li><li>'Proteins'</li><li>'Peptides'</li><li>'RazorUniquePeptides'</li><li>'UniquePeptides'</li><li>'Coverage'</li><li>'MolWeight'</li><li>'LFQ.intensity.gushidai.1'</li><li>'LFQ.intensity.lilongfeng.1'</li><li>'LFQ.intensity.shenli.1'</li><li>'LFQ.intensity.sunxing.1'</li><li>'LFQ.intensity.weizhengshu.1'</li><li>'LFQ.intensity.zhangpingai.1'</li><li>'LFQ.intensity.zhourongchang.1'</li><li>'average.MVI.P'</li><li>'LFQ.intensity.huyunzhen.0'</li><li>'LFQ.intensity.lixiaonan.0'</li><li>'LFQ.intensity.panzhihua.0'</li><li>'LFQ.intensity.qinliqiang.0'</li><li>'LFQ.intensity.xuhuolai.0'</li><li>'LFQ.intensity.yemeitou.0'</li><li>'LFQ.intensity.yuanguiwei.0'</li><li>'LFQ.intensity.zhousanman.0'</li><li>'average.MVI.N'</li><li>'MVI.P.MVI.N'</li><li>'padj'</li><li>'log2FoldChange'</li><li>'regulate'</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 'Protein'\n", "\\item 'Protein.Name'\n", "\\item 'Genename'\n", "\\item 'Function'\n", "\\item 'ProteinGroup'\n", "\\item 'FastaHeaders'\n", "\\item 'Proteins'\n", "\\item 'Peptides'\n", "\\item 'RazorUniquePeptides'\n", "\\item 'UniquePeptides'\n", "\\item 'Coverage'\n", "\\item 'MolWeight'\n", "\\item 'LFQ.intensity.gushidai.1'\n", "\\item 'LFQ.intensity.lilongfeng.1'\n", "\\item 'LFQ.intensity.shenli.1'\n", "\\item 'LFQ.intensity.sunxing.1'\n", "\\item 'LFQ.intensity.weizhengshu.1'\n", "\\item 'LFQ.intensity.zhangpingai.1'\n", "\\item 'LFQ.intensity.zhourongchang.1'\n", "\\item 'average.MVI.P'\n", "\\item 'LFQ.intensity.huyunzhen.0'\n", "\\item 'LFQ.intensity.lixiaonan.0'\n", "\\item 'LFQ.intensity.panzhihua.0'\n", "\\item 'LFQ.intensity.qinliqiang.0'\n", "\\item 'LFQ.intensity.xuhuolai.0'\n", "\\item 'LFQ.intensity.yemeitou.0'\n", "\\item 'LFQ.intensity.yuanguiwei.0'\n", "\\item 'LFQ.intensity.zhousanman.0'\n", "\\item 'average.MVI.N'\n", "\\item 'MVI.P.MVI.N'\n", "\\item 'padj'\n", "\\item 'log2FoldChange'\n", "\\item 'regulate'\n", "\\end{enumerate*}\n"], "text/markdown": ["1. '<PERSON><PERSON>'\n", "2. 'Protein.Name'\n", "3. 'Genename'\n", "4. 'Function'\n", "5. 'ProteinGroup'\n", "6. '<PERSON><PERSON><PERSON><PERSON><PERSON>'\n", "7. '<PERSON>tein<PERSON>'\n", "8. 'Peptides'\n", "9. 'RazorUniquePeptides'\n", "10. 'UniquePeptides'\n", "11. 'Coverage'\n", "12. 'Mo<PERSON>Weight'\n", "13. 'LFQ.intensity.gushidai.1'\n", "14. 'LFQ.intensity.lilongfeng.1'\n", "15. 'LFQ.intensity.shenli.1'\n", "16. 'LFQ.intensity.sunxing.1'\n", "17. 'LFQ.intensity.weizhengshu.1'\n", "18. 'LFQ.intensity.zhangpingai.1'\n", "19. 'LFQ.intensity.zhourongchang.1'\n", "20. 'average.MVI.P'\n", "21. 'LFQ.intensity.huyunzhen.0'\n", "22. 'LFQ.intensity.lixiaonan.0'\n", "23. 'LFQ.intensity.panzhihua.0'\n", "24. 'LFQ.intensity.qinliqiang.0'\n", "25. 'LFQ.intensity.xuhuolai.0'\n", "26. 'LFQ.intensity.yemeitou.0'\n", "27. 'LFQ.intensity.yuanguiwei.0'\n", "28. 'LFQ.intensity.zhousanman.0'\n", "29. 'average.MVI.N'\n", "30. 'MVI.P.MVI.N'\n", "31. 'padj'\n", "32. 'log2FoldChange'\n", "33. 'regulate'\n", "\n", "\n"], "text/plain": [" [1] \"Protein\"                       \"Protein.Name\"                 \n", " [3] \"Genename\"                      \"Function\"                     \n", " [5] \"ProteinGroup\"                  \"FastaHeaders\"                 \n", " [7] \"Proteins\"                      \"Peptides\"                     \n", " [9] \"RazorUniquePeptides\"           \"UniquePeptides\"               \n", "[11] \"Coverage\"                      \"MolWeight\"                    \n", "[13] \"LFQ.intensity.gushidai.1\"      \"LFQ.intensity.lilongfeng.1\"   \n", "[15] \"LFQ.intensity.shenli.1\"        \"LFQ.intensity.sunxing.1\"      \n", "[17] \"LFQ.intensity.weizhengshu.1\"   \"LFQ.intensity.zhangpingai.1\"  \n", "[19] \"LFQ.intensity.zhourongchang.1\" \"average.MVI.P\"                \n", "[21] \"LFQ.intensity.huyunzhen.0\"     \"LFQ.intensity.lixiaonan.0\"    \n", "[23] \"LFQ.intensity.panzhihua.0\"     \"LFQ.intensity.qinliqiang.0\"   \n", "[25] \"LFQ.intensity.xuhuolai.0\"      \"LFQ.intensity.yemeitou.0\"     \n", "[27] \"LFQ.intensity.yuanguiwei.0\"    \"LFQ.intensity.zhousanman.0\"   \n", "[29] \"average.MVI.N\"                 \"MVI.P.MVI.N\"                  \n", "[31] \"padj\"                          \"log2FoldChange\"               \n", "[33] \"regulate\"                     "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"data": {"image/png": "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*********************************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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["#火山图3个类型，我的数据运行成功\n", "# install.packages(\"devtools\") \n", "# devtools::install_github(\"BioSenior/ggvolcano\")\n", "library(ggVolcano) \n", "# data(deg_data)\n", "data <- read.csv(\"D:/R-code/promics_data.csv\")\n", "colnames(data)\n", "\n", "data2 <- add_regulate(data, log2FC_name = \"log2FC\",              \n", "                     fdr_name = \"pvalue\",log2FC = 1, fdr =  0.05) #fdr为p值；这行代码会自动把log2FC和pvalue转换成log2FoldChange和padj\n", "colnames(data2)\n", "# str(data2)\n", "# write.csv(data2, file = \"D:/R-code/data2_export.csv\", row.names = FALSE)\n", "\n", "#图1\n", "p1 <-ggvolcano(data2, x = \"log2FoldChange\", y = \"padj\",     #x和y的名字不需要改，\n", "          #x_lab = \"log2FC\",\n", "          y_lab = \"-log10(P-value)\",  \n", "          log2FC_cut = 1, #FC>2 或 FC<0.5 对应的logFC为1和-1\n", "          FDR_cut = 0.05, #p<0.05\n", "          label = \"Genename\", label_number = 20, output = FALSE)   #只需要改label\n", "p1 + coord_cartesian(xlim = c(-6, 6))\n", "\n", "#图2\n", "library(patchwork)\n", "p2 <- ggvolcano(data2, x = \"log2FoldChange\", y = \"padj\",\n", "                y_lab = \"-log10(P-value)\", \n", "                fills = c(\"#e94234\",\"#b4b4d8\",\"#269846\"),\n", "                colors = c(\"#e94234\",\"#b4b4d8\",\"#269846\"),\n", "                label = \"Genename\", label_number = 20, output = FALSE)\n", "p2 + coord_cartesian(xlim = c(-6, 6))\n", "\n", "#图3\n", "library(RColorBrewer)\n", "library(patchwork)\n", "p3 <- gradual_volcano(data2, x = \"log2FoldChange\", y = \"padj\",\n", "                      y_lab = \"-log10(P-value)\", \n", "                      label = \"Genename\", label_number = 20, output = FALSE)\n", "p4 <-p3 + coord_cartesian(xlim = c(-6, 6))\n", "p4\n", "ggsave(\"D:/R-code/volcano2.pdf\", p4, width = 8, height = 8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["## 我的数据\n", "library(ggplot2)\n", "\n", "# 读取数据：\n", "\n", "data <- read.csv(\"D:/R-code/promics_data.csv\",row.names = 1)\n", "data$label <- c(rownames(data)[1:20],rep(NA,(nrow(data)-20)))\n", "\n", "ggplot(data,aes(log2FC, -log10(pvalue)))+\n", "  # 横向水平参考线：\n", "  geom_hline(yintercept = -log10(0.05), linetype = \"dashed\", color = \"#999999\")+\n", "  # 纵向垂直参考线：\n", "  geom_vline(xintercept = c(-1.2,1.2), linetype = \"dashed\", color = \"#999999\")+\n", "  # 散点图:\n", "  geom_point(aes(size=-log10(pvalue), color= -log10(pvalue)))+\n", "  # 指定颜色渐变模式：\n", "  scale_color_gradientn(values = seq(0,1,0.2),\n", "                        colors = c(\"#39489f\",\"#39bbec\",\"#f9ed36\",\"#f38466\",\"#b81f25\"))+\n", "  # 指定散点大小渐变模式：\n", "  scale_size_continuous(range = c(1,3))+\n", "  # 主题调整：\n", "  theme_bw()+\n", "  # 调整主题和图例位置：\n", "  theme(panel.grid = element_blank(),\n", "        legend.position = c(0.01,0.7),\n", "        legend.justification = c(0,1)\n", "        )+\n", "  # 设置部分图例不显示：\n", "  guides(col = guide_colourbar(title = \"-Log10(p-value)\"),\n", "         size = \"none\")+\n", "  # 添加标签：\n", "  geom_text(aes(label=label, color = -log10(pvalue)), size = 3, vjust = 1.5, hjust=3.5)+\n", "  # 修改坐标轴：\n", "  xlab(\"Log2FC\")+\n", "  ylab(\"-Log10(p-value)\")\n", "\n", "# 保存图片：\n", "ggsave(\"D:/R-code/vocanol_plot.tiff\", height = 9, width = 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["##课程数据\n", "library(ggplot2)\n", "\n", "# 读取数据：\n", "\n", "data <- read.csv(\"D:/dataset/data02.csv\",row.names = 1)\n", "data$label <- c(rownames(data)[1:10],rep(NA,(nrow(data)-10)))\n", "\n", "ggplot(data,aes(log2FoldChange, -log10(padj)))+\n", "  # 横向水平参考线：\n", "  geom_hline(yintercept = -log10(0.05), linetype = \"dashed\", color = \"#999999\")+\n", "  # 纵向垂直参考线：\n", "  geom_vline(xintercept = c(-1.2,1.2), linetype = \"dashed\", color = \"#999999\")+\n", "  # 散点图:\n", "  geom_point(aes(size=-log10(padj), color= -log10(padj)))+\n", "  # 指定颜色渐变模式：\n", "  scale_color_gradientn(values = seq(0,1,0.2),\n", "                        colors = c(\"#39489f\",\"#39bbec\",\"#f9ed36\",\"#f38466\",\"#b81f25\"))+\n", "  # 指定散点大小渐变模式：\n", "  scale_size_continuous(range = c(1,3))+\n", "  # 主题调整：\n", "  theme_bw()+\n", "  # 调整主题和图例位置：\n", "  theme(panel.grid = element_blank(),\n", "        legend.position = c(0.01,0.7),\n", "        legend.justification = c(0,1)\n", "        )+\n", "  # 设置部分图例不显示：\n", "  guides(col = guide_colourbar(title = \"-Log10(p-value)\"),\n", "         size = \"none\")+\n", "  # 添加标签：\n", "  geom_text(aes(label=label, color = -log10(padj)), size = 3, vjust = 1.5, hjust=1)+\n", "  # 修改坐标轴：\n", "  xlab(\"Log2FC\")+\n", "  ylab(\"-Log10(p-value)\")\n", "\n", "# 保存图片：\n", "# ggsave(\"vocanol_plot.pdf\", height = 9, width = 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# 安装并加载 readxl 包\n", "# install.packages(\"readxl\")\n", "library(readxl)\n", "\n", "# # 读取Excel文件\n", "data <- read_excel(\"D:/R-code/huoshantu/promics_data.xlsx\")\n", "colnames(data)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#蛋白质组学数据   火山图\n", "data <- read.csv(\"D:/R-code/promics_data.csv\") #\"row.names = 1\"表示将第一列作为数据框的行名。\n", "colnames(data)\n", "\n", "color <- rep(\"#999999\",nrow(data))\n", "color[data$pvalue <0.05 & data$log2FC > 1] <- \"#FC4E07\"\n", "color[data$pvalue <0.05 & data$log2FC < -1] <- \"#00AFBB\"\n", "\n", "par(oma = c(0,2,0,0))\n", "\n", "plot(data$log2FC,-log10(data$pvalue),pch = 16,cex = 0.5, \n", "     xlim = c(-5,5), ylim = c(0,4), col = color, frame.plot = F,\n", "     xlab = \"log2FC\", ylab = \"-log10(P-value)\", cex.axis = 1, cex.lab = 1.3)\n", "   \n", "# 添加参考线：\n", "abline(h = -log10(0.05),lwd = 2, lty = 3)  # lwd设置线的宽度，lty设置线的类型；\n", "abline(v = c(-1,1),lwd = 2, lty = 3)  # lwd设置线的宽度，lty设置线的类型；\n", "\n", "# 添加图例\n", "legend(x = 3, y = 4, legend = c(\"Up\",\"Normal\",\"Down\"), \n", "       bty = \"n\", # 去除边框\n", "       pch = 19,cex = 1, # 设置点的样式和大小\n", "       x.intersp = 0.6, # 设置字与点之间的距离；\n", "       y.intersp = 0.6, # 设置点与点的高度差，相当于行距；\n", "       col = c(\"#FC4E07\",\"#999999\",\"#00AFBB\"))\n", "\n", "# 添加标签：\n", "color = c()\n", "color[which(data[1:20,]$regulate == \"Up\")] = \"#FC4E07\"\n", "color[which(data[1:20,]$regulate == \"Down\")] = \"#00AFBB\"\n", "\n", "text(data$log2FC[1:20],-log10(data$pvalue)[1:20],\n", "     labels = data$Genename[1:20],\n", "     adj = c(0,1.5),\n", "     cex = 0.6,\n", "     col = color)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["\n", "#课程数据1\n", "data <- read.csv(\"D:/R-code/huoshantu/DEG.csv\")#row.names = 1\n", "\n", "color <- rep(\"#999999\",nrow(data))\n", "\n", "color[data$pvalue <0.05 & data$log2FoldChange > 1] <- \"#FC4E07\"\n", "color[data$pvalue <0.05 & data$log2FoldChange < -1] <- \"#00AFBB\"\n", "\n", "par(oma = c(0,2,0,0))\n", "\n", "plot(data$log2FoldChange,-log10(data$pvalue),pch = 16,cex = 0.5,\n", "     xlim = c(-4,4), ylim = c(0,32), col = color, frame.plot = F,\n", "     xlab = \"log2FC\", ylab = \"-log10(Pvalue)\", cex.axis = 1, cex.lab = 1.3)\n", "\n", "# 添加参考线：\n", "abline(h = -log10(0.05),lwd = 2, lty = 3)  # lwd设置线的宽度，lty设置线的类型；\n", "abline(v = c(-1,1),lwd = 2, lty = 3)  # lwd设置线的宽度，lty设置线的类型；\n", "\n", "# 添加图例\n", "legend(x = 3, y = 32, legend = c(\"Up\",\"Normal\",\"Down\"), \n", "       bty = \"n\", # 去除边框\n", "       pch = 19,cex = 1, # 设置点的样式和大小\n", "       x.intersp = 0.6, # 设置字与点之间的距离；\n", "       y.intersp = 0.6, # 设置点与点的高度差，相当于行距；\n", "       col = c(\"#FC4E07\",\"#999999\",\"#00AFBB\"))\n", "\n", "# 添加标签：\n", "color = c()\n", "color[which(data[1:10,]$regulate == \"Up\")] = \"#FC4E07\"\n", "color[which(data[1:10,]$regulate != \"Up\")] = \"#00AFBB\"\n", "text(data$log2FoldChange[1:10],-log10(data$pvalue)[1:10],\n", "     labels = data$row[1:10],\n", "     adj = c(0,1.5),\n", "     cex = 0.6,\n", "     col = color)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# 包装函数：课程数据2\n", "# 调整1: xlim和ylim得去掉\n", "# 调整2: 修改图例的位置\n", "plotVoc <- function(data){\n", "  color <- rep(\"#999999\",nrow(data))\n", "  \n", "  color[data$pvalue <0.05 & data$log2FoldChange > 1] <- \"#FC4E07\"\n", "  color[data$pvalue <0.05 & data$log2FoldChange < -1] <- \"#00AFBB\"\n", "  \n", "  par(oma = c(0,2,0,0))\n", "  \n", "  plot(data$log2FoldChange,-log10(data$pvalue),pch = 16,cex = 0.5,\n", "       col = color, frame.plot = F,\n", "       xlab = \"log2FC\", ylab = \"-log10(Pvalue)\", cex.axis = 1, cex.lab = 1.3)\n", "  \n", "  # 添加参考线：\n", "  abline(h = -log10(0.05),lwd = 2, lty = 3)  # lwd设置线的宽度，lty设置线的类型；\n", "  abline(v = c(-1,1),lwd = 2, lty = 3)  # lwd设置线的宽度，lty设置线的类型；\n", "  \n", "  # 添加图例\n", "  legend(x = 3, y = max(-log10(data$pvalue)), legend = c(\"Up\",\"Normal\",\"Down\"), \n", "         bty = \"n\", # 去除边框\n", "         pch = 19,cex = 1, # 设置点的样式和大小\n", "         x.intersp = 0.6, # 设置字与点之间的距离；\n", "         y.intersp = 0.6, # 设置点与点的高度差，相当于行距；\n", "         col = c(\"#999999\", \"#FC4E07\",\"#00AFBB\"))\n", "  \n", "  # 添加标签：\n", "  color = c()\n", "  color[which(data[1:10,]$regulate == \"Up\")] = \"#FC4E07\"\n", "  color[which(data[1:10,]$regulate != \"Up\")] = \"#00AFBB\"\n", "  text(data$log2FoldChange[1:10],-log10(data$pvalue)[1:10],\n", "       labels = data$row[1:10],\n", "       adj = c(0,1.5),\n", "       cex = 0.6,\n", "       col = color)\n", "}\n", "\n", "data <- read.csv(\"D:/R-code/huoshantu/DEG2.csv\",row.names = 1)\n", "\n", "plotVoc(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#火山图3个类型，运行成功\n", "# install.packages(\"devtools\") \n", "# devtools::install_github(\"BioSenior/ggvolcano\")\n", "library(ggVolcano) \n", "# data(deg_data)\n", "data <- read.csv(\"D:/R-code/promics_data.csv\")\n", "colnames(data)\n", "\n", "data2 <- add_regulate(data, log2FC_name = \"log2FC\",              \n", "                     fdr_name = \"pvalue\",log2FC = 1, fdr =  0.05) #fdr为p值；这行代码会自动把log2FC和pvalue转换成log2FoldChange和padj\n", "colnames(data2)\n", "# str(data2)\n", "# write.csv(data2, file = \"D:/R-code/data2_export.csv\", row.names = FALSE)\n", "\n", "#图1\n", "p1 <-ggvolcano(data2, x = \"log2FoldChange\", y = \"padj\",     #x和y的名字不需要改，\n", "          #x_lab = \"log2FC\",\n", "          y_lab = \"-log10(P-value)\",  \n", "          log2FC_cut = 1, #FC>2 或 FC<0.5 对应的logFC为1和-1\n", "          FDR_cut = 0.05, #p<0.05\n", "          label = \"Genename\", label_number = 20, output = FALSE)   #只需要改label\n", "p1 + coord_cartesian(xlim = c(-6, 6))\n", "\n", "#图2\n", "library(patchwork)\n", "p2 <- ggvolcano(data2, x = \"log2FoldChange\", y = \"padj\",\n", "                fills = c(\"#e94234\",\"#b4b4d8\",\"#269846\"),\n", "                colors = c(\"#e94234\",\"#b4b4d8\",\"#269846\"),\n", "                label = \"Genename\", label_number = 20, output = FALSE)\n", "p2 + coord_cartesian(xlim = c(-6, 6))\n", "\n", "#图3\n", "library(RColorBrewer)\n", "library(patchwork)\n", "p3 <- gradual_volcano(data2, x = \"log2FoldChange\", y = \"padj\",\n", "                      label = \"Genename\", label_number = 20, output = FALSE)\n", "p3 + coord_cartesian(xlim = c(-6, 6))"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}