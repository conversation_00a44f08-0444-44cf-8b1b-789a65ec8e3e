################## 基础树状图 ######################
# 载入R包
library(tidyverse)
# 数据
head(mtcars)
# 使用三个变量来聚类：
mtcars %>%
select(mpg, cyl, disp) %>%
dist() %>%  # 计算距离矩阵
hclust() %>%   # 根据距离矩阵聚类
as.dendrogram() -> dend  # 构建图对象
# Plot
pdf("clust1.pdf", height = 7, width = 7)
par(mar=c(7,3,1,1))  # Increase bottom margin to have the complete label
plot(dend)
dev.off()
dev.off()
# Plot
pdf("clust1.pdf", height = 7, width = 7)
par(mar=c(7,3,1,1))  # Increase bottom margin to have the complete label
plot(dend)
dev.off()
# Plot
pdf("clust1.pdf", height = 7, width = 7)
par(mar=c(7,3,1,1))  # Increase bottom margin to have the complete label
plot(dend)
dev.off()
par(mar=c(7,3,1,1))  # Increase bottom margin to have the complete label
plot(dend)
dev.off()
# Plot
pdf("clust1.pdf", height = 7, width = 7)
par(mar=c(7,3,1,1))  # Increase bottom margin to have the complete label
plot(dend)
dev.off()
# Plot
pdf("clust1.pdf", height = 7, width = 7)
par(mar=c(7,3,1,1))  # Increase bottom margin to have the complete label
plot(dend)
dev.off()
10*9*8*7
10*9*8*7*6*5
