{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["#######柱形图  我的数据 已成功\n", "library(ggplot2)\n", "\n", "#读取作图数据，这是一组上调基因的 GO 富集结果，展示了 top 富集的条目\n", "library(readxl)\n", "\n", "# 读取整个Excel文件\n", "go_enrich <- read_excel(\"D:/R-code/GO2.xlsx\") #要取前20个\n", "\n", "#按 p 值由低到高排个序\n", "go_enrich <- go_enrich[order(go_enrich$Category, go_enrich$pvalue, decreasing =  FALSE), ]\n", "go_enrich$Term <- factor(go_enrich$Term, levels = go_enrich$Term)\n", "\n", "\n", "#柱形图，横坐标 GO Term，纵坐标是 p 值的对数转换，颜色按 Category 着色\n", "p1 <- ggplot(go_enrich, aes(Term, count)) +\n", "geom_col(aes(fill = Category), width = 0.5) +\n", "scale_fill_manual(values = c('#8EA1CB', '#67C1A5', '#FA8E61')) +\n", "theme(panel.grid = element_blank(), panel.background = element_rect(color = 'black', fill = 'transparent'), \n", "\taxis.text.x = element_text(angle = 70, hjust = 1, vjust = 1)) +\n", "scale_y_continuous(expand = expansion(mult = c(0, 0.1))) + \n", "labs(x = '', y = 'Protein number\\n') +\n", "labs(title = \"Enriched GO Terms (Top20)\")\n", "# 设置标题居中显示\n", "p1 + theme(plot.title = element_text(hjust = 0.5))\n", "\n", "p2 <- p1 + \n", "  facet_grid(.~Category, scale = 'free_x', space = 'free_x') +\n", "  theme(legend.position = 'none')+ theme(plot.title = element_text(hjust = 0.5))\n", "p2 \n", "ggsave(\"D:/R-code/facet_plot2.pdf\", p2, width = 8, height = 8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#install.packages(\"cols4all\") 安装报错用下面的\n", "\n", "# 安装zip包\n", "install.packages(\"D:/data/cols4all_0.7-1.zip\", repos = NULL, type = \"win.binary\")\n", "\n", "# 或者从URL安装zip包\n", "# install.packages(\"https://example.com/your/package.zip\", repos = NULL, type = \"win.binary\")\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["── \u001b[1mAttaching core tidyverse packages\u001b[22m ──────────────────────── tidyverse 2.0.0 ──\n", "\u001b[32m✔\u001b[39m \u001b[34mdplyr    \u001b[39m 1.1.4     \u001b[32m✔\u001b[39m \u001b[34mreadr    \u001b[39m 2.1.5\n", "\u001b[32m✔\u001b[39m \u001b[34mforcats  \u001b[39m 1.0.0     \u001b[32m✔\u001b[39m \u001b[34mstringr  \u001b[39m 1.5.1\n", "\u001b[32m✔\u001b[39m \u001b[34mlubridate\u001b[39m 1.9.3     \u001b[32m✔\u001b[39m \u001b[34mtibble   \u001b[39m 3.2.1\n", "\u001b[32m✔\u001b[39m \u001b[34mpurrr    \u001b[39m 1.0.2     \u001b[32m✔\u001b[39m \u001b[34mtidyr    \u001b[39m 1.3.1\n", "── \u001b[1mConflicts\u001b[22m ────────────────────────────────────────── tidyverse_conflicts() ──\n", "\u001b[31m✖\u001b[39m \u001b[34mdplyr\u001b[39m::\u001b[32mfilter()\u001b[39m masks \u001b[34mstats\u001b[39m::filter()\n", "\u001b[31m✖\u001b[39m \u001b[34mdplyr\u001b[39m::\u001b[32mlag()\u001b[39m    masks \u001b[34mstats\u001b[39m::lag()\n", "\u001b[36mℹ\u001b[39m Use the conflicted package (\u001b[3m\u001b[34m<http://conflicted.r-lib.org/>\u001b[39m\u001b[23m) to force all conflicts to become errors\n", "Warning message:\n", "\"\u001b[1m\u001b[22mThe `size` argument of `element_line()` is deprecated as of ggplot2 3.4.0.\n", "\u001b[36mℹ\u001b[39m Please use the `linewidth` argument instead.\"\n"]}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0gAAANICAMAAADKOT/pAAAAZlBMVEUAAAAAv8QzMzNNTU1oaGh8fHyDg4OMjIyVlZWZmZmampqjo6Onp6evr6+ysrK5ubm9vb3BwcHHx8fJycnQ0NDR0dHY2NjZ2dne3t7h4eHk5OTp6enq6urv7+/w8PD19fX4dm3///+PZXIwAAAACXBIWXMAABJ0AAASdAHeZh94AAAa6UlEQVR4nO3dDZNbVXZGYWnkNsT0gJmBgMdgcv//n4zVwlhW23jfuftovUdaT1USKqliVO/ZK7jbH+wWSZvt6A8g3QJDkhoYktTAkKQGhiQ1MCSpgSFJDQxJamBIUgNDkhoYktTAkKQGhhRtL8y6lzKkaPQx3bN1L2VI0ehjumfrXsqQotHHdM/WvZQhRaOP6Z6teylDikYf0z1b91KGFI0+pnu27qUMKRp9TPds3UsZUjT6mO7ZupcypGj0Md2zdS9lSNHoY7pn617KkKLRx3TP1r2UIUWjj+merXspQ4pGH9M9W/dShhSNPqZ7tu6lDCkafUz3bN1LGVI0+pju2bqXMqRo9DHds3UvZUjR6GO6Z+teypCi0cd0z9a9lCFFo4/pnq17KUOKRh/TPVv3UoYUjT6me7bupQwpGn1M92zdSxlSNPqY7tm6lzKkaPQx3bN1L2VI0ehjumfrXsqQotHHdM/WvZQhRaOP6Z6teylDkhoYktTAkKQGhhQN/SJhEvQbnRhSNPpIZ0C/0YkhRaOPdAb0G50YUjT6SGdAv9GJIUWjj3QG9BudGFI0+khnQL/RiSFFo490BvQbnRhSNPpIZ0C/0YkhRaOPdAb0G50YUjT6SGdAv9GJIUWjj3QG9BudGFI0+khnQL/RiSFFo490BvQbnRhSNPpIZ0C/0YkhRaOPdAb0G50YUjT6SGdAv9GJIUWjj3QG9BudGFI0+khnQL/RiSFFo490BvQbnRhSNPpIZ0C/0YkhRaOPdAb0G50YUjT6SGdAv9GJIUWjj3QG9BudGFI0+khnQL/RiSFFo490BvQbnRhSNPpIZ0C/0YkhRaOPdAb0G50YUjT6SGdAv9GJIUWjj3QG9BudGJLUwJCkBoYkNTCkaOgXHwnoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzpGj0HePoBygzJKmBIUkNDElqYEjR0K9ProUeuYUhRaNv/CrokVsYUjT6xq+CHrmFIUWjb/wq6JFbGFI0+savgh65hSFFo2/8KuiRWxhSNPrGr4IeuYUhRaNv/CrokVsYUjT6xq+CHrmFIUWjb/wq6JFbGFI0+savgh65hSFFo2/8KuiRWxhSNPrGr4IeuYUhRaNv/CrokVsYUjT6xq+CHrmFIUWjb/wq6JFbGFI0+savgh65hSFFo2/8KuiRWxhSNPrGr4IeuYUhRaNv/CrokVsYUjT6xq+CHrmFIUWjb/wq6JFbGFI0+savgh65hSFFo2/8KuiRWxhSNPrGr4IeuYUhRaNv/CrokVsYUjT6xq+CHrmFIUWjb/wq6JFbGFI0+savgh65hSFJDQxJamBIUgNDioZ+7TIaPW4rQ4pG3/pQ9LitDCkafetD0eO2MqRo9K0PRY/bypCi0bc+FD1uK0OKRt/6UPS4rQwpGn3rQ9HjtjKkaPStD0WP28qQotG3PhQ9bitDikbf+lD0uK0MKRp960PR47YypGj0rQ9Fj9vKkKLRtz4UPW4rQ4pG3/pQ9LitDCkafetD0eO2MqRo9K0PRY/bypCi0bc+FD1uK0OKRt/6UPS4rQwpGn3rQ9HjtjKkaPStD0WP28qQotG3PhQ9bitDikbf+lD0uK0MKRp960PR47YypGj0rQ9Fj9vKkKLRtz4UPW4rQ4pG3/pQ9LitDCkafetD0eO2MqRo9K0PRY/bypCi0bc+FD1uK0OSGhiS1MCQpAaGFA39GmYketh2hhSNvvdh6GHbGVI0+t6HoYdtZ0jR6Hsfhh62nSFFo+99GHrYdoYUjb73Yehh2xlSNPreh6GHbWdI0eh7H4Yetp0hRaPvfRh62HaGFI2+92HoYdsZUjT63oehh21nSNHoex+GHradIUWj730Yeth2hhSNvvdh6GHbGVI0+t6HoYdtZ0jR6Hsfhh62nSFFo+99GHrYdoYUjb73Yehh2xlSNPreh6GHbWdI0eh7H4Yetp0hRaPvfRh62HaGFI2+92HoYdsZUjT63oehh21nSNHoex+GHradIUWj730Yeth2hhSNvvdh6GHbGVI0+t6HoYdtZ0jR6Hsfhh62nSFFo+99GHrYdoYkNTAkqYEhSQ0MKRr6dcxA9K79DCkaffCj0Lv2M6Ro9MGPQu/az5Ci0Qc/Cr1rP0OKRh/8KPSu/QwpGn3wo9C79jOkaPTBj0Lv2s+QotEHPwq9az9DikYf/Cj0rv0MKRp98KPQu/YzpGj0wY9C79rPkKLRBz8KvWs/Q4pGH/wo9K79DCkaffCj0Lv2M6Ro9MGPQu/az5Ci0Qc/Cr1rP0OKRh/8KPSu/QwpGn3wo9C79jOkaPTBj0Lv2s+QotEHPwq9az9DikYf/Cj0rv0MKRp98KPQu/YzpGj0wY9C79rPkKLRBz8KvWs/Q4pGH/wo9K79DCkaffCj0Lv2M6Ro9MGPQu/az5Ci0Qc/Cr1rP0OSGhiS1MCQpAaGJDUwpGjkNwR60UuOZkjR6PPvQy85miFFo8+/D73kaIYUjT7/PvSSoxlSNPr8+9BLjmZI0ejz70MvOZohRaPPvw+95GiGFI0+/z70kqMZUjT6/PvQS45mSNHo8+9DLzmaIUWjz78PveRohhSNPv8+9JKjGVI0+vz70EuOZkjR6PPvQy85miFFo8+/D73kaIYUjT7/PvSSoxlSNPr8+9BLjmZI0ejz70MvOZohRaPPvw+95GiGFI0+/z70kqMZUjT6/PvQS45mSNHo8+9DLzmaIUWjz78PveRohhSNPv8+9JKjGVI0+vz70EuOZkjR6PPvQy85miFFo8+/D73kaIYUjT7/PvSSoxmS1MCQpAaGJDUwpGjolzWr0WuRDCkancY69FokQ4pGp7EOvRbJkKLRaaxDr0UypGh0GuvQa5EMKRqdxjr0WiRDikansQ69FsmQotFprEOvRTKkaHQa69BrkQwpGp3GOvRaJEOKRqexDr0WyZCi0WmsQ69FMqRodBrr0GuRDCkancY69FokQ4pGp7EOvRbJkKLRaaxDr0UypGh0GuvQa5EMKRqdxjr0WiRDikansQ69FsmQotFprEOvRTKkaHQa69BrkQwpGp3GOvRaJEOKRqexDr0WyZCi0WmsQ69FMqRodBrr0GuRDCkancY69FokQ4pGp7EOvRbJkKLRaaxDr0UypGh0GuvQa5EMSWpgSFIDQ5IaGFI09EueT9FThDOkaHQ9Z+gpwhlSNLqeM/QU4QwpGl3PGXqKcIYUja7nDD1FOEOKRtdzhp4inCFFo+s5Q08RzpCi0fWcoacIZ0jR6HrO0FOEM6RodD1n6CnCGVI0up4z9BThDCkaXc8ZeopwhhSNrucMPUU4Q4pG13OGniKcIUWj6zlDTxHOkKLR9ZyhpwhnSNHoes7QU4QzpGh0PWfoKcIZUjS6njP0FOEMKRpdzxl6inCGFI2u5ww9RThDikbXc4aeIpwhRaPrOUNPEc6QotH1nKGnCGdI0eh6ztBThDOkaHQ9Z+gpwhlSNLqeM/QU4QwpGl3PGXqKcIYUja7nDD1FOEOSGhiS1MCQpAaGFA39quiIHmAahhSN7mhPDzANQ4pGd7SnB5iGIUWjO9rTA0zDkKLRHe3pAaZhSNHojvb0ANMwpGh0R3t6gGkYUjS6oz09wDQMKRrd0Z4eYBqGFI3uaE8PMA1DikZ3tKcHmIYhRaM72tMDTMOQotEd7ekBpmFI0eiO9vQA0zCkaHRHe3qAaRhSNLqjPT3ANAwpGt3Rnh5gGoYUje5oTw8wjcuQHg+7E+TT6ALd0Z4eYBoXwTzudoYUhO5oTw8wjYtgDrvXzOfQZ9Ed7ekBpnERkv8kykJ3tKcHmMZFON/s3jGfQ59Fd7SnB5jGRUhvDw9vmQ+iz6E72tMDTOPZD+38ZkMSuqM9PcA0DCka3dGeHmAaBhON7mhPDzANQ4pGd7SnB5jGZUjvHl/sdi8e/d5dBrqjPT3ANJ591+70FdLB791JK1yE9Gp3/Pb324fdK+bjSHP6wq9s8Lt20hqGFM0vjWbhD+2iGdIs/GZDNEOahd/+jmZIs/BroWiGNAtDimZIszgPabfzF62GMaRZGFI0Q5qFwUQzpFkYUjRDmsUXfmXD4XD9j6LnDGkW5yEddju/RspiSLM4D+b1WUf+8XYRDGkW/rl20QxpFoYTzZBm8dlfa/fwPfJZ9IwhzcJf/R3NkGZxEdKDvx8piiHN4vPfbHjnNx0yGNIsvvCH6O8egM+iZwxpFpf/5Hn18Ob4Qzv/KP0MhjSLL/7Z3/7qhgSGNAtDimZIszCWaIY0C0OKZkiz8HfIRjOkWRhSNEOahcFEM6RZXP4SIX9pUBRDmsVFSAf/CSX9Fy7CefPw6K9pkFbz32ouNegK6f80Qts7a7Cuf/LQF3ejml5HwxlStKbX0XCXIX3z9L/YvVj7LQf64m5UzyNrvIuQHk9fHO1W/1Zz+uJuVNMra7hnP4/0y/F/vPGbDRmaXlnDdf1bzemLu1Etb6wrePZnNrx6d/zD7Vb/mQ30xd2otnfWYF/6c+3erPz70Bd3o9reWYN94d9qvvrXCdEXd6OaXlnD+fNI0ZpeR8MZUrSm19FwlyG9PizLL7vD6j9Fn764G9X0yhruIqTXu93pGw5rS6Iv7ka1vbMGuwjpxe6X9//1+s1u7b9Dlr64G9X2zhrs+U/I/rR74U/Ipmh7Zw327JcIvX21e3P8Kmnl34e+uBvV9s4a7CKk748/GXv8B9Ljyr8PfXE3qu2dNdjlD+Eed4ef3v+DaW1HhjRG0ytrOH8eKVrT62g4Q4rW9Doa7llIr7/Z7ZaHtb9m1ZDG6HlkjXcR0rsXT3+C0O70+/tWoC/uRrW9swa7COnV7vH4c0j/8vcjZWh7Zw32md8h++G/VqEv7ka1vbMGM6Robe+swT7/Q7tH/xShDG3vrMEuv9nw4bea++faRWh7Zw327Idw3z/9VvN3a/8+9MXdqJ5H1nj+WyekBoYkNfg0pHffP7z/Aumb19CHkWb1SUg//fmtht1h7S9s+Dv01xkza3wGDXUe0tvd7tXxF9n98s1ut/q7DV9GH+PM+l5BY52H9PFnj16t/o19f4M+xpn1vYLGOg/psPvws0dvj39uQxf6GGfW9woa6zyks18X1PkvY6aPcWZ9r6CxDCla3ytoLEOK1vcKGsuQovW9gsb6NKQzff8R9DHOrO8VNJYhRet7BY11hV9rRx/jzMa/jnoYUrTxr6MehhRt/OuohyFFG/866mFI0ca/jnoYUrTxr6MehhRt/OuohyFFG/866mFI0ca/jnoYUrTxr6MehhRt/OuohyFFG/866mFI0ca/jnoYUrTxr6MehhRt/OuohyFFG/866mFI0ca/jnoYUrTxr6MehhRt/Ouoh/9aF6mBIUkNDElqEB0S/RUKj34BVRlSNPoFVGVI0egXUJUhRaNfQFWGFI1+AVUZUjT6BVRlSNHoF1CVIUWjX0BVhhSNfgFVGVI0+gVUZUjR6BdQlSFFo19AVYYUjX4BVRlSNPoFVGVI0egXUJUhRaNfQFWGFI1+AVUZUjT6BVRlSNHoF1CVIUWjX0BVhhSNfgFVGVI0+gVUZUjR6BdQlSFFo19AVYYUjX4BVRlSNPoFVGVI0egXUJUhRaNfQFWGFI1+AVVFhyTNwpCkBoYkNbilkOgvaAagJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1WVIUWjJ1XVLYUkYQxJamBIUgNDkhrcUkj0dwYGoCdVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydVlSFFoydV1S2FJGEMSWpgSFKDWwuJ/qKmGT2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqgwpGj2nqm4tJAlhSFIDQ5Ia3GpI9Bc3TegZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVWVI0egZVXWrIUlXZUhSA0OSGtxuSPSXNy3oEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVVlSNHoEVV1uyFJV2RIUgNDkhrcQ0j0Fzob0NOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOpypCi0dOp6h5CkoYzJKmBIUkN7iEk+gudDejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVGVI0ejpVHUPIUnDGZLUwJCkBoa0JH8RRS+jKkNaDEnbGdJiSNrOkBZD0naGtBiStjOkxZC0nSEthqTtDGkxJG1nSIshaTtDWgxJ2xnSYkjazpAWQ9J2hrQYkrYzpMWQtJ0hLYak7QxpMSRtZ0iLIWk7Q1oMSdsZ0mJI2s6QFkPSdoa0GJK2M6TFkLSdIS2GpO0MaTEkbWdIiyFpO0NaDEnbGdJiSNrOkBZD0naGtBiStjMkqYEhSQ0MSWpgSItfI2k7Q1oMSdsZ0mJI2s6QFkPSdoa0GJK2M6TFkLSdIS2GpO0MaTEkbWdIiyFpO0NaDEnbGdJiSNrOkBZD0naGtBiStjOkxZC0nSEthqTtDGkxJG1nSIshaTtDWgxJ2xnSYkjazpAWQ9J2hrQYkrYzpMWQtJ0hLYak7QxpMSRtZ0iLIWk7Q1oMSdsZ0mJI2s6QFkPSdoYkNTAkqYEhSQ0MSWpQCel/JH1OTEj/GPj3/m/4eb4i7QPBn8eQvsDP8xVpH2iqkAb6B/sf/4yf5yvSPlDM5zGkT/h5viLtA8V8HkP6hJ/nK9I+UMzn8dvfUgNDkhoYktTAkKQGhiQ1oEP6+dvDyx//gD/EJ34+0J/ggx9fpm0TNM5R0vHAIf14OHoZMsbRb4eUW/nuaZtv6Y9xLmeco6jjYUP67fDDH8f/N/cD+inO/fYy5Vb+c3j52/Hj/If+IB/ljHOUdTxsSP88vUvO8/x8+C7lw/x4+PX9f//fw7/pD/KXoHGOso6H/hrpScgW7x1+jPkw/zz8vhz/3+4/6Q/yl6BxzoR8pISQ/jh8R3+ED36LeZgPnyPl4yxR43yUcjwJIf389IOYFCm3khfSkvZpjlKOJyCk31/m/OhlybkVQ6qIOR4+pD9eZvyz+YOUWzGkgpzjYUJ6+gmAP9/ku4CfKTn/PCm38tKQvi7heE7okH7/9rvfkY/wicSQTt+1+z3ou3ZLzjgnGcdzAv/Q7teQ77mcSbmVfz99Ff3r4Uf6g5xLGedJ1PGwIf2eNMWfUm4l8Fc25IxzlHU8bEg/HA7nP6qKEPNhvn1aJulYgsZZ0o6HDekQtcVJzIf54+lXf9Of4lMx4yxpx8N/+1u6AYYkNTAkqYEhSQ0MSWpgSFIDQ5IaGJLUwJCkBoYkNTCkZLu/fZ7dyas31/o4+jJDSlYKabezJJ4hJftaSE//43H3cJUPo79jSMnOQnr76v0P4t4+/dXD7sVPx//Lh//r3+emq/ANkn1M5N3h+GO4w7sPf2VIYXyDZB8Tefrx28PucVm+f/9X7x4+hvTu1e4V9wn1J0NK9jGkF7v3P6x7u3vx11/tzr7Z8Jb8jHpiSMk+hnT6q4//HPoY0sFvfycwpGRfC4n6XHrGt0j2tR/akZ9Nn/Atkn3umw1//pUhZfEtkn34bsLff/tbAXyLZB9DuvgJ2X8ZUhjfYk67jD/NTR8Y0mx2u1+OXyj5k7BZDGk2j/4kbCJDms7rFx++WlIOQ5IaGJLUwJCkBoYkNTAkqYEhSQ0MSWpgSFKD/wcaiZtgl2cTNQAAAABJRU5ErkJggg==", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["#双向柱状图  已经成功\n", "#相关R包载入：\n", "library(tidyverse)\n", "library(ggplot2)\n", "library(cols4all)\n", "library(patchwork)\n", "library(readxl)\n", "#测试数据读入：\n", "##将上下调数据通过正负区分  大家可以分别使用上调和下调基因完成富集，将下调基因富集到的通路p值标注为负数，和上调做区分\n", "# dt <- read.csv('testdt.csv',header = T) \n", "\n", "# 读取整个Excel文件\n", "dt <- read_excel(\"D:/R-code/KEGG双向柱状图数据.xlsx\") \n", "# head(dt)\n", "\n", "dt$group <- case_when(dt$LogP > 0 ~ 'up',\n", "dt$LogP < 0 ~ 'down')\n", "# head(dt)\n", "\n", "dt$Description <- factor(dt$Description,levels = rev(dt$Description))\n", "\n", "#基础版上下调富集柱形图绘制：\n", "p <- ggplot(dt,\n", "aes(x =LogP, y = Description, fill = group)) + #数据映射\n", "geom_col() + #绘制添加条形图\n", "theme_bw()\n", "p\n", "\n", "#自定义主题调整：\n", "mytheme <- theme(\n", "legend.position = 'none',\n", "axis.text.y = element_blank(),\n", "axis.ticks.y = element_blank(),\n", "panel.grid.major = element_blank(),\n", "panel.grid.minor = element_blank(),\n", "panel.border = element_blank(),\n", "axis.line.x = element_line(color = 'grey60',size = 1.1),\n", "axis.text = element_text(size = 12)\n", ")\n", "p1 <- p + mytheme\n", "p1\n", "\n", "\n", "#先根据上下调标签拆分数据框：\n", "up <- dt[which(dt$LogP > 0),]\n", "down <- dt[which(dt$LogP < 0),]\n", "#添加上调pathway标签：\n", "p2 <- p1 +\n", "geom_text(data = up,\n", "aes(x = -0.2, y = Description, label = Description),\n", "size = 3.5,\n", "hjust = 1) #标签右对齐\n", "p2\n", "\n", "#添加下调pathway标签：\n", "p3 <- p2 +\n", "geom_text(data = down,\n", "aes(x = 0.2, y = Description, label = Description),\n", "size = 3.5,\n", "hjust = 0) #标签左对齐\n", "p3\n", "\n", "#继续调整细节：\n", "p4 <- p3 +\n", "scale_x_continuous(breaks=seq(-4, 6,1)) + #x轴刻度修改\n", "labs(x = '-log10(P value)', y = 'Enriched KEGG Pathway', title = '') + #修改x/y轴标签、标题添加\n", "theme(plot.title = element_text(hjust = 0.5, size = 14)) #主标题居中、字号调整\n", "p4\n", "\n", "# 将上调改为红色，下调改为蓝色\n", "p4 <- p3 +\n", "  scale_x_continuous(breaks = seq(-4, 6, 1)) +\n", "  labs(x = '-log10(P value)', y = 'Enriched KEGG Pathway', title = '') +\n", "  theme(plot.title = element_text(hjust = 0.5, size = 14)) +\n", "  scale_fill_manual(values = c( \"#55a8e3\",\"#f4083f\")) # 设置柱状图的填充颜色为红色和蓝色\n", "\n", "p4\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["\n", "ggsave(\"D:/R-code/双向柱图.pdf\", p4, width = 7, height = 6)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# install.packages(c(\"shinyjs\", \"kableExtra\", \"colorblindcheck\"))\n", "# 加载程序包\n", "# library(kableExtra)\n", "# library(shinyjs)\n", "# library(colorblindcheck)\n", "#颜色修改：\n", "c4a_gui()\n", "mycol <- c4a('bright',2)\n", "mycol\n", "p5 <- p4 +\n", "scale_fill_manual(values = mycol)\n", "p5\n", "\n", "#最后添加上下调提示标签：\n", "p6 <- p5 +\n", "geom_text(x = 6, y = 14, label = \"Up\", size = 6, color = '#EE6677') +\n", "geom_text(x = -4.5, y = 3.5, label = \"Down\", size = 6, color = '#4477AA')\n", "p6\n", "# ggsave(\"D:/R-code/facet_plot2.pdf\", p6, width = 6, height = 8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#######柱形图\n", "library(ggplot2)\n", "\n", "#读取作图数据，这是一组上调基因的 GO 富集结果，展示了 top 富集的条目\n", "go_enrich <- read.delim('K:/2020-2023HCC/579hcc/模型代码总结2/R-code/R语言绘制柱形图或气泡图可视化GO、KEGG富集结果/GO.txt', sep = '\\t', stringsAsFactors = TRUE)\n", "\n", "#柱形图，纵坐标是 GO Term，横坐标是各 GO Term 的富集得分（Enrichment_score），颜色按 p 值着色\n", "p <- ggplot(go_enrich, aes(Term, Enrichment_score)) +\n", "geom_col(aes(fill = pValue), width = 0.5) +\n", "scale_fill_gradient(high = 'blue', low = 'red') +\n", "theme(panel.grid = element_blank(), panel.background = element_rect(color = 'black', fill = 'transparent')) +\n", "scale_y_continuous(expand = expansion(mult = c(0, 0.1))) + \n", "coord_flip() +\n", "labs(x = '', y = 'Enrichment Score')\n", "\n", "p\n", "\n", "\n", "#或者根据 Category 绘制分面图\n", "p + facet_grid(Category~., scale = 'free_y', space = 'free_y') +\n", "theme(legend.position = 'none')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["\n", "#按 p 值由低到高排个序\n", "go_enrich <- go_enrich[order(go_enrich$Category, go_enrich$pValue, decreasing = c(FALSE, FALSE)), ]\n", "go_enrich$Term <- factor(go_enrich$Term, levels = go_enrich$Term)\n", "\n", "#柱形图，横坐标 GO Term，纵坐标是 p 值的对数转换，颜色按 Category 着色\n", "p1 <- ggplot(go_enrich, aes(Term, -log10(pValue))) +\n", "geom_col(aes(fill = Category), width = 0.5) +\n", "scale_fill_manual(values = c('#8EA1CB', '#67C1A5', '#FA8E61')) +\n", "theme(panel.grid = element_blank(), panel.background = element_rect(color = 'black', fill = 'transparent'), \n", "\taxis.text.x = element_text(angle = 60, hjust = 1, vjust = 1)) +\n", "scale_y_continuous(expand = expansion(mult = c(0, 0.1))) + \n", "labs(x = '', y = '-Log10 P-Value\\n')\n", "\n", "p1\n", "\n", "#或者根据 Category 绘制分面图\n", "p1 + \n", "facet_grid(.~Category, scale = 'free_x', space = 'free_x') +\n", "theme(legend.position = 'none')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#按 p 值由高到低排个序\n", "go_enrich <- go_enrich[order(go_enrich$Category, go_enrich$pValue, decreasing = c(TRUE, TRUE)), ]\n", "go_enrich$Term <- factor(go_enrich$Term, levels = go_enrich$Term)\n", "\n", "#柱形图，横坐标 p 值的对数转换，纵坐标是 GO Term，颜色按 Category 着色\n", "p2 <- ggplot(go_enrich, aes(Term, -log10(pValue))) +\n", "geom_col(aes(fill = Category), width = 0.5) +\n", "scale_fill_manual(values = c('#8EA1CB', '#67C1A5', '#FA8E61')) +\n", "theme(panel.grid = element_blank(), panel.background = element_rect(color = 'black', fill = 'transparent')) +\n", "scale_y_continuous(expand = expansion(mult = c(0, 0.1))) + \n", "coord_flip() +\n", "labs(x = '', y = '-Log10 P-Value\\n')\n", "\n", "p2\n", "\n", "\n", "#或者根据 Category 绘制分面图\n", "p2 + facet_grid(Category~., scale = 'free_y', space = 'free_y') +\n", "theme(legend.position = 'none')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#######气泡图\n", "library(ggplot2)\n", "\n", "#读取作图数据，这是一组上调基因的 GO 富集结果，展示了 top 富集的条目\n", "go_enrich <- read.delim('GO.txt', sep = '\\t', stringsAsFactors = FALSE)\n", "\n", "#按富集得分由低到高排个序\n", "go_enrich <- go_enrich[order(go_enrich$Enrichment_score, decreasing = FALSE), ]\n", "go_enrich$Term <- factor(go_enrich$Term, levels = go_enrich$Term)\n", "\n", "#气泡图，纵坐标是 GO Term，横坐标是各 GO Term 的富集得分（Enrichment_score）\n", "#按各 GO Term 中富集的基因数量（Count）赋值气泡图中点的大小，颜色按 p 值着色\n", "p <- ggplot(go_enrich, aes(Term, Enrichment_score)) +\n", "geom_point(aes(size = Count, color = pValue)) +\n", "scale_size(range = c(2, 6)) +\n", "scale_color_gradient(high = 'blue', low = 'red') +\n", "theme(panel.background = element_rect(color = 'black', fill = 'transparent'), \n", "    panel.grid = element_blank(), legend.key = element_blank()) +\n", "coord_flip() +\n", "labs(x = '', y = 'Enrichment Score')\n", "\n", "p\n", "\n", "#或者根据 Category 绘制分面图\n", "p + facet_grid(Category~., scale = 'free_y', space = 'free_y')\n", "\n", "#######气泡图，多组情况\n", "go_enrich <- read.delim('GO.4group.txt', stringsAsFactors = FALSE)\n", "\n", "#纵坐标是 GO Term，横坐标是各个分组比较\n", "#按各 GO Term 中富集的基因数量（Count）赋值气泡图中点的大小，颜色按 p 值着色\n", "ggplot(go_enrich, aes(Group, Term)) +\n", "geom_point(aes(color = -log10(pValue), size = Count)) +\n", "scale_size(range = c(2, 6)) +\n", "scale_color_gradientn(colors = c('#55B047', '#FBA304', '#FF1900')) +\n", "theme_bw() +\n", "labs(x = '', y = '')\n"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}