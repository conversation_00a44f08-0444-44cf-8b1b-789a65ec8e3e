# install.packages("waterfalls", repos = "https://cloud.r-project.org") 
# 方法1 - 使用ggplot2包  OK
library(ggplot2)
library(readxl)
data<-read_excel("D:/dataset/validation30.xlsx")
# data<-read.csv("C:/Users/<USER>/Desktop/ch05/lung.csv")

# 假设我们有一个名为data的数据框，包含ID，Handcrafted_pathomics_score和VETC列
# 如果你的数据框的列名不同，请相应地调整这个代码

# 创建一个新的列，用于决定每个条形的颜色
data$color <- ifelse(data$VETC == 0, "#66C2A5", "#FF6347")

# 制作瀑布图
ggplot(data, aes(x = reorder(ID, -nomogram2), 
                 y = nomogram2, fill = color)) +
  geom_bar(stat = "identity") +
  scale_fill_identity() +
  theme_minimal() +
  labs(title = "Waterfall plot for changes in QoL scores",
       y = "Change from baseline (%) in QoL score", 
       x = "ID") +
  theme(plot.title = element_text(hjust = 0.5), 
        legend.position = "none")