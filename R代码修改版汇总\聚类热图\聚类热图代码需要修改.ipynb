{"cells": [{"cell_type": "code", "execution_count": 34, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "\"Setting row names on a tibble is deprecated.\"\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0gAAANICAMAAADKOT/pAAAA/1BMVEUAAAAAAP9NTU1oaGh8fHyA//+E//+MjIyQ//+ampqd//+h//+l//+m//+np6eo//+p//+q//+s//+t//+v//+w//+ysrKz//+7//+8//+9vb2+//+////D///G///Hx8fI///J///N///O///Q0NDT///V///X///Z2dnZ///e///h4eHi///k///p6enw8PDw///2////AAD/gP//hP//l///mP//mf//n///oP//of//ov//o///pv//p///q///rv//r///sP//s///tP//tf//uf//u///vP//vf//v///wP//w///xf//zP//zf//0v//1f//1v//2P////8Cjb2HAAAACXBIWXMAABJ0AAASdAHeZh94AAAU20lEQVR4nO3caXvcVhmA4ZOxYRgWF0pjwFDCYihg6oGyU6BAWcpWNv3/34K3cWKaXPb7nqMzsnQ/H5J8GPloRrq1HI1TBknVlX2vgDSHQJIaBJLUIJCkBoEkNQgkqUEgSQ0CSWoQSFKDQJIaBJLUIJCkBoEkNQgkqUEgSQ0CSWoQSFKDQJIaBJLUIJCkBoEkNQgkqUEgSQ0CSWoQSFKDQJIaBJLUIJCkBj2H9M497V73mXivx/tEuE/F+3y8L8d7K95r4X4W74fxPoz3Qbw/hvt3PJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgmkuUE625SyOQcJJJBqIJ2Wy1ZbkEACqQLSanU2bNflCCSQQMpDenZFaFtWIIEEUh7SppzVEQIJJJCG4aAMx6uyqbxFAgmkhUMqZX012QASSCBVQbqcbNiUY5BAAqkG0uU90nk5AAkkkGogvfgXSCCBlIK0BgkkkOohHZfT4fLS7hAkkEDKQ7q4O9peTjY8AwkkkPKQLk5Jl1WekEACaemQhtPDsqr8ph1IIIHUJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEAC6bon97R73VfjPY33/XCfi/eNeF+K9/F4b4T7a7wfxftFvD/H+0e4v8UDCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQJoZpHIdSCCBVAHpDCSQQGoBaV1pCCSQQBpOyjFIIIFUD+kEJJBAqoW0LqebsjoCCSSQ6iBddQgSSCBVQCrl2TBsj2ov8EACadmQrtuWA5BAAqkW0lD7IAkkkEACCSSQKiGtyvbiz/Pax7IggbRsSEfl6Gqy4RQkkEDKQ9qurqa/ax8kgQTSsiFdnI1W5aD62w0ggbRwSG0CCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQZgHpnXvave7teG/G+2K478VLHBNei/dWvK+H+zDen+K9H+/X8d4N9/N4IIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSC+D9J0yVAYSSCBtVyCBBFI1pHUBCSSQaiE9KyCBBFItpPNyCBJIINVCOiznIIEEUiWk4/JsAAkkkOognZX1ABJIIFVCOlhtQQIJpEpIm3I6gAQSSJWQym0ggQQSSCCBtE9IO051jEACCSSQQAIJJJBAmgyk+kACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCaRZQHpyT7vX/T7eX+L9J9y/4v0k3j/j/SHe38N9Id7r8T4d77PxnoZLHNxBAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkGYGabspZXMGEkggVUFalctqJYEE0rIhHZXN5R9rkEACqQLSqmwvHZShLpBAWjakGwcrkEACqRbSUTkBCSSQ6iA9K+Wo0hFIIIF0sl6VY5BAAqkO0kWb2ms7kEACaRi2tbMNIIEE0lA//w0SSMuGdP0c6bwcgAQSSHlIV99s2K7dI4EEUg2km+/aHdY5AgmkpUMajlbloPZ5LEggLR5Sk0ACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCaRZQHrnnnav+3G8d+O9H+69eD+N98t4v4oXfyvfjfetePFd/Okb8b4S7mPxQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkD4C6eSgrI62IIEEUg2ko3LZqlISSCAtG9JZ2VwYOikbkEACKQ9pfU2glKEqkEBaNqQdBJBAAqka0rYcggQSSLWQTsopSCCBVAnpfLWucwQSSCAN21XlhR1IIIE0DIcHtY5AAmnxkM4PDs9BAgmkOkintRN2IIEE0nDexBFIIC0c0qbcBBJIIOUhFZBAAqkeUqNAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgkkkEACCSSQQAIJJJBAAgmkWUB6ck+7170d74N48f31N/F+EO938f4b77fhPhnvzXjfjPe1eHGt344HEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggTQ/SCdlqA0kkBYP6ayABBJItZDOViCBBFItpJNyCBJIINVCKkcDSCCBVAvpbAAJJJCqIQ0ggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCNBqlBIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQQSSCCBBBJIIIEEEkgggQSSpMpAkhoEktQgkKQGgSQ1CCSpQSBJDQJJatDjglQuuvo7tshusfFGiRdao5tFhufrNtogiXcfX69UXTZLummu1Su6/RxjG/nuPx48SkJrYGcq8b18t04PXzA3SOIzDq5XpsSK9azVWmV2pfAiZfdnD0gjb6+bw3hwkdtPYMxBhvhnHFyv3JEnumKJQfI1GyNzEZFcILKTpyGNfqFy81dsV0pAig9y/deokBK7S83G71C7sTpI2r0+vl9kLu0ixQ97qbuXJKTQIM8He/Ai4fUKvvbOAqF7xOgg+aZ5wfmqyu1hKbBI9OyegJRdJNrV+8gcRkJjxJeNrleqzIr1a5prtdf6QNK8ar31u9zYjVv2Rvjxv/MXm8GG7Frbjyv46See8GRuR8av16VduOyzl9hWCf7wZIkN323Oru2HEF3n8DVv30cJ456REm8j+bwgNJW9Gyj08qHDW6k4WPXZW5r9oBLdYPEJtQSk9LOE8HchEvvFqOu0GyT6SCy3IUd/KwlIiRnbfK3GiM9/JiFFP8/cG+zwhZchs26Zc37ygWxskdHfylIgdTsjhT/PzCaOXw3FBxmys9PjzuR3OiPdjhQaZQmQhtSldWwdUpDC1ZxcY4sk30b8Hik8QBdIw8j3oY8WUnayJ3THEx0lUZdjcvbmbfQz0s0ooRd3eCvpr2YOjxFS7BDT8zuFwfodk2Olni+kPuIOvxUx8gCP9EurXYsclJOfY/jkOv7FzZweks7orVzX7v2Mf/Pyf2M9/JXZmbvQKB249jjCdvpdwAlfjeRqN2uX+Hnpj7ILpOAouR0wvtDIVfwu4LhvZuL2Wq1V5sYuYy+6SCdIiY3cY5I9+6A4Mf06+m9CxnuU90iZKbj8OkwNUvqHh+cORv9PG3ZHt/DEdHwa5IXhRq3PNFDrnwPSQ396fpI9dmMVGSJ/fRB0dPNHhzuBR/al1S6QMlcqFaf3xFsJDpCcZI+fx2KDBMtDil9Bxt986OX59jjZUHFG6tPokLKT7BM7KqchZRzFdrF+u1a7kcLH/TykkfeL3B16erTQKKOfkRJlP7CEvdhiPY/QezwbZK+6ggeyMgRv0BP1mR/qco9Ud1s54hhd7gTyTfWy6pXlpma7TMGNvkjmFjE+yp2/QktO7YzUs2mu1avKzXQFIXVSkVssOEjFVWp05Xr8JuTdf0yqZrN2PU6j5YU/A4sED3+JXXyqkBIl99fxr5/ie1hXeo/rHqnLs5c+u3hmlB5PMXOTY9HN8nzJzFKxn95n6m4CB8VRD31zgrR7H32uUqOjjHtT9ZHhHvzaTnv43iGN/ai6hH8XcOqQgm8lWmKyIXdGyl4Nhidtu7RnSB1+Vyj+04cukLIPX4IH5HipW93oEvmrQZBeMni/0efwm7vx+6OebyUhfHxIy7i0mySjCZeAVDtabIku8xORm7dlTDZ0O1xO79ySa+qQogOMfY+0lOnvPmUOfaPPMQ/PDyOJRRJPUhJN7mowf9wFqb7MxXhi3irfZDdAnwui2Vx27fUeKfwLn8lBktdDy5Y01fVKNHNIiWelyYHG/8WXiqa6w459fBvCB9H0DTVIzYZKPXx5/JBy38LqMAV0u93jT5cT6wVSw8GCzz3v/GPMxj/wT7EEpIrNAtJ+6jLZ0OtJ6TQpJbZ9HFLXJ+vzh5QYYpLfa0gXvhOJ372E6wKpa7N/INt1LnuqZX53dfStAtJjasIff78vwYWnWkaHlPxS/t1/PGSQbpt9evtX26YLqdOpMvMfRE7yzjX+gXV9G3v9uDockycLqc+K5f4PoWlCCu8ui4HU45i8bEiZ50jDZCFFWwqkTsfkDmNkmq7w8N3LVAOp7TDTnMueLKTJFt6MIC2hZb/7RHEWS3ogu+912F9dt/IcmviNG0h6HIH0yqEdk/XwQJIaBJJelvNxsIl/YNNcqwVlA8wj23GvTfTwqnA25B7DaD7ZlHsLozllY+4rn/yssjn31MQnoRTMZpQaBJLUIJCkBoEkNQgkqUEgSQ0CSWoQSFKDQJIaBJLUIJCkBoEkNQgkqUEgSQ0CSWoQSFKDQJIaBJLUIJCkBoEkNQgkqUEgSQ0CSWoQSFKDQJIaBJLUIJCkBoEkNeh/n6awstu50pMAAAAASUVORK5CYII=", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["# 安装并加载readxl包\n", "# install.packages(\"readxl\")\n", "library(readxl)\n", "\n", "# 从Excel文件中读取数据\n", "data <- read_excel(\"K:/2020-2023HCC/579hcc/模型代码总结2/R-code/R代码修改版汇总/聚类热图/heatmap_example.xlsx\")\n", "\n", "# 设置group和sample为行名\n", "rownames(data) <- paste(data$group, data$sample, sep='-')\n", "data <- data[, -c(1, 2)]  # 删除group和sample列\n", "\n", "# 创建颜色向量\n", "colors <- c(\"blue\", \"red\")\n", "group_colors <- rep(colors, length.out = nrow(data))\n", "\n", "# 绘制热图\n", "heatmap(as.matrix(data), scale=\"column\", Colv=NA, Rowv=NA, col = cm.colors(256), RowSideColors = group_colors)\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 6 × 11</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>mpg</th><th scope=col>cyl</th><th scope=col>disp</th><th scope=col>hp</th><th scope=col>drat</th><th scope=col>wt</th><th scope=col>qsec</th><th scope=col>vs</th><th scope=col>am</th><th scope=col>gear</th><th scope=col>carb</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>Mazda RX4</th><td>21.0</td><td>6</td><td>160</td><td>110</td><td>3.90</td><td>2.620</td><td>16.46</td><td>0</td><td>1</td><td>4</td><td>4</td></tr>\n", "\t<tr><th scope=row>Mazda RX4 Wag</th><td>21.0</td><td>6</td><td>160</td><td>110</td><td>3.90</td><td>2.875</td><td>17.02</td><td>0</td><td>1</td><td>4</td><td>4</td></tr>\n", "\t<tr><th scope=row>Datsun 710</th><td>22.8</td><td>4</td><td>108</td><td> 93</td><td>3.85</td><td>2.320</td><td>18.61</td><td>1</td><td>1</td><td>4</td><td>1</td></tr>\n", "\t<tr><th scope=row>Hornet 4 Drive</th><td>21.4</td><td>6</td><td>258</td><td>110</td><td>3.08</td><td>3.215</td><td>19.44</td><td>1</td><td>0</td><td>3</td><td>1</td></tr>\n", "\t<tr><th scope=row>Hornet Sportabout</th><td>18.7</td><td>8</td><td>360</td><td>175</td><td>3.15</td><td>3.440</td><td>17.02</td><td>0</td><td>0</td><td>3</td><td>2</td></tr>\n", "\t<tr><th scope=row>Valiant</th><td>18.1</td><td>6</td><td>225</td><td>105</td><td>2.76</td><td>3.460</td><td>20.22</td><td>1</td><td>0</td><td>3</td><td>1</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 6 × 11\n", "\\begin{tabular}{r|lllllllllll}\n", "  & mpg & cyl & disp & hp & drat & wt & qsec & vs & am & gear & carb\\\\\n", "  & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl>\\\\\n", "\\hline\n", "\tMazda RX4 & 21.0 & 6 & 160 & 110 & 3.90 & 2.620 & 16.46 & 0 & 1 & 4 & 4\\\\\n", "\tMazda RX4 Wag & 21.0 & 6 & 160 & 110 & 3.90 & 2.875 & 17.02 & 0 & 1 & 4 & 4\\\\\n", "\tDatsun 710 & 22.8 & 4 & 108 &  93 & 3.85 & 2.320 & 18.61 & 1 & 1 & 4 & 1\\\\\n", "\tHornet 4 Drive & 21.4 & 6 & 258 & 110 & 3.08 & 3.215 & 19.44 & 1 & 0 & 3 & 1\\\\\n", "\tHornet Sportabout & 18.7 & 8 & 360 & 175 & 3.15 & 3.440 & 17.02 & 0 & 0 & 3 & 2\\\\\n", "\tValiant & 18.1 & 6 & 225 & 105 & 2.76 & 3.460 & 20.22 & 1 & 0 & 3 & 1\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 6 × 11\n", "\n", "| <!--/--> | mpg &lt;dbl&gt; | cyl &lt;dbl&gt; | disp &lt;dbl&gt; | hp &lt;dbl&gt; | drat &lt;dbl&gt; | wt &lt;dbl&gt; | qsec &lt;dbl&gt; | vs &lt;dbl&gt; | am &lt;dbl&gt; | gear &lt;dbl&gt; | carb &lt;dbl&gt; |\n", "|---|---|---|---|---|---|---|---|---|---|---|---|\n", "| Mazda RX4 | 21.0 | 6 | 160 | 110 | 3.90 | 2.620 | 16.46 | 0 | 1 | 4 | 4 |\n", "| Mazda RX4 Wag | 21.0 | 6 | 160 | 110 | 3.90 | 2.875 | 17.02 | 0 | 1 | 4 | 4 |\n", "| Datsun 710 | 22.8 | 4 | 108 |  93 | 3.85 | 2.320 | 18.61 | 1 | 1 | 4 | 1 |\n", "| Hornet 4 Drive | 21.4 | 6 | 258 | 110 | 3.08 | 3.215 | 19.44 | 1 | 0 | 3 | 1 |\n", "| Hornet Sportabout | 18.7 | 8 | 360 | 175 | 3.15 | 3.440 | 17.02 | 0 | 0 | 3 | 2 |\n", "| Valiant | 18.1 | 6 | 225 | 105 | 2.76 | 3.460 | 20.22 | 1 | 0 | 3 | 1 |\n", "\n"], "text/plain": ["                  mpg  cyl disp hp  drat wt    qsec  vs am gear carb\n", "Mazda RX4         21.0 6   160  110 3.90 2.620 16.46 0  1  4    4   \n", "Mazda RX4 Wag     21.0 6   160  110 3.90 2.875 17.02 0  1  4    4   \n", "Datsun 710        22.8 4   108   93 3.85 2.320 18.61 1  1  4    1   \n", "Hornet 4 Drive    21.4 6   258  110 3.08 3.215 19.44 1  0  3    1   \n", "Hornet Sportabout 18.7 8   360  175 3.15 3.440 17.02 0  0  3    2   \n", "Valiant           18.1 6   225  105 2.76 3.460 20.22 1  0  3    1   "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 6 × 6 of type dbl</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>mpg</th><th scope=col>disp</th><th scope=col>hp</th><th scope=col>drat</th><th scope=col>wt</th><th scope=col>qsec</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>Mazda RX4</th><td> 0.1508848</td><td>-0.57061982</td><td>-0.5350928</td><td> 0.5675137</td><td>-0.610399567</td><td>-0.7771651</td></tr>\n", "\t<tr><th scope=row>Mazda RX4 Wag</th><td> 0.1508848</td><td>-0.57061982</td><td>-0.5350928</td><td> 0.5675137</td><td>-0.349785269</td><td>-0.4637808</td></tr>\n", "\t<tr><th scope=row>Datsun 710</th><td> 0.4495434</td><td>-0.99018209</td><td>-0.7830405</td><td> 0.4739996</td><td>-0.917004624</td><td> 0.4260068</td></tr>\n", "\t<tr><th scope=row>Hornet 4 Drive</th><td> 0.2172534</td><td> 0.22009369</td><td>-0.5350928</td><td>-0.9661175</td><td>-0.002299538</td><td> 0.8904872</td></tr>\n", "\t<tr><th scope=row>Hornet Sportabout</th><td>-0.2307345</td><td> 1.04308123</td><td> 0.4129422</td><td>-0.8351978</td><td> 0.227654255</td><td>-0.4637808</td></tr>\n", "\t<tr><th scope=row>Valiant</th><td>-0.3302874</td><td>-0.04616698</td><td>-0.6080186</td><td>-1.5646078</td><td> 0.248094592</td><td> 1.3269868</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 6 × 6 of type dbl\n", "\\begin{tabular}{r|llllll}\n", "  & mpg & disp & hp & drat & wt & qsec\\\\\n", "\\hline\n", "\tMazda RX4 &  0.1508848 & -0.57061982 & -0.5350928 &  0.5675137 & -0.610399567 & -0.7771651\\\\\n", "\tMazda RX4 Wag &  0.1508848 & -0.57061982 & -0.5350928 &  0.5675137 & -0.349785269 & -0.4637808\\\\\n", "\tDatsun 710 &  0.4495434 & -0.99018209 & -0.7830405 &  0.4739996 & -0.917004624 &  0.4260068\\\\\n", "\tHornet 4 Drive &  0.2172534 &  0.22009369 & -0.5350928 & -0.9661175 & -0.002299538 &  0.8904872\\\\\n", "\tHornet Sportabout & -0.2307345 &  1.04308123 &  0.4129422 & -0.8351978 &  0.227654255 & -0.4637808\\\\\n", "\tValiant & -0.3302874 & -0.04616698 & -0.6080186 & -1.5646078 &  0.248094592 &  1.3269868\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 6 × 6 of type dbl\n", "\n", "| <!--/--> | mpg | disp | hp | drat | wt | qsec |\n", "|---|---|---|---|---|---|---|\n", "| Mazda RX4 |  0.1508848 | -0.57061982 | -0.5350928 |  0.5675137 | -0.610399567 | -0.7771651 |\n", "| Mazda RX4 Wag |  0.1508848 | -0.57061982 | -0.5350928 |  0.5675137 | -0.349785269 | -0.4637808 |\n", "| Datsun 710 |  0.4495434 | -0.99018209 | -0.7830405 |  0.4739996 | -0.917004624 |  0.4260068 |\n", "| Hornet 4 Drive |  0.2172534 |  0.22009369 | -0.5350928 | -0.9661175 | -0.002299538 |  0.8904872 |\n", "| Hornet Sportabout | -0.2307345 |  1.04308123 |  0.4129422 | -0.8351978 |  0.227654255 | -0.4637808 |\n", "| Valiant | -0.3302874 | -0.04616698 | -0.6080186 | -1.5646078 |  0.248094592 |  1.3269868 |\n", "\n"], "text/plain": ["                  mpg        disp        hp         drat       wt          \n", "Mazda RX4          0.1508848 -0.57061982 -0.5350928  0.5675137 -0.610399567\n", "Mazda RX4 Wag      0.1508848 -0.57061982 -0.5350928  0.5675137 -0.349785269\n", "Datsun 710         0.4495434 -0.99018209 -0.7830405  0.4739996 -0.917004624\n", "Hornet 4 Drive     0.2172534  0.22009369 -0.5350928 -0.9661175 -0.002299538\n", "Hornet Sportabout -0.2307345  1.04308123  0.4129422 -0.8351978  0.227654255\n", "Valiant           -0.3302874 -0.04616698 -0.6080186 -1.5646078  0.248094592\n", "                  qsec      \n", "Mazda RX4         -0.7771651\n", "Mazda RX4 Wag     -0.4637808\n", "Datsun 710         0.4260068\n", "Hornet 4 Drive     0.8904872\n", "Hornet Sportabout -0.4637808\n", "Valiant            1.3269868"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["TRUE"], "text/latex": ["TRUE"], "text/markdown": ["TRUE"], "text/plain": ["[1] TRUE"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["head(mtcars) #查看mtcars数据集\n", "#                   mpg cyl disp  hp drat    wt  qsec vs am gear carb\n", "#Mazda RX4         21.0   6  160 110 3.90 2.620 16.46  0  1    4    4\n", "#Mazda RX4 Wag     21.0   6  160 110 3.90 2.875 17.02  0  1    4    4\n", "#Datsun 710        22.8   4  108  93 3.85 2.320 18.61  1  1    4    1\n", "data <- mtcars[c(\"mpg\",\"disp\",\"hp\",\"drat\",\"wt\", \"qsec\")] #选择需要的变量\n", "df <- scale(data) #对数据标准化\n", "head(df) #查看标准化后的数据\n", "#                         mpg        disp         hp       drat           wt       qsec\n", "#Mazda RX4          0.1508848 -0.57061982 -0.5350928  0.5675137 -0.610399567 -0.7771651\n", "#Mazda RX4 Wag      0.1508848 -0.57061982 -0.5350928  0.5675137 -0.349785269 -0.4637808\n", "#Datsun 710         0.4495434 -0.99018209 -0.7830405  0.4739996 -0.917004624  0.4260068\n", "is.matrix(df) #判断是否为矩阵\n", "\n", "heatmap(df) #基础图形\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["# install.packages(\"RColorBrewer\")\n", "library(RColorBrewer) # 加载package\n", "col <- colorRampPalette(brewer.pal(n = 9, name = \"YlGnBu\"))(100) # 生成颜色\n", "\n", "# 绘制热图\n", "heatmap(df, col = col)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#安装package\n", "# if (!requireNamespace(\"BiocManager\", quietly = TRUE))\n", "#     install.packages(\"BiocManager\")\n", "# BiocManager::install(\"ComplexHeatmap\")\n", "#加载package\n", "library(ComplexHeatmap)\n", "#绘图\n", "Heatmap(df) #基础图形"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}