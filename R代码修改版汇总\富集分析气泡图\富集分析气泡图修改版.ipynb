{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" [1] \"Level1\"         \"Level2\"         \"Map_ID\"         \"Term\"          \n", " [5] \"Protein_number\" \"TestAll\"        \"Ref\"            \"RefAll\"        \n", " [9] \"Test_per\"       \"Ref_per\"        \"Test_Seq\"       \"Ref_Seq\"       \n", "[13] \"Over_Under\"     \"P_value\"        \"FDR\"            \"richFactor\"    \n"]}, {"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 富集气泡图已成功\n", "library(ggplot2)\n", "\n", "# 读取富集气泡图数据文件\n", "# df= read.delim(\"https://www.bioladder.cn/shiny/zyp/bioladder2/demoData/bubble/data.txt\")# 这里读取了网络上的demo数据，将此处换成你自己电脑里的文件\n", "df <- read.csv(\"D:/R-code/kegg.csv\")\n", "print(colnames(df))\n", "top20_terms <- df[1:20, ]# 筛选出前20个term的数据\n", "\n", "# 绘图：\n", "pdf(\"D:/R-code/KEGG气泡图.pdf\", height = 7, width = 8)\n", "\n", "# 绘图\n", "ggplot(top20_terms,aes(x = richFactor, \n", "              y = reorder(Term,-log10(P_value),sum), # 按照富集度大小排序\n", "              size = Protein_number,\n", "              colour=-log10(P_value))) +\n", "  geom_point(shape = 16) +                    # 设置点的形状\n", "  labs(x = \"Rich factor\", y = \"KEGG Pathway(Top20)\")+           # 设置x，y轴的名称\n", "  scale_colour_continuous(                    # 设置颜色图例\n", "    name=\"-log10(P value)\",                        # 图例名称\n", "    low=\"green\",                              # 设置颜色范围\n", "    high=\"red\")+\n", "  scale_radius(                               # 设置点大小图例\n", "    range=c(2,5),                             # 设置点大小的范围\n", "    name=\"Protein number\")+                             # 图例名称\n", "  guides(   \n", "    color = guide_colorbar(order = 1),        # 决定图例的位置顺序\n", "    size = guide_legend(order = 2)\n", "  )+\n", "  theme_bw() +\n", "  theme(axis.text.y = element_text(size = 12))  # 设置纵坐标字体大小为12\n", "\n", "dev.off()"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}