#%%目前R语言机器学习领域的两大当红R包mlr3和tidymodels
# 安装并加载必要的包
# 安装并加载必要的包
# install.packages("mlr3")
# install.packages("mlr3learners")
# install.packages("mlr3measures")
# install.packages("pROC")
# 安装并加载必要的包
# 安装并加载必要的包

library(mlr3)
library(mlr3learners)
library(mlr3measures)
library(pROC)

# 示例数据集
data <- data.frame(
  response = factor(c(0, 1, 1, 0, 1, 0, 1, 1)),
  predictor1 = c(2.3, 1.1, 0.5,1.2, 3.3, 1.8, 3.9, 4.1),
  predictor2 = c(5.1, 1.2, 5.8, 1.9, 6.0, 5.5, 6.1, 5.7)
)

# 将数据转换为任务
task <- TaskClassif$new(id = "example", backend = data, target = "response")

# 定义学习者
lrn_svm <- lrn("classif.svm", predict_type = "prob")
lrn_lr <- lrn("classif.log_reg", predict_type = "prob")

# 训练模型
model_svm <- lrn_svm$train(task)
model_lr <- lrn_lr$train(task)

# 预测
pred_svm <- model_svm$predict(task)
pred_lr <- model_lr$predict(task)

# 计算 AUC
auc_svm <- pred_svm$score(msr("classif.auc"))
auc_lr <- pred_lr$score(msr("classif.auc"))

# 输出 AUC
cat("AUC for SVM:", auc_svm, "\n")
cat("AUC for Logistic Regression:", auc_lr, "\n")

# 绘制 ROC 曲线
roc_svm <- roc(data$response, pred_svm$prob[,2])
roc_lr <- roc(data$response, pred_lr$prob[,2])

plot(roc_svm, col = "blue", main = "ROC Curve")
plot(roc_lr, col = "red", add = TRUE)
legend("bottomright", legend = c("SVM", "Logistic Regression"), col = c("blue", "red"), lwd = 2)
