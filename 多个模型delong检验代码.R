#多个模型auc比较：delong检验
# 导入所需的包
library(pROC)
library(readxl)  # 导入readxl包用于读取Excel文件
library(openxlsx)
data <- read.xlsx("K:/2020-2023HCC/all-HCC/734hcctumor/val.xlsx")


# 提取标签和预测概率
labels <- data[, 2]
predictions <- data[, 3:6]  # 假设第3到第6列是多个模型的预测概率
model_names <- colnames(predictions)


# 计算每个模型的AUC
auc_results <- data.frame(Model = character(), AUC = numeric(), Lower_CI = numeric(), Upper_CI = numeric(), stringsAsFactors = FALSE)
for (i in 1:ncol(predictions)) {
  model <- predictions[, i]
  roc_obj <- roc(labels, model)
  ci <- ci.auc(roc_obj)
  auc_results <- rbind(auc_results, data.frame(Model = model_names[i], AUC = ci[2], Lower_CI = ci[1], Upper_CI = ci[3]))
}

# 比较所有模型的AUC
delong_results <- data.frame(Model_Comparison = character(), AUC_Comparison = character(), Z_score = numeric(), P_value = numeric(), stringsAsFactors = FALSE)
for (i in 1:(ncol(predictions) - 1)) {
  for (j in (i + 1):ncol(predictions)) {
    roc1 <- roc(labels, predictions[, i])
    roc2 <- roc(labels, predictions[, j])
    test <- roc.test(roc1, roc2, method = "delong")
    delong_results <- rbind(delong_results, data.frame(Model_Comparison = paste(model_names[i], "vs", model_names[j]),
                                                       AUC_Comparison = paste0(round(auc(roc1), 3), " vs ", round(auc(roc2), 3)),
                                                       Z_score = test$statistic,
                                                       P_value = test$p.value))
  }
}

# 保存结果到Excel文件
output_path <- "K:/2020-2023HCC/all-HCC/734hcctumor/val_delong_test_results.xlsx"
write.xlsx(list(AUC_Results = auc_results, DeLong_Test_Results = delong_results), file = output_path)

print("Results saved to Excel.")






