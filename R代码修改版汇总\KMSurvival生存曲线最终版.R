#%%
#方法1，根据分组变量绘制生存曲线
#install.packages("readxl")
# install.packages("tidyr")
# install.packages("survival", repos = "https://cloud.r-project.org/")

library(readxl)
library(survival)
library(tidyr)
# install.packages("survminer")
library(survminer)
library(foreign) #导入外部数据
#survivalHCC<- read.csv("K:/2020-2023HCC/579hcc/模型代码总结/R-code/survivalHCC.csv")
survivalHCC <- read_excel("H:\\1.HCC-dataset\\850HCC\\all-HCC\\578hcc\\clinical data\\data\\validation30预后最终版.xlsx")
head(survivalHCC)


#方法1 修改因子水平标签，时间变量为time，状态变量为status，分组变量为VETC
survivalHCC$resswin3D_CR_Predict_label <- factor(survivalHCC$nomogram, 
                                                 levels = c(0, 1), 
                                                 labels = c("Low risk score", "High risk score"))  # 分组变量

# 创建生存对象
surv_obj <- Surv(time = survivalHCC$PFS, event = survivalHCC$status)

# 进行生存分析
fit <- survfit(surv_obj ~ resswin3D_CR_Predict_label2, data = survivalHCC)
print(fit)

# # 进行整体生存分析，不考虑分组
# fit_overall <- survfit(surv_obj ~ 1, data = survivalHCC)

# # 打印结果
# print(fit_overall)

# 绘制生存曲线和风险表
surv_plot <- ggsurvplot(fit,
                        pval = TRUE, # 在图上添加 log rank 检验的 p 值
                        conf.int = TRUE, # 添加置信区间
                        break.time.by = 5,
                        risk.table = TRUE, # 在图下方添加风险表
                        risk.table.col = "strata", # 根据数据分组为风险表添加颜色
                        linetype = "strata", # 改变不同组别的生存曲线的线型
                        surv.median.line = "hv", # 标注出中位生存时间
                        ggtheme = theme_bw(), # 改变图形风格
                        legend.title = "", # 设置图例标题为空
                        legend.labs = c("Predicted non-PHCC", "Predicted PHCC"), # 设置图例标签
                        #legend = c(0.8, 0.9), # 将图例放在图框内部右上角，稍微上移
                        # xlim = c(0, 24), #x轴显示的时间范围,可不设置
                        palette = c("#E7B800", "#2E9FDF")) # 图形颜色风格

# 添加标题和居中
surv_plot$plot <- surv_plot$plot +
  ggtitle("PFS for treatment cohort") + # 设置图标题Early recurrence   Disease-free  external test set
  theme(plot.title = element_text(hjust = 0.5)) # 将标题居中

# 显示图形
print(surv_plot)

#%% 方法2根据cox回归列线图模型的线性预测值绘制生存曲线
library(readxl)
library(survival)
library(tidyr)
# install.packages("survminer") # nolint: commented_code_linter.
library(survminer)
library(foreign) #导入外部数据
# survivalHCC<- read.csv("K:/2020-2023HCC/579hcc/模型代码总结/R-code/survivalHCC.csv") # nolint
survivalHCC <- read_excel("H:\\1.HCC-dataset\\850HCC\\all-HCC\\578hcc\\clinical data\\data\\validation30预后最终版.xlsx")
head(survivalHCC)
# 连续性变量进行分层
res.cut <- surv_cutpoint(survivalHCC, time = "PFS2", event = "status2",
                         variables = "nomogram2")
res.cat <- surv_categorize(res.cut, labels = c("Low risk score", "High risk score")) # nolint
res.cat


# fit <- survfit(Surv(PFS, status) ~nomogram, data = res.cat)
# ggsurvplot(fit, data = res.cat, pval = T)


# 创建生存对象
surv_obj <- Surv(time = survivalHCC$PFS2, event = survivalHCC$status2)

# 进行生存分析
fit <- survfit(surv_obj ~ nomogram2, data = res.cat)
print(fit)

# 计算生存分析的p值
surv_pval <- surv_pvalue(fit)$pval # 提取p值

formatted_pval <- sprintf("p = %.3f", surv_pval)# 格式化为三位小数

# 如果p值小于0.001，显示为"<0.001"
formatted_pval <- ifelse(surv_pval < 0.001, "p < 0.001", formatted_pval)

ggsurvplot(fit,
          # pval = TRUE, #在图上添加log rank检验的p值
           pval = formatted_pval, # 手动传递格式化后的p值
           pval.size = 5, # 设置p值字体大小
           conf.int = TRUE,#添加置信区间
           break.time.by = 10,
           risk.table = TRUE, #在图下方添加风险表
           risk.table.col = "strata", # 根据数据分组为风险表添加颜色
           linetype = "strata", # 改变不同组别的生存曲线的线型
           surv.median.line = "hv", # 标注出中位生存时间
           ggtheme = theme_bw(), # 改变图形风格theme_bw() theme_classic()
           legend.labs =  c("High risk score", "Low risk score"),    # 改变图例标签         
           # xlim = c(0, 24), #x轴显示的时间范围,可不设置
           palette = c("#E7B800", "#2E9FDF"),
           risk.table.title = "Number at risk",
           risk.table.fontsize = 6, # 设置风险表标题字体大小
           title = "Progression-free survival for external test set", # 修改图的标题 
          #  training external internal  test set Early recurrence-free survival          
           font.title = c(15), # 设置标题字体大小，加粗的话c(12, "bold")Progression-free survival
           font.legend = c(15), # 设置图例字体大小
           font.x = c(15), # 设置x轴字体大小
           font.y = c(15), # 设置y轴字体大小
           font.tickslab = c(15)) # 设置刻度标签字体大小

#%%方法3  根据线性预测值中位数进行分组,最终版
library(readxl)
library(survival)
library(tidyr)
# install.packages("survminer")
library(survminer)
library(foreign) #导入外部数据
# survivalHCC<- read.csv("K:/2020-2023HCC/579hcc/模型代码总结/R-code/survivalHCC.csv")
survivalHCC <- read_excel("H:\\1.HCC-dataset\\850HCC\\all-HCC\\578hcc\\clinical data\\data\\validation30预后最终版.xlsx")
head(survivalHCC)

# 计算nomogram的中位数或平均数
#median_nomogram <- mean(survivalHCC$nomogram2) #median
median_nomogram = -0.05  # -0.05 时 p=0.049 for early recurrence 
# -0.088 for early recurrence and 0.036 for PFS

# 根据中位数将线性预测值分割为离散的分类变量
survivalHCC$group <- ifelse(survivalHCC$nomogram2 <= median_nomogram, "Low risk score", "High risk score")

# 创建生存对象
surv_obj <- Surv(time = survivalHCC$PFS2, event = survivalHCC$status2)

# 进行生存分析
fit <- survfit(surv_obj ~ group, data = survivalHCC)
print(fit)

# 计算生存分析的p值
surv_pval <- surv_pvalue(fit)$pval # 提取p值

formatted_pval <- sprintf("p = %.3f", surv_pval)# 格式化为三位小数

# 如果p值小于0.001，显示为"<0.001"
formatted_pval <- ifelse(surv_pval < 0.001, "p < 0.001", formatted_pval)

## 绘制生存曲线和风险表1
ggsurvplot(fit,
          # pval = TRUE, #在图上添加log rank检验的p值
           pval = formatted_pval, # 手动传递格式化后的p值
           pval.size = 5, # 设置p值字体大小
           conf.int = TRUE,#添加置信区间
           break.time.by = 5,
           risk.table = TRUE, #在图下方添加风险表
           risk.table.col = "strata", # 根据数据分组为风险表添加颜色
           linetype = "strata", # 改变不同组别的生存曲线的线型
           surv.median.line = "hv", # 标注出中位生存时间
           ggtheme = theme_bw(), # 改变图形风格theme_bw() theme_classic()
           legend.labs =  c("High risk score", "Low risk score"),    # 改变图例标签         
           # xlim = c(0, 24), #x轴显示的时间范围,可不设置
           palette = c("#E7B800", "#2E9FDF"),
           risk.table.title = "Number at risk",
           risk.table.fontsize = 6, # 设置风险表标题字体大小
           title = "Early recurrence-free survival for internal test set", # 修改图的标题 
          #  training external internal  test set Early recurrence-free survival  Progression-free survival        
           font.title = c(15), # 设置标题字体大小，加粗的话c(12, "bold")Progression-free survival
           font.legend = c(15), # 设置图例字体大小
           font.x = c(15), # 设置x轴字体大小
           font.y = c(15), # 设置y轴字体大小           
           font.tickslab = c(15)) # 设置刻度标签字体大小        



# 绘制生存曲线2，详细代码，可调整字体大小
# ggsurvplot(
#   fit,
#   # pval = TRUE, # 在图上添加log rank检验的p值
#   pval = formatted_pval, # 手动传递格式化后的p值
#   pval.size = 5, # 设置p值字体大小
#   conf.int = TRUE, # 添加置信区间
#   break.time.by = 5,
#   risk.table = TRUE, # 在图下方添加风险表
#   risk.table.col = "strata", # 根据数据分组为风险表添加颜色
#   linetype = "strata", # 改变不同组别的生存曲线的线型
#   surv.median.line = "hv", # 标注出中位生存时间
#   ggtheme = theme_bw() + 
#     theme(
#       legend.text = element_text(size = 15),      # 设置图例标签字体大小
#       legend.title = element_text(size = 15),     # 设置图例标题字体大小（如果有）
#       axis.title.x = element_text(size = 15),     # 设置x轴标题字体大小
#       axis.title.y = element_text(size = 15),     # 设置y轴标题字体大小
#       axis.text = element_text(size = 15)          # 设置x轴和y轴刻度标签字体大小
#     ),
#   legend.labs = c("High risk score", "Low risk score"),    # 改变图例标签         
#   # xlim = c(0, 24), # x轴显示的时间范围,可不设置
#   palette = c("#E7B800", "#2E9FDF"),
#   risk.table.title = "Number at risk",
#   risk.table.fontsize = 6, # 设置风险表标题字体大小
#   title = "Early recurrence-free survival for internal test set", # 修改图的标题 
#   # training external internal  test set Early recurrence-free survival  Progression-free survival        
#   font.title = c(15), # 设置标题字体大小，加粗的话c(12, "bold")
#   font.legend = c(15), # 设置图例字体大小（备用）
#   font.x = c(15), # 设置x轴字体大小
#   font.y = c(15), # 设置y轴字体大小           
#   font.tickslab = c(15) # 设置刻度标签字体大小
# )

#%%方法2  不修改标签
library(survival)
library(survminer)
library(foreign) #导入外部数据
# survivalHCC<- read.csv("K:/2020-2023HCC/579hcc/模型代码总结/R-code/survivalHCC.csv")
survivalHCC <- read_excel("K:/2020-2023HCC/579hcc/clinical data/data/train30预后.xlsx")

fit <- survfit(Surv(PFS, status) ~ VETC, data = survivalHCC)
fit
ggsurvplot(fit,
           pval = TRUE, #在图上添加log rank检验的p值
           conf.int = TRUE,#添加置信区间
           break.time.by = 10,
           risk.table = TRUE, #在图下方添加风险表
           risk.table.col = "strata", # 根据数据分组为风险表添加颜色
           linetype = "strata", # 改变不同组别的生存曲线的线型
           surv.median.line = "hv", # 标注出中位生存时间
           ggtheme = theme_bw(), # 改变图形风格
           # xlim = c(0, 60), #x轴显示的时间范围,可不设置
           palette = c("#E7B800", "#2E9FDF"))#图形颜色风格


##survivalHCC.csv数据集
library(foreign) #导入外部数据
survivalHCC<- read.csv("K:/2020-2023HCC/579hcc/模型代码总结2/R-code/survivalHCC.csv")
head(survivalHCC)

fit <- survfit(Surv(time1, status1) ~ VETC, data = survivalHCC)
fit
ggsurvplot(fit,
           pval = TRUE, #在图上添加log rank检验的p值
           conf.int = TRUE,#添加置信区间
           break.time.by = 10,
           risk.table = TRUE, #在图下方添加风险表
           risk.table.col = "strata", # 根据数据分组为风险表添加颜色
           linetype = "strata", # 改变不同组别的生存曲线的线型
           surv.median.line = "hv", # 标注出中位生存时间
           ggtheme = theme_bw(), # 改变图形风格
           palette = c("#E7B800", "#2E9FDF"))#图形颜色风格


#R自带数据集
library(survminer)
fit <- survfit(Surv(time, status) ~ sex, data = lung)
ggsurvplot(fit,
           pval = TRUE, #在图上添加log rank检验的p值
           conf.int = TRUE,#添加置信区间
           risk.table = TRUE, #在图下方添加风险表
           risk.table.col = "strata", # 根据数据分组为风险表添加颜色
           linetype = "strata", # 改变不同组别的生存曲线的线型
           surv.median.line = "hv", # 标注出中位生存时间
           ggtheme = theme_bw(), # 改变图形风格
           palette = c("#E7B800", "#2E9FDF"))#图形颜色风格


ggsurvplot(
  fit,                    
  pval = FALSE,             
  conf.int = TRUE, 
  fun = "cumhaz",
  conf.int.style = "ribbon",  # 设置置信区间的风格
  xlab = "Time in days",   # 设置x轴标签
  break.time.by = 200,     # 将x轴按照200为间隔进行切分
  ggtheme = theme_light(), # 设置图形风格
  risk.table = "abs_pct",  # 在风险表中添加绝对数和相对数
  risk.table.y.text.col = TRUE,# 设置风险表的文字颜色
  risk.table.y.text = FALSE,# 以条柱展示风险表的标签，而非文字
  ncensor.plot = TRUE,      # 展示随访过程中不同时间点死亡和删失的情况
  surv.median.line = "hv",  # 添加中位生存时间
  legend.labs = 
    c("Male", "Female"),    # 改变图例标签
  palette = 
    c("#E7B800", "#2E9FDF") # 设置颜色
)

ggsurvplot(fit,
           conf.int = TRUE,
           risk.table.col = "strata", 
           ggtheme = theme_bw(), 
           palette = c("#E7B800", "#2E9FDF"),
           fun = "cumhaz")
dev.off()
