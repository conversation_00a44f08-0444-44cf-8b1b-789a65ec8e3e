{"cells": [{"cell_type": "code", "execution_count": 32, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "\"\u001b[1m\u001b[22mUse of `data$nomogram2` is discouraged.\n", "\u001b[36mℹ\u001b[39m Use `nomogram2` instead.\"\n"]}, {"data": {"image/png": "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*****************************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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["# 方法1 - 使用ggplot2包  OK\n", "library(ggplot2)\n", "library(readxl)\n", "data<-read_excel(\"D:/dataset/validation30.xlsx\")\n", "# data<-read.csv(\"C:/Users/<USER>/Desktop/ch05/lung.csv\")\n", "\n", "# 假设我们有一个名为data的数据框，包含ID，Handcrafted_pathomics_score和VETC列\n", "# 如果你的数据框的列名不同，请相应地调整这个代码\n", "\n", "# 创建一个新的列，用于决定每个条形的颜色\n", "data$color <- ifelse(data$VETC == 0, \"#66C2A5\", \"#FF6347\")\n", "\n", "# 制作瀑布图\n", "ggplot(data, aes(x = reorder(ID, -nomogram2), \n", "                 y = nomogram2, fill = color)) +\n", "  geom_bar(stat = \"identity\") +\n", "  scale_fill_identity() +\n", "  theme_minimal() +\n", "  labs(title = \"Deep learning risk scores\",\n", "       y = \" Deep learning score\", \n", "       x = \"ID\") +\n", "  theme(plot.title = element_text(hjust = 0.5), \n", "        axis.text.x = element_blank(),#隐去坐标轴文本\n", "        axis.ticks.x = element_blank(),#隐去坐标轴刻度\n", "        axis.line.x= element_blank(),#隐去横坐标轴\n", "        legend.position = \"right\",\n", "        panel.grid.major = element_blank(),\n", "        panel.grid.minor = element_blank()) +\n", "     #    geom_vline(xintercept = seq(0, max(data$nomogram2), by = 0.5), color = \"#100f0ff1\", linetype = \"solid\") # 添加竖线\n", "        geom_segment(aes(x = -Inf, y = 0, xend = -Inf, yend = max(data$nomogram2)), color = \"black\") + # 添加黑色实线    \n", "        scale_y_continuous(breaks = seq(0, max(data$nomogram2), by = 0.5)) # 设置y轴刻度\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tibble [54 × 3] (S3: tbl_df/tbl/data.frame)\n", " $ ID      : num [1:54] 1 2 3 4 5 6 7 8 9 10 ...\n", " $ Change  : num [1:54] 0.576 0.147 0.179 0.535 0.839 ...\n", " $ Response: num [1:54] 0 0 0 0 1 1 0 0 0 1 ...\n"]}, {"ename": "ERROR", "evalue": "\u001b[1m\u001b[33mError\u001b[39m in `scale_fill_brewer()`:\u001b[22m\n\u001b[1m\u001b[22m\u001b[33m!\u001b[39m Continuous values supplied to discrete scale.\n\u001b[36mℹ\u001b[39m Example values: \u001b[34m0\u001b[39m, \u001b[34m0\u001b[39m, \u001b[34m0\u001b[39m, \u001b[34m0\u001b[39m, and \u001b[34m1\u001b[39m\n", "output_type": "error", "traceback": ["\u001b[1m\u001b[33mError\u001b[39m in `scale_fill_brewer()`:\u001b[22m\n\u001b[1m\u001b[22m\u001b[33m!\u001b[39m Continuous values supplied to discrete scale.\n\u001b[36mℹ\u001b[39m Example values: \u001b[34m0\u001b[39m, \u001b[34m0\u001b[39m, \u001b[34m0\u001b[39m, \u001b[34m0\u001b[39m, and \u001b[34m1\u001b[39m\nTraceback:\n", "1. plot(p)", "2. plot.ggplot(p)", "3. ggplot_build(x)", "4. ggplot_build.ggplot(x)", "5. lapply(data, npscales$train_df)", "6. FUN(X[[i]], ...)", "7. train_df(..., self = self)", "8. lapply(self$scales, function(scale) scale$train_df(df = df))", "9. FUN(X[[i]], ...)", "10. scale$train_df(df = df)", "11. train_df(..., self = self)", "12. self$train(df[[aesthetic]])", "13. train(..., self = self)", "14. cli::cli_abort(c(\"Continuous values supplied to discrete scale.\", \n  .     i = \"Example values: {.and {.val {head(x, 5)}}}\"), call = self$call)", "15. rlang::abort(message, ..., call = call, use_cli_format = TRUE, \n  .     .frame = .frame)", "16. signal_abort(cnd, .file)"]}], "source": ["library(ggplot2)#绘图工具   未成功\n", "#读入数据，此例中数据的格式为xlsx\n", "library(readxl)\n", "mydata<-read_excel(\"K:/2020-2023HCC/579hcc/模型代码总结2/R-code/R代码修改版汇总/瀑布图/data.xlsx\")\n", "\n", "str(mydata)\n", "# 'data.frame':  45 obs. of  3 variables:\n", "# 'data.frame':  45 obs. of  3 variables:\n", "#  $ ID      : int  1 2 3 4 5 6 7 8 9 10 ...\n", "#  $ Change  : num  5 10 12 20 25 30 36 5 -6.8 -10 ...\n", "#  $ Response: chr  \"SD\" \"SD\" \"SD\" \"SD\" ...\n", "\n", " \n", "p<-ggplot(data =mydata,aes(x = reorder(ID,Change),y=Change,fill=Response))+ #对id按照Change重新排序后作为x值，change为y值，以response填充\n", "  geom_bar(stat =\"identity\",position=\"dodge\",width = 0.9)+#设置条形图参数\n", "  scale_fill_brewer(palette=\"Dark2\")+ #使用 scale_fill_brewer函数重新填充\n", "  xlab(NULL)+#这里我们不显示x轴标签\n", "  ylab(\"Percentage of Change from Baseline(%)\")+#纵坐标名称\n", "  ggtitle(\"Waterfall plot for clinical oncology data visualization\")+#使用ggtitle添加标题\n", "  theme_classic()+#应用经典主题\n", "  theme(legend.position =\"right\",#设置图例位置在右侧\n", "        legend.background = element_rect(fill=\"lightgrey\", size=0.5,linetype=\"solid\", colour =\"black\"),#设置图例背景参数\n", "        legend.title = element_text(colour=\"black\", size=10,face=\"bold\"),#图例标题加粗\n", "        axis.text.x = element_blank(),#隐去坐标轴文本\n", "        axis.ticks.x = element_blank(),#隐去坐标轴刻度\n", "        axis.line.x= element_blank(),#隐去横坐标轴\n", "        plot.title = element_text(colour=\"black\", size=12,face=\"bold\",hjust =0.5))+#设置标题参数\n", "\n", "  scale_y_continuous(limits = c(-2,5),breaks = seq(-2,5,15))+#更改纵坐标刻度值\n", "  geom_hline(aes(yintercept=20), colour=\"darkgrey\", linetype=\"dashed\",size=0.5)+#在y=20处添加一条水平线\n", "  geom_hline(aes(yintercept=0), colour=\"black\", linetype=\"solid\",size=0.5)+#在y=0处添加一条水平线\n", "  geom_hline(aes(yintercept=-30), colour=\"darkgrey\", linetype=\"dashed\",size=0.5)#在y=-30处添加一条水平线\n", "  \n", "plot(p)#打印图片\n", "# ggsave(\"waterfall plot.png\", plot = p, width = 6.5, height = 5, dpi = 600, units = \"in\", device='png')\n"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}