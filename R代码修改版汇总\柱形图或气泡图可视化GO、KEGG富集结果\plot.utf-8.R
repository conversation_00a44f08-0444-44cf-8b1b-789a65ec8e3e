#######柱形图
library(ggplot2)

#读取作图数据，这是一组上调基因的 GO 富集结果，展示了 top 富集的条目
go_enrich <- read.delim('GO.txt', sep = '\t', stringsAsFactors = TRUE)

#柱形图，纵坐标是 GO Term，横坐标是各 GO Term 的富集得分（Enrichment_score），颜色按 p 值着色
p <- ggplot(go_enrich, aes(Term, Enrichment_score)) +
geom_col(aes(fill = pValue), width = 0.5) +
scale_fill_gradient(high = 'blue', low = 'red') +
theme(panel.grid = element_blank(), panel.background = element_rect(color = 'black', fill = 'transparent')) +
scale_y_continuous(expand = expansion(mult = c(0, 0.1))) + 
coord_flip() +
labs(x = '', y = 'Enrichment Score')

p

#或者根据 Category 绘制分面图
p + facet_grid(Category~., scale = 'free_y', space = 'free_y') +
theme(legend.position = 'none')

#按 p 值由低到高排个序
go_enrich <- go_enrich[order(go_enrich$Category, go_enrich$pValue, decreasing = c(FALSE, FALSE)), ]
go_enrich$Term <- factor(go_enrich$Term, levels = go_enrich$Term)

#柱形图，横坐标 GO Term，纵坐标是 p 值的对数转换，颜色按 Category 着色
p1 <- ggplot(go_enrich, aes(Term, -log10(pValue))) +
geom_col(aes(fill = Category), width = 0.5) +
scale_fill_manual(values = c('#8EA1CB', '#67C1A5', '#FA8E61')) +
theme(panel.grid = element_blank(), panel.background = element_rect(color = 'black', fill = 'transparent'), 
	axis.text.x = element_text(angle = 60, hjust = 1, vjust = 1)) +
scale_y_continuous(expand = expansion(mult = c(0, 0.1))) + 
labs(x = '', y = '-Log10 P-Value\n')

p1

#或者根据 Category 绘制分面图
p1 + 
facet_grid(.~Category, scale = 'free_x', space = 'free_x') +
theme(legend.position = 'none')

#按 p 值由高到低排个序
go_enrich <- go_enrich[order(go_enrich$Category, go_enrich$pValue, decreasing = c(TRUE, TRUE)), ]
go_enrich$Term <- factor(go_enrich$Term, levels = go_enrich$Term)

#柱形图，横坐标 p 值的对数转换，纵坐标是 GO Term，颜色按 Category 着色
p2 <- ggplot(go_enrich, aes(Term, -log10(pValue))) +
geom_col(aes(fill = Category), width = 0.5) +
scale_fill_manual(values = c('#8EA1CB', '#67C1A5', '#FA8E61')) +
theme(panel.grid = element_blank(), panel.background = element_rect(color = 'black', fill = 'transparent')) +
scale_y_continuous(expand = expansion(mult = c(0, 0.1))) + 
coord_flip() +
labs(x = '', y = '-Log10 P-Value\n')

p2

#或者根据 Category 绘制分面图
p2 + facet_grid(Category~., scale = 'free_y', space = 'free_y') +
theme(legend.position = 'none')


#######气泡图
library(ggplot2)

#读取作图数据，这是一组上调基因的 GO 富集结果，展示了 top 富集的条目
go_enrich <- read.delim('GO.txt', sep = '\t', stringsAsFactors = FALSE)

#按富集得分由低到高排个序
go_enrich <- go_enrich[order(go_enrich$Enrichment_score, decreasing = FALSE), ]
go_enrich$Term <- factor(go_enrich$Term, levels = go_enrich$Term)

#气泡图，纵坐标是 GO Term，横坐标是各 GO Term 的富集得分（Enrichment_score）
#按各 GO Term 中富集的基因数量（Count）赋值气泡图中点的大小，颜色按 p 值着色
p <- ggplot(go_enrich, aes(Term, Enrichment_score)) +
geom_point(aes(size = Count, color = pValue)) +
scale_size(range = c(2, 6)) +
scale_color_gradient(high = 'blue', low = 'red') +
theme(panel.background = element_rect(color = 'black', fill = 'transparent'), 
    panel.grid = element_blank(), legend.key = element_blank()) +
coord_flip() +
labs(x = '', y = 'Enrichment Score')

p

#或者根据 Category 绘制分面图
p + facet_grid(Category~., scale = 'free_y', space = 'free_y')

#######气泡图，多组情况
go_enrich <- read.delim('GO.4group.txt', stringsAsFactors = FALSE)

#纵坐标是 GO Term，横坐标是各个分组比较
#按各 GO Term 中富集的基因数量（Count）赋值气泡图中点的大小，颜色按 p 值着色
ggplot(go_enrich, aes(Group, Term)) +
geom_point(aes(color = -log10(pValue), size = Count)) +
scale_size(range = c(2, 6)) +
scale_color_gradientn(colors = c('#55B047', '#FBA304', '#FF1900')) +
theme_bw() +
labs(x = '', y = '')
