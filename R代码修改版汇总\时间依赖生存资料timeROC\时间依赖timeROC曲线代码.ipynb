{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#在相应版本的R 如R 4.3或者3.5 控制台中运行以下命令来安装 IRkernel 包：\n", "install.packages('IRkernel')\n", "IRkernel::installspec()\n", "\n", "#install.packages('timeROC')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#安装rtools\n", "#install.packages(\"timeROC\", repos = \"https://cloud.r-project.org/\")\n", "# install.packages(\"mets\")\n", "# install.packages(\"ranger\")\n", "# install.packages(\"riskRegression\")\n", "# install.packages(\"timeROC\")\n", "#remove.packages(\"readxl\")\n", "#install.packages(\"tidyverse\")\n", "#install.packages(\"readxl\")  安装报错最终通过安装下载的readxl_1.4.3.zip解决的\n", "# install.packages(\"openxlsx\")\n", "# library(openxlsx)\n", "# data <- read.xlsx('D:/路径/文件名.xlsx')\n", "\n", "library(readxl)\n", "library(pec)\n", "library(timeROC)\n", "library(survival)\n", "library(MASS)\n", "\n", "#new_dat<- read_excel('D:/dataset/train30.xlsx')\n", "\n", "new_dat <- read_excel('K:/2020-2023HCC/579hcc/clinical data/data/validation30预后最终版.xlsx') # nolint\n", "head(new_dat)\n", "nrow(new_dat) \n", "length(new_dat$PFS)\n", "length(new_dat$nomogram)\n", "\n", "\n", "timeROC1 <-with(new_dat, timeROC(T=PFS,\n", "                               delta=status,\n", "                               marker=nomogram,\n", "                               cause=1,\n", "                               times=c(12,24,36,48),\n", "                               iid = TRUE))\n", "#identical(c(timeROC1$TP[,1],timeROC1$TP[,2],timeROC1$TP[,3]),as.numeric(timeROC1$TP))\n", "# 计算AUC值及其置信区间\n", "timeROC1$AUC\n", "confint(timeROC1,level = 0.95)$CI_AUC\n", "\n", "dat = data.frame(fpr = as.numeric(timeROC1$FP),\n", "                 tpr = as.numeric(timeROC1$TP),\n", "                 time = rep(as.factor(c(12,24,36,48)),each = nrow(timeROC1$TP)))\n", "\n", "library(ggplot2)\n", "ggplot() + \n", "  geom_line(data = dat,aes(x = fpr, y = tpr,color = time),size = 1) + \n", "  scale_color_manual(name = NULL,values = c(\"#92C5DE\", \"#F4A582\", \"#66C2A5\",\"#E78AC3\"),# \"#8DA0CB\"\n", "                     labels = paste0(\"AUC of \",c(1,2,3,4),\"-year survival: \",\n", "                                     format(round(timeROC1$AUC,3),nsmall = 2)))+\n", "  geom_line(aes(x=c(0,1),y=c(0,1)),color = \"grey\")+\n", "  theme_bw()+\n", "  theme(panel.grid = element_blank(),\n", "        legend.background = element_rect(linetype = 1, size = 0.2, colour = \"black\"),\n", "        legend.position = c(0.765,0.125))+\n", "  scale_x_continuous(expand = c(0.005,0.005))+\n", "  scale_y_continuous(expand = c(0.005,0.005))+\n", "  labs(title = \"Time-dependent ROC for training set\", # Training set  External test set  internal test set\n", "       x = \"1 - Specificity\",\n", "       y = \"Sensitivity\")+\n", "  coord_fixed()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#----早期复发的ROC\n", "library(readxl)\n", "library(pec)\n", "library(timeROC)\n", "library(survival)\n", "library(MASS)\n", "\n", "#new_dat<- read_excel('D:/dataset/train30.xlsx')\n", "\n", "new_dat <- read_excel('K:/2020-2023HCC/579hcc/clinical data/data/test预后最终版.xlsx')\n", "head(new_dat)\n", "nrow(new_dat) \n", "length(new_dat$PFS2)\n", "length(new_dat$nomogram2)\n", "\n", "\n", "timeROC1 <-with(new_dat, timeROC(T=PFS2,\n", "                                 delta=status2,\n", "                                 marker=nomogram,\n", "                                 cause=1,\n", "                                 times=c(12,24),\n", "                                 iid = TRUE))\n", "#identical(c(timeROC1$TP[,1],timeROC1$TP[,2],timeROC1$TP[,3]),as.numeric(timeROC1$TP))\n", "# 计算AUC值及其置信区间\n", "timeROC1$AUC\n", "confint(timeROC1,level = 0.95)$CI_AUC\n", "\n", "dat = data.frame(fpr = as.numeric(timeROC1$FP),\n", "                 tpr = as.numeric(timeROC1$TP),\n", "                 time = rep(as.factor(c(12,24)),each = nrow(timeROC1$TP)))\n", "\n", "library(ggplot2)\n", "ggplot() + \n", "  geom_line(data = dat,aes(x = fpr, y = tpr,color = time),size = 1) + \n", "  scale_color_manual(name = NULL,values = c(\"#92C5DE\", \"#F4A582\", \"#66C2A5\",\"#E78AC3\"),# \"#8DA0CB\"\n", "                     labels = paste0(\"AUC of \",c(1,2,3,4),\"-year survival: \",\n", "                                     format(round(timeROC1$AUC,3),nsmall = 2)))+\n", "  geom_line(aes(x=c(0,1),y=c(0,1)),color = \"grey\")+\n", "  theme_bw()+\n", "  theme(panel.grid = element_blank(),\n", "        legend.background = element_rect(linetype = 1, size = 0.2, colour = \"black\"),\n", "        legend.position = c(0.765,0.125))+\n", "  scale_x_continuous(expand = c(0.005,0.005))+\n", "  scale_y_continuous(expand = c(0.005,0.005))+\n", "  labs(title = \"Time-dependent ROC for external test set\", # Training set  external test set  internal test set\n", "       x = \"1 - Specificity\",\n", "       y = \"Sensitivity\")+\n", "  coord_fixed()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#-------比较两个time-dependent AUC  已运行过\n", "\n", "timeroc2 <- with(new_dat,timeROC(T = PFS,delta = status,marker = nomogram,\n", "  cause = 1,weighting=\"marginal\", times = c(12,24,36,48),ROC = TRUE,iid = TRUE))\n", "  #times = c(12,24,36),ROC = TRUE,iid = TRUE)\n", "\n", "timeroc2$AUC\n", "confint(timeroc2,level = 0.95)$CI_AUC\n", "\n", "compare(timeROC1,timeroc2,adjusted = TRUE)$p_values_AUC\n", "\n", "plotAUCcurve(timeROC1, conf.int=TRUE, col=\"red\")\n", "plotAUCcurve(timeroc2, conf.int=TRUE, col=\"blue\", add=TRUE)\n", "legend(\"bottomright\",c(\"VETC\", \"RFinter\"), col = c(\"red\",\"blue\"), lty=1, lwd=2)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}