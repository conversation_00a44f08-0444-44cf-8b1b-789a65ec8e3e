rm(list = ls())
### R包载入：
library(org.Hs.eg.db)
library(clusterProfiler)
library(pathview)
library(topGO)
library(ggplot2)

# 读取文件，差异分析结果：
degs <- read.csv("gene.csv")

DEG_list <- as.character(degs[,1])

#################################
# GO富集分析：
ego <- enrichGO(gene          = DEG_list,
                OrgDb         = org.Hs.eg.db,
                keyType = "SYMBOL",
                ont           = "ALL",
                pAdjustMethod = "BH",
                pvalueCutoff  = 0.01,
                qvalueCutoff  = 0.01)

# 保存富集分析结果：
write.table(ego, file = "Go_Enrichment.txt",sep="\t", row.names =F, quote = F)


# 转换为data.frame,方便查看：
ego_results<-as.data.frame(ego)

pdf("GO_barplot.pdf",height = 8, width = 9)
## 最简单的方法绘制柱状图：直接barplot就可以搞定：
barplot(ego, showCategory=10, x = "GeneRatio")
dev.off()

pdf("GO_dotplot.pdf",height = 8, width = 9)
## 最简单的方法绘制柱状图：直接dotplot就可以搞定：
dotplot(ego,showCategory=10)
dev.off()




#################################
# KEGG富集分析：
# 先转换ID：将gene名转换为ENTREZID
eg = bitr(DEG_list, fromType="SYMBOL", toType="ENTREZID", OrgDb="org.Hs.eg.db")

kegg <- enrichKEGG(
  gene = eg$ENTREZID,
  keyType = "kegg",
  organism  = 'human',
  pvalueCutoff  = 0.05,
  pAdjustMethod  = "BH",
  qvalueCutoff  = 0.05
)

# 保存富集分析结果：
write.table(kegg, file = "kegg_Enrichment.txt",sep="\t", row.names =F, quote = F)


# 转换为data.frame,方便查看：
kegg_results<-as.data.frame(kegg)

pdf("KEGG_barplot.pdf",height = 8, width = 9)
## 最简单的方法绘制柱状图：直接barplot就可以搞定：
barplot(kegg, showCategory=10, x = "GeneRatio")
dev.off()

pdf("KEGG_dotplot.pdf",height = 8, width = 9)
## 最简单的方法绘制柱状图：直接dotplot就可以搞定：
dotplot(kegg,showCategory=10)
dev.off()