##
##install.packages("rms")
# install.packages("nomogramFormula")
##install.packages("rms")
#%%
library(foreign) 
library(rms)
library(foreign) #导入外部数据
# data2<- read.csv("C:/Users/<USER>/Desktop/ch05/data2.csv")
# mydata<-as.data.frame(data2)
# head(mydata)

library(readxl)
file_path <- "H:\\1.HCC-dataset\\850HCC\\all-HCC\\734hcctumor\\data\\临床-影像学特征\\AIC6个特征建模\\train_AIC_BIC后.xlsx"

# 导入整个文件
data2 <- read_excel(file_path)
mydata<-as.data.frame(data2)
head(mydata)

dd<-datadist(mydata)
options(datadist='dd')

fit1<-lrm(PHCC~age+ALT+AST+AFP+diameter+number,data=mydata,x=T,y=T) #group为因变量，x为自变量,如结果提示变量有问题，可删除变量之一再试试
fit1 
summary(fit1)

nom1 <- nomogram(fit1, fun=plogis,fun.at=c(seq(.1,.9, by=.1), .95),lp=F, funlabel="risk of group2")
plot(nom1,fun.side=c(1,3,1,3,1,3,1,3,1,1),label.every=2)

nom1 <- nomogram(fit1, fun=plogis,fun.at=c(.01, .05, seq(.1,.9, by=.1), .95),lp=F, funlabel="Risk of severe COVID-19")
plot(nom1,fun.side=c(1,3,1,3,1,3,1,3,1,3,1,3),label.every=1)

cal1 <- calibrate(fit1, cmethod='hare', method='boot', B=1000)
plot(cal1,xlim=c(0,1.0),ylim=c(0,1.0))

#计算每个变量的得分
print(nom1)

#计算每个患者的总得分：
library(nomogramFormula)
results <- formula_rd(nomogram = nom1)
results$formula
##               b0       x^1
## sex    -85.87254 85.872536
## age     93.33333 -0.666667
## weight -72.65806  1.453161
mydata  = na.omit(mydata)  #去掉缺失值
mydata$points = points_cal(formula = results$formula,rd = mydata)
head(mydata)

#%% 校准曲线加上颜色
cal1 <- calibrate(fit1, method='boot', B=1000)
plot(cal1,xlim=c(0,1.0),ylim=c(0,1.0))

plot(cal1,xlim = c(0,1),ylim=c(0,1.0),xlab = "Predicted Probability",ylab="Observed Probability", legend =F,subtitles = T,

abline(0,1,col="black",lty=2,lwd=2))#对角线设为虚线

lines(cal1[,c("predy","calibrated.orig")],type="l",lwd=2,col="red",pch=16)

lines(cal1[,c("predy","calibrated.corrected")],type="l",lwd=2,col="#2600ff",pch=16)

legend(0.55,0.4,       
       c("Ideal","Apparent","Bias-corrected"),       
       lty = c(2,1,1),     
       lwd = c(2,1,1),      
       col = c("black","red","#2f00ff"),     
       bty="n")

mtext("")

box(lwd = 1)