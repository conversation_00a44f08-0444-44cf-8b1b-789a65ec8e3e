rm(list = ls())
library(pheatmap)

data <- as.matrix(read.table("data.txt",row.names = 1,header = T,sep = "\t"))

# 构建列注释信息
annotation_col = data.frame(CellType = factor(c("A","B","C","D","E",
                                                "F","G","H","I","J"))
                            #Sex = factor(rep(c("F","M"),5))
                            )

rownames(annotation_col) <- colnames(data)
head(annotation_col)

# 构建行注释信息
annotation_row = data.frame(
  GeneClass = factor(rep(c("Path1", "Path2", "Path3"), c(6, 6, 6)))
)
rownames(annotation_row) = rownames(data)
head(annotation_row)


# 自定注释信息的颜色列表
ann_colors = list(
  # Sex = c(F = "red", M = "#016D06"),
  CellType = c(A = "#65B99F", B = "#F08961", C = "#8A9BC3", D = "#DA85B5", E = "#A1CC56",
               F = "#F5D239", G = "#7CC07B", H = "#BAABD0", I = "#3766A4", J = "#DF3078")
  # GeneClass = c(Path1 = "#7570B3", Path2 = "#E7298A", Path3 = "#66A61E")
)
head(ann_colors)

pheatmap(data,
         # 去掉聚类树：
         cluster_cols = FALSE,
         cluster_rows = FALSE,
         # 加color bar：列注释信息；
         annotation_col = annotation_col,
         # 行注释信息：
         # annotation_row = annotation_row,
         # color bar 颜色设定：
         annotation_colors = ann_colors,
         # 设置单元格颜色渐变；(100)表示分100段渐变；
         color = colorRampPalette(c("#FDEBEA","#D5281F"))(100), 
         # 行、列标签的字体大小
         fontsize_col = 8,
         fontsize_row = 10,
         # 是否显示行、列名
         show_colnames = F,
         # 设置每个单元格的宽度和高度
         cellwidth = 30, 
         cellheight = 24,
         # 行、列聚类树的高度：
         # treeheight_row = 50, 
         # treeheight_col = 30,
         # display_numbers = TRUE参数设定在每个热图格子中显示相应的数值，
         # number_color参数设置数值字体的颜色
         display_numbers = TRUE,number_color = "black",
         # 设置标题：
         main = "Heatmap")
