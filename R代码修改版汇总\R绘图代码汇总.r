#%% R绘制弦图
# install.packages("circlize")  # 安装
# install.packages("readxl")  
library(circlize) # 载入
library(readxl)

# 设置文件路径
file_path <- "H:/1.HCC-VETC/734HCC/all-HCC/734hcctumor/data/临床-影像学特征/AIC6个特征建模/train_AIC_BIC后.xlsx"

# 导入整个文件
data <- read_excel(file_path)

# 转换为数据框格式
data_df <- as.data.frame(data)

# 提取第2列为标签，第3列及之后的列为变量
dfnew <- data_df[, 2:ncol(data_df)]  # 从第2列开始提取

# 查看数据框的前几行
head(dfnew)

# 提取标签和变量
labels <- dfnew[[1]]  # 第2列为标签
variables <- dfnew[, 2:ncol(dfnew)]  # 第3列及之后的列为变量

# 创建邻接矩阵
data_matrix <- as.matrix(variables)
chordDiagram(data_matrix)
circos.clear() # 还原所有参数
