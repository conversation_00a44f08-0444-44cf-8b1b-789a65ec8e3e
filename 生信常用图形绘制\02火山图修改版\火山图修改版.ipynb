{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>'Protein'</li><li>'Protein.Name'</li><li>'Genename'</li><li>'Function'</li><li>'ProteinGroup'</li><li>'FastaHeaders'</li><li>'Proteins'</li><li>'Peptides'</li><li>'RazorUniquePeptides'</li><li>'UniquePeptides'</li><li>'Coverage'</li><li>'MolWeight'</li><li>'LFQ.intensity.gushidai.1'</li><li>'LFQ.intensity.lilongfeng.1'</li><li>'LFQ.intensity.shenli.1'</li><li>'LFQ.intensity.sunxing.1'</li><li>'LFQ.intensity.weizhengshu.1'</li><li>'LFQ.intensity.zhangpingai.1'</li><li>'LFQ.intensity.zhourongchang.1'</li><li>'average.MVI.P'</li><li>'LFQ.intensity.huyunzhen.0'</li><li>'LFQ.intensity.lixiaonan.0'</li><li>'LFQ.intensity.panzhihua.0'</li><li>'LFQ.intensity.qinliqiang.0'</li><li>'LFQ.intensity.xuhuolai.0'</li><li>'LFQ.intensity.yemeitou.0'</li><li>'LFQ.intensity.yuanguiwei.0'</li><li>'LFQ.intensity.zhousanman.0'</li><li>'average.MVI.N'</li><li>'MVI.P.MVI.N'</li><li>'pvalue'</li><li>'log2FC'</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 'Protein'\n", "\\item 'Protein.Name'\n", "\\item 'Genename'\n", "\\item 'Function'\n", "\\item 'ProteinGroup'\n", "\\item 'FastaHeaders'\n", "\\item 'Proteins'\n", "\\item 'Peptides'\n", "\\item 'RazorUniquePeptides'\n", "\\item 'UniquePeptides'\n", "\\item 'Coverage'\n", "\\item 'MolWeight'\n", "\\item 'LFQ.intensity.gushidai.1'\n", "\\item 'LFQ.intensity.lilongfeng.1'\n", "\\item 'LFQ.intensity.shenli.1'\n", "\\item 'LFQ.intensity.sunxing.1'\n", "\\item 'LFQ.intensity.weizhengshu.1'\n", "\\item 'LFQ.intensity.zhangpingai.1'\n", "\\item 'LFQ.intensity.zhourongchang.1'\n", "\\item 'average.MVI.P'\n", "\\item 'LFQ.intensity.huyunzhen.0'\n", "\\item 'LFQ.intensity.lixiaonan.0'\n", "\\item 'LFQ.intensity.panzhihua.0'\n", "\\item 'LFQ.intensity.qinliqiang.0'\n", "\\item 'LFQ.intensity.xuhuolai.0'\n", "\\item 'LFQ.intensity.yemeitou.0'\n", "\\item 'LFQ.intensity.yuanguiwei.0'\n", "\\item 'LFQ.intensity.zhousanman.0'\n", "\\item 'average.MVI.N'\n", "\\item 'MVI.P.MVI.N'\n", "\\item 'pvalue'\n", "\\item 'log2FC'\n", "\\end{enumerate*}\n"], "text/markdown": ["1. '<PERSON><PERSON>'\n", "2. 'Protein.Name'\n", "3. 'Genename'\n", "4. 'Function'\n", "5. 'ProteinGroup'\n", "6. '<PERSON><PERSON><PERSON><PERSON><PERSON>'\n", "7. '<PERSON>tein<PERSON>'\n", "8. 'Peptides'\n", "9. 'RazorUniquePeptides'\n", "10. 'UniquePeptides'\n", "11. 'Coverage'\n", "12. 'Mo<PERSON>Weight'\n", "13. 'LFQ.intensity.gushidai.1'\n", "14. 'LFQ.intensity.lilongfeng.1'\n", "15. 'LFQ.intensity.shenli.1'\n", "16. 'LFQ.intensity.sunxing.1'\n", "17. 'LFQ.intensity.weizhengshu.1'\n", "18. 'LFQ.intensity.zhangpingai.1'\n", "19. 'LFQ.intensity.zhourongchang.1'\n", "20. 'average.MVI.P'\n", "21. 'LFQ.intensity.huyunzhen.0'\n", "22. 'LFQ.intensity.lixiaonan.0'\n", "23. 'LFQ.intensity.panzhihua.0'\n", "24. 'LFQ.intensity.qinliqiang.0'\n", "25. 'LFQ.intensity.xuhuolai.0'\n", "26. 'LFQ.intensity.yemeitou.0'\n", "27. 'LFQ.intensity.yuanguiwei.0'\n", "28. 'LFQ.intensity.zhousanman.0'\n", "29. 'average.MVI.N'\n", "30. 'MVI.P.MVI.N'\n", "31. 'pvalue'\n", "32. 'log2FC'\n", "\n", "\n"], "text/plain": [" [1] \"Protein\"                       \"Protein.Name\"                 \n", " [3] \"Genename\"                      \"Function\"                     \n", " [5] \"ProteinGroup\"                  \"FastaHeaders\"                 \n", " [7] \"Proteins\"                      \"Peptides\"                     \n", " [9] \"RazorUniquePeptides\"           \"UniquePeptides\"               \n", "[11] \"Coverage\"                      \"MolWeight\"                    \n", "[13] \"LFQ.intensity.gushidai.1\"      \"LFQ.intensity.lilongfeng.1\"   \n", "[15] \"LFQ.intensity.shenli.1\"        \"LFQ.intensity.sunxing.1\"      \n", "[17] \"LFQ.intensity.weizhengshu.1\"   \"LFQ.intensity.zhangpingai.1\"  \n", "[19] \"LFQ.intensity.zhourongchang.1\" \"average.MVI.P\"                \n", "[21] \"LFQ.intensity.huyunzhen.0\"     \"LFQ.intensity.lixiaonan.0\"    \n", "[23] \"LFQ.intensity.panzhihua.0\"     \"LFQ.intensity.qinliqiang.0\"   \n", "[25] \"LFQ.intensity.xuhuolai.0\"      \"LFQ.intensity.yemeitou.0\"     \n", "[27] \"LFQ.intensity.yuanguiwei.0\"    \"LFQ.intensity.zhousanman.0\"   \n", "[29] \"average.MVI.N\"                 \"MVI.P.MVI.N\"                  \n", "[31] \"pvalue\"                        \"log2FC\"                       "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style>\n", ".list-inline {list-style: none; margin:0; padding: 0}\n", ".list-inline>li {display: inline-block}\n", ".list-inline>li:not(:last-child)::after {content: \"\\00b7\"; padding: 0 .5ex}\n", "</style>\n", "<ol class=list-inline><li>'Protein'</li><li>'Protein.Name'</li><li>'Genename'</li><li>'Function'</li><li>'ProteinGroup'</li><li>'FastaHeaders'</li><li>'Proteins'</li><li>'Peptides'</li><li>'RazorUniquePeptides'</li><li>'UniquePeptides'</li><li>'Coverage'</li><li>'MolWeight'</li><li>'LFQ.intensity.gushidai.1'</li><li>'LFQ.intensity.lilongfeng.1'</li><li>'LFQ.intensity.shenli.1'</li><li>'LFQ.intensity.sunxing.1'</li><li>'LFQ.intensity.weizhengshu.1'</li><li>'LFQ.intensity.zhangpingai.1'</li><li>'LFQ.intensity.zhourongchang.1'</li><li>'average.MVI.P'</li><li>'LFQ.intensity.huyunzhen.0'</li><li>'LFQ.intensity.lixiaonan.0'</li><li>'LFQ.intensity.panzhihua.0'</li><li>'LFQ.intensity.qinliqiang.0'</li><li>'LFQ.intensity.xuhuolai.0'</li><li>'LFQ.intensity.yemeitou.0'</li><li>'LFQ.intensity.yuanguiwei.0'</li><li>'LFQ.intensity.zhousanman.0'</li><li>'average.MVI.N'</li><li>'MVI.P.MVI.N'</li><li>'padj'</li><li>'log2FoldChange'</li><li>'regulate'</li></ol>\n"], "text/latex": ["\\begin{enumerate*}\n", "\\item 'Protein'\n", "\\item 'Protein.Name'\n", "\\item 'Genename'\n", "\\item 'Function'\n", "\\item 'ProteinGroup'\n", "\\item 'FastaHeaders'\n", "\\item 'Proteins'\n", "\\item 'Peptides'\n", "\\item 'RazorUniquePeptides'\n", "\\item 'UniquePeptides'\n", "\\item 'Coverage'\n", "\\item 'MolWeight'\n", "\\item 'LFQ.intensity.gushidai.1'\n", "\\item 'LFQ.intensity.lilongfeng.1'\n", "\\item 'LFQ.intensity.shenli.1'\n", "\\item 'LFQ.intensity.sunxing.1'\n", "\\item 'LFQ.intensity.weizhengshu.1'\n", "\\item 'LFQ.intensity.zhangpingai.1'\n", "\\item 'LFQ.intensity.zhourongchang.1'\n", "\\item 'average.MVI.P'\n", "\\item 'LFQ.intensity.huyunzhen.0'\n", "\\item 'LFQ.intensity.lixiaonan.0'\n", "\\item 'LFQ.intensity.panzhihua.0'\n", "\\item 'LFQ.intensity.qinliqiang.0'\n", "\\item 'LFQ.intensity.xuhuolai.0'\n", "\\item 'LFQ.intensity.yemeitou.0'\n", "\\item 'LFQ.intensity.yuanguiwei.0'\n", "\\item 'LFQ.intensity.zhousanman.0'\n", "\\item 'average.MVI.N'\n", "\\item 'MVI.P.MVI.N'\n", "\\item 'padj'\n", "\\item 'log2FoldChange'\n", "\\item 'regulate'\n", "\\end{enumerate*}\n"], "text/markdown": ["1. '<PERSON><PERSON>'\n", "2. 'Protein.Name'\n", "3. 'Genename'\n", "4. 'Function'\n", "5. 'ProteinGroup'\n", "6. '<PERSON><PERSON><PERSON><PERSON><PERSON>'\n", "7. '<PERSON>tein<PERSON>'\n", "8. 'Peptides'\n", "9. 'RazorUniquePeptides'\n", "10. 'UniquePeptides'\n", "11. 'Coverage'\n", "12. 'Mo<PERSON>Weight'\n", "13. 'LFQ.intensity.gushidai.1'\n", "14. 'LFQ.intensity.lilongfeng.1'\n", "15. 'LFQ.intensity.shenli.1'\n", "16. 'LFQ.intensity.sunxing.1'\n", "17. 'LFQ.intensity.weizhengshu.1'\n", "18. 'LFQ.intensity.zhangpingai.1'\n", "19. 'LFQ.intensity.zhourongchang.1'\n", "20. 'average.MVI.P'\n", "21. 'LFQ.intensity.huyunzhen.0'\n", "22. 'LFQ.intensity.lixiaonan.0'\n", "23. 'LFQ.intensity.panzhihua.0'\n", "24. 'LFQ.intensity.qinliqiang.0'\n", "25. 'LFQ.intensity.xuhuolai.0'\n", "26. 'LFQ.intensity.yemeitou.0'\n", "27. 'LFQ.intensity.yuanguiwei.0'\n", "28. 'LFQ.intensity.zhousanman.0'\n", "29. 'average.MVI.N'\n", "30. 'MVI.P.MVI.N'\n", "31. 'padj'\n", "32. 'log2FoldChange'\n", "33. 'regulate'\n", "\n", "\n"], "text/plain": [" [1] \"Protein\"                       \"Protein.Name\"                 \n", " [3] \"Genename\"                      \"Function\"                     \n", " [5] \"ProteinGroup\"                  \"FastaHeaders\"                 \n", " [7] \"Proteins\"                      \"Peptides\"                     \n", " [9] \"RazorUniquePeptides\"           \"UniquePeptides\"               \n", "[11] \"Coverage\"                      \"MolWeight\"                    \n", "[13] \"LFQ.intensity.gushidai.1\"      \"LFQ.intensity.lilongfeng.1\"   \n", "[15] \"LFQ.intensity.shenli.1\"        \"LFQ.intensity.sunxing.1\"      \n", "[17] \"LFQ.intensity.weizhengshu.1\"   \"LFQ.intensity.zhangpingai.1\"  \n", "[19] \"LFQ.intensity.zhourongchang.1\" \"average.MVI.P\"                \n", "[21] \"LFQ.intensity.huyunzhen.0\"     \"LFQ.intensity.lixiaonan.0\"    \n", "[23] \"LFQ.intensity.panzhihua.0\"     \"LFQ.intensity.qinliqiang.0\"   \n", "[25] \"LFQ.intensity.xuhuolai.0\"      \"LFQ.intensity.yemeitou.0\"     \n", "[27] \"LFQ.intensity.yuanguiwei.0\"    \"LFQ.intensity.zhousanman.0\"   \n", "[29] \"average.MVI.N\"                 \"MVI.P.MVI.N\"                  \n", "[31] \"padj\"                          \"log2FoldChange\"               \n", "[33] \"regulate\"                     "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["#火山图3个类型，运行成功\n", "# install.packages(\"devtools\") \n", "# devtools::install_github(\"BioSenior/ggvolcano\")\n", "library(ggVolcano) \n", "# data(deg_data)\n", "data <- read.csv(\"D:/R-code/promics_data.csv\")\n", "colnames(data)\n", "\n", "data2 <- add_regulate(data, log2FC_name = \"log2FC\",              \n", "                     fdr_name = \"pvalue\",log2FC = 1, fdr =  0.05) #fdr为p值；这行代码会自动把log2FC和pvalue转换成log2FoldChange和padj\n", "colnames(data2)\n", "# str(data2)\n", "# write.csv(data2, file = \"D:/R-code/data2_export.csv\", row.names = FALSE)\n", "\n", "#图1\n", "p1 <-ggvolcano(data2, x = \"log2FoldChange\", y = \"padj\",     #x和y的名字不需要改，\n", "          #x_lab = \"log2FC\",\n", "          y_lab = \"-log10(P-value)\",  \n", "          log2FC_cut = 1, #FC>2 或 FC<0.5 对应的logFC为1和-1\n", "          FDR_cut = 0.05, #p<0.05\n", "          label = \"Genename\", label_number = 20, output = FALSE)   #只需要改label\n", "p1 + coord_cartesian(xlim = c(-6, 6))\n", "\n", "#图2\n", "library(patchwork)\n", "p2 <- ggvolcano(data2, x = \"log2FoldChange\", y = \"padj\",\n", "                fills = c(\"#e94234\",\"#b4b4d8\",\"#269846\"),\n", "                colors = c(\"#e94234\",\"#b4b4d8\",\"#269846\"),\n", "                label = \"Genename\", label_number = 20, output = FALSE)\n", "p2 + coord_cartesian(xlim = c(-6, 6))\n", "\n", "#图3\n", "library(RColorBrewer)\n", "library(patchwork)\n", "p3 <- gradual_volcano(data2, x = \"log2FoldChange\", y = \"padj\",\n", "                      label = \"Genename\", label_number = 20, output = FALSE)\n", "p3 + coord_cartesian(xlim = c(-6, 6))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["## 我的数据\n", "library(ggplot2)\n", "\n", "# 读取数据：\n", "\n", "data <- read.csv(\"D:/R-code/promics_data.csv\",row.names = 1)\n", "data$label <- c(rownames(data)[1:20],rep(NA,(nrow(data)-20)))\n", "\n", "ggplot(data,aes(log2FC, -log10(pvalue)))+\n", "  # 横向水平参考线：\n", "  geom_hline(yintercept = -log10(0.05), linetype = \"dashed\", color = \"#999999\")+\n", "  # 纵向垂直参考线：\n", "  geom_vline(xintercept = c(-1.2,1.2), linetype = \"dashed\", color = \"#999999\")+\n", "  # 散点图:\n", "  geom_point(aes(size=-log10(pvalue), color= -log10(pvalue)))+\n", "  # 指定颜色渐变模式：\n", "  scale_color_gradientn(values = seq(0,1,0.2),\n", "                        colors = c(\"#39489f\",\"#39bbec\",\"#f9ed36\",\"#f38466\",\"#b81f25\"))+\n", "  # 指定散点大小渐变模式：\n", "  scale_size_continuous(range = c(1,3))+\n", "  # 主题调整：\n", "  theme_bw()+\n", "  # 调整主题和图例位置：\n", "  theme(panel.grid = element_blank(),\n", "        legend.position = c(0.01,0.7),\n", "        legend.justification = c(0,1)\n", "        )+\n", "  # 设置部分图例不显示：\n", "  guides(col = guide_colourbar(title = \"-Log10(p-value)\"),\n", "         size = \"none\")+\n", "  # 添加标签：\n", "  geom_text(aes(label=label, color = -log10(pvalue)), size = 3, vjust = 1.5, hjust=3.5)+\n", "  # 修改坐标轴：\n", "  xlab(\"Log2FC\")+\n", "  ylab(\"-Log10(p-value)\")\n", "\n", "# 保存图片：\n", "ggsave(\"D:/R-code/vocanol_plot.tiff\", height = 9, width = 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["##课程数据\n", "library(ggplot2)\n", "\n", "# 读取数据：\n", "\n", "data <- read.csv(\"D:/dataset/data02.csv\",row.names = 1)\n", "data$label <- c(rownames(data)[1:10],rep(NA,(nrow(data)-10)))\n", "\n", "ggplot(data,aes(log2FoldChange, -log10(padj)))+\n", "  # 横向水平参考线：\n", "  geom_hline(yintercept = -log10(0.05), linetype = \"dashed\", color = \"#999999\")+\n", "  # 纵向垂直参考线：\n", "  geom_vline(xintercept = c(-1.2,1.2), linetype = \"dashed\", color = \"#999999\")+\n", "  # 散点图:\n", "  geom_point(aes(size=-log10(padj), color= -log10(padj)))+\n", "  # 指定颜色渐变模式：\n", "  scale_color_gradientn(values = seq(0,1,0.2),\n", "                        colors = c(\"#39489f\",\"#39bbec\",\"#f9ed36\",\"#f38466\",\"#b81f25\"))+\n", "  # 指定散点大小渐变模式：\n", "  scale_size_continuous(range = c(1,3))+\n", "  # 主题调整：\n", "  theme_bw()+\n", "  # 调整主题和图例位置：\n", "  theme(panel.grid = element_blank(),\n", "        legend.position = c(0.01,0.7),\n", "        legend.justification = c(0,1)\n", "        )+\n", "  # 设置部分图例不显示：\n", "  guides(col = guide_colourbar(title = \"-Log10(p-value)\"),\n", "         size = \"none\")+\n", "  # 添加标签：\n", "  geom_text(aes(label=label, color = -log10(padj)), size = 3, vjust = 1.5, hjust=1)+\n", "  # 修改坐标轴：\n", "  xlab(\"Log2FC\")+\n", "  ylab(\"-Log10(p-value)\")\n", "\n", "# 保存图片：\n", "# ggsave(\"vocanol_plot.pdf\", height = 9, width = 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# 安装并加载 readxl 包\n", "# install.packages(\"readxl\")\n", "library(readxl)\n", "\n", "# # 读取Excel文件\n", "data <- read_excel(\"D:/R-code/huoshantu/promics_data.xlsx\")\n", "colnames(data)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#蛋白质组学数据   火山图\n", "data <- read.csv(\"D:/R-code/promics_data.csv\") #\"row.names = 1\"表示将第一列作为数据框的行名。\n", "colnames(data)\n", "\n", "color <- rep(\"#999999\",nrow(data))\n", "color[data$pvalue <0.05 & data$log2FC > 1] <- \"#FC4E07\"\n", "color[data$pvalue <0.05 & data$log2FC < -1] <- \"#00AFBB\"\n", "\n", "par(oma = c(0,2,0,0))\n", "\n", "plot(data$log2FC,-log10(data$pvalue),pch = 16,cex = 0.5, \n", "     xlim = c(-5,5), ylim = c(0,4), col = color, frame.plot = F,\n", "     xlab = \"log2FC\", ylab = \"-log10(P-value)\", cex.axis = 1, cex.lab = 1.3)\n", "   \n", "# 添加参考线：\n", "abline(h = -log10(0.05),lwd = 2, lty = 3)  # lwd设置线的宽度，lty设置线的类型；\n", "abline(v = c(-1,1),lwd = 2, lty = 3)  # lwd设置线的宽度，lty设置线的类型；\n", "\n", "# 添加图例\n", "legend(x = 3, y = 4, legend = c(\"Up\",\"Normal\",\"Down\"), \n", "       bty = \"n\", # 去除边框\n", "       pch = 19,cex = 1, # 设置点的样式和大小\n", "       x.intersp = 0.6, # 设置字与点之间的距离；\n", "       y.intersp = 0.6, # 设置点与点的高度差，相当于行距；\n", "       col = c(\"#FC4E07\",\"#999999\",\"#00AFBB\"))\n", "\n", "# 添加标签：\n", "color = c()\n", "color[which(data[1:20,]$regulate == \"Up\")] = \"#FC4E07\"\n", "color[which(data[1:20,]$regulate == \"Down\")] = \"#00AFBB\"\n", "\n", "text(data$log2FC[1:20],-log10(data$pvalue)[1:20],\n", "     labels = data$Genename[1:20],\n", "     adj = c(0,1.5),\n", "     cex = 0.6,\n", "     col = color)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["\n", "#课程数据1\n", "data <- read.csv(\"D:/R-code/huoshantu/DEG.csv\")#row.names = 1\n", "\n", "color <- rep(\"#999999\",nrow(data))\n", "\n", "color[data$pvalue <0.05 & data$log2FoldChange > 1] <- \"#FC4E07\"\n", "color[data$pvalue <0.05 & data$log2FoldChange < -1] <- \"#00AFBB\"\n", "\n", "par(oma = c(0,2,0,0))\n", "\n", "plot(data$log2FoldChange,-log10(data$pvalue),pch = 16,cex = 0.5,\n", "     xlim = c(-4,4), ylim = c(0,32), col = color, frame.plot = F,\n", "     xlab = \"log2FC\", ylab = \"-log10(Pvalue)\", cex.axis = 1, cex.lab = 1.3)\n", "\n", "# 添加参考线：\n", "abline(h = -log10(0.05),lwd = 2, lty = 3)  # lwd设置线的宽度，lty设置线的类型；\n", "abline(v = c(-1,1),lwd = 2, lty = 3)  # lwd设置线的宽度，lty设置线的类型；\n", "\n", "# 添加图例\n", "legend(x = 3, y = 32, legend = c(\"Up\",\"Normal\",\"Down\"), \n", "       bty = \"n\", # 去除边框\n", "       pch = 19,cex = 1, # 设置点的样式和大小\n", "       x.intersp = 0.6, # 设置字与点之间的距离；\n", "       y.intersp = 0.6, # 设置点与点的高度差，相当于行距；\n", "       col = c(\"#FC4E07\",\"#999999\",\"#00AFBB\"))\n", "\n", "# 添加标签：\n", "color = c()\n", "color[which(data[1:10,]$regulate == \"Up\")] = \"#FC4E07\"\n", "color[which(data[1:10,]$regulate != \"Up\")] = \"#00AFBB\"\n", "text(data$log2FoldChange[1:10],-log10(data$pvalue)[1:10],\n", "     labels = data$row[1:10],\n", "     adj = c(0,1.5),\n", "     cex = 0.6,\n", "     col = color)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# 包装函数：课程数据2\n", "# 调整1: xlim和ylim得去掉\n", "# 调整2: 修改图例的位置\n", "plotVoc <- function(data){\n", "  color <- rep(\"#999999\",nrow(data))\n", "  \n", "  color[data$pvalue <0.05 & data$log2FoldChange > 1] <- \"#FC4E07\"\n", "  color[data$pvalue <0.05 & data$log2FoldChange < -1] <- \"#00AFBB\"\n", "  \n", "  par(oma = c(0,2,0,0))\n", "  \n", "  plot(data$log2FoldChange,-log10(data$pvalue),pch = 16,cex = 0.5,\n", "       col = color, frame.plot = F,\n", "       xlab = \"log2FC\", ylab = \"-log10(Pvalue)\", cex.axis = 1, cex.lab = 1.3)\n", "  \n", "  # 添加参考线：\n", "  abline(h = -log10(0.05),lwd = 2, lty = 3)  # lwd设置线的宽度，lty设置线的类型；\n", "  abline(v = c(-1,1),lwd = 2, lty = 3)  # lwd设置线的宽度，lty设置线的类型；\n", "  \n", "  # 添加图例\n", "  legend(x = 3, y = max(-log10(data$pvalue)), legend = c(\"Up\",\"Normal\",\"Down\"), \n", "         bty = \"n\", # 去除边框\n", "         pch = 19,cex = 1, # 设置点的样式和大小\n", "         x.intersp = 0.6, # 设置字与点之间的距离；\n", "         y.intersp = 0.6, # 设置点与点的高度差，相当于行距；\n", "         col = c(\"#999999\", \"#FC4E07\",\"#00AFBB\"))\n", "  \n", "  # 添加标签：\n", "  color = c()\n", "  color[which(data[1:10,]$regulate == \"Up\")] = \"#FC4E07\"\n", "  color[which(data[1:10,]$regulate != \"Up\")] = \"#00AFBB\"\n", "  text(data$log2FoldChange[1:10],-log10(data$pvalue)[1:10],\n", "       labels = data$row[1:10],\n", "       adj = c(0,1.5),\n", "       cex = 0.6,\n", "       col = color)\n", "}\n", "\n", "data <- read.csv(\"D:/R-code/huoshantu/DEG2.csv\",row.names = 1)\n", "\n", "plotVoc(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#火山图3个类型，运行成功\n", "# install.packages(\"devtools\") \n", "# devtools::install_github(\"BioSenior/ggvolcano\")\n", "library(ggVolcano) \n", "# data(deg_data)\n", "data <- read.csv(\"D:/R-code/promics_data.csv\")\n", "colnames(data)\n", "\n", "data2 <- add_regulate(data, log2FC_name = \"log2FC\",              \n", "                     fdr_name = \"pvalue\",log2FC = 1, fdr =  0.05) #fdr为p值；这行代码会自动把log2FC和pvalue转换成log2FoldChange和padj\n", "colnames(data2)\n", "# str(data2)\n", "# write.csv(data2, file = \"D:/R-code/data2_export.csv\", row.names = FALSE)\n", "\n", "#图1\n", "p1 <-ggvolcano(data2, x = \"log2FoldChange\", y = \"padj\",     #x和y的名字不需要改，\n", "          #x_lab = \"log2FC\",\n", "          y_lab = \"-log10(P-value)\",  \n", "          log2FC_cut = 1, #FC>2 或 FC<0.5 对应的logFC为1和-1\n", "          FDR_cut = 0.05, #p<0.05\n", "          label = \"Genename\", label_number = 20, output = FALSE)   #只需要改label\n", "p1 + coord_cartesian(xlim = c(-6, 6))\n", "\n", "#图2\n", "library(patchwork)\n", "p2 <- ggvolcano(data2, x = \"log2FoldChange\", y = \"padj\",\n", "                fills = c(\"#e94234\",\"#b4b4d8\",\"#269846\"),\n", "                colors = c(\"#e94234\",\"#b4b4d8\",\"#269846\"),\n", "                label = \"Genename\", label_number = 20, output = FALSE)\n", "p2 + coord_cartesian(xlim = c(-6, 6))\n", "\n", "#图3\n", "library(RColorBrewer)\n", "library(patchwork)\n", "p3 <- gradual_volcano(data2, x = \"log2FoldChange\", y = \"padj\",\n", "                      label = \"Genename\", label_number = 20, output = FALSE)\n", "p3 + coord_cartesian(xlim = c(-6, 6))"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}