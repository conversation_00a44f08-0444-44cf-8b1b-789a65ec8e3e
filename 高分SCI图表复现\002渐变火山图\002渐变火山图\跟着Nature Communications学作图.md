# 跟着Nature Communications学作图

从这个系列开始，师兄就带着大家从各大顶级期刊中的Figuer入手，从仿照别人的作图风格到最后实现自己游刃有余的套用在自己的分析数据上！这一系列绝对是高质量！还不赶紧点赞+在看，学起来！

![image-20211129003956607](.\figs\image-20211129003956607.png)

插入公众号

> 本期分享的是昨天更新的Nature Communications上面一篇文章中的火山图。
>
> 之所以讲这张图，是因为这张图和我们常见的火山图存在着一些细节上的差别，这张图通过颜色的渐变效果展示了差异基因的FDR显著性大小，此外散点的大小也是随着FDR的显著性增大而增大，在表现表达差异的同时也更好的呈现了显著性关系。非常好的表达方式，值得大家学习！

话不多说，直接上图！

### 读图

<img src=".\figs\image-20211129004413444.png" alt="image-20211129004413444" style="zoom:50%;" />

> 火山图的含义我想我应该不用过多介绍了，如果这都不清楚，那你应该多读一些文章，或者多看一些生信基础教程了！
>
> 这里的特色就是通过散点颜色和大小的变化表示q值的显著性水平。

### 示例数据和R包载入

```R
# 加载包：
library(ggplot2)

# 读取数据：
data <- read.csv("data02.csv",row.names = 1)

# 查看数据
head(data)
                row   baseMean log2FoldChange     lfcSE       stat       pvalue
GCR1           GCR1  7201.5782       2.244064 0.2004959  11.192564 4.434241e-29
OPI10         OPI10  1009.4171      -2.257454 0.2096469 -10.767889 4.880607e-27
AGA2           AGA2   249.1173       3.829474 0.3623263  10.569132 4.143136e-26
FIM1_1376 FIM1_1376  5237.5035       2.550409 0.2560379   9.961059 2.256459e-23
HMG1           HMG1 10838.1037       2.214300 0.2229065   9.933763 2.968371e-23
FIM1_3918 FIM1_3918  2456.8070       2.288243 0.2356228   9.711467 2.694309e-22
                  padj regulate
GCR1      2.153711e-25       Up
OPI10     1.185255e-23     Down
AGA2      6.707736e-23       Up
FIM1_1376 2.739905e-20       Up
HMG1      2.883475e-20       Up
FIM1_3918 2.181043e-19       Up
```

### 绘制

- 首先绘制出图的大致形状，修改散点为渐变色，大小为渐变大小；

```R
# 加载包：
library(ggplot2)

# 读取数据：
data <- read.csv("data02.csv",row.names = 1)

# 新增一列用于存储label信息，将需要显示的label列出即可：
data$label <- c(rownames(data)[1:10],rep(NA,(nrow(data)-10)))

ggplot(data,aes(log2FoldChange, -log10(padj)))+
  # 横向水平参考线：
  geom_hline(yintercept = -log10(0.05), linetype = "dashed", color = "#999999")+
  # 纵向垂直参考线：
  geom_vline(xintercept = c(-1.2,1.2), linetype = "dashed", color = "#999999")+
  # 散点图:
  geom_point(aes(size=-log10(padj), color= -log10(padj)))+
  # 指定颜色渐变模式：
  scale_color_gradientn(values = seq(0,1,0.2),
                        colors = c("#39489f","#39bbec","#f9ed36","#f38466","#b81f25"))+
  # 指定散点大小渐变模式：
  scale_size_continuous(range = c(1,3))+
  # 主题调整：
  theme_bw()+
  theme(panel.grid = element_blank())
```

![image-20211129095211448](.\figs\image-20211129095211448.png)

- 为部分散点添加上label，并修改图例位置

```R
# 加载包：
library(ggplot2)

# 读取数据：
data <- read.csv("data02.csv",row.names = 1)
data$label <- c(rownames(data)[1:10],rep(NA,(nrow(data)-10)))

# 读取数据：
data <- read.csv("data02.csv",row.names = 1)

ggplot(data,aes(log2FoldChange, -log10(padj)))+
  # 横向水平参考线：
  geom_hline(yintercept = -log10(0.05), linetype = "dashed", color = "#999999")+
  # 纵向垂直参考线：
  geom_vline(xintercept = c(-1.2,1.2), linetype = "dashed", color = "#999999")+
  # 散点图:
  geom_point(aes(size=-log10(padj), color= -log10(padj)))+
  # 指定颜色渐变模式：
  scale_color_gradientn(values = seq(0,1,0.2),
                        colors = c("#39489f","#39bbec","#f9ed36","#f38466","#b81f25"))+
  # 指定散点大小渐变模式：
  scale_size_continuous(range = c(1,3))+
  # 主题调整：
  theme_bw()+
  # 调整主题和图例位置：
  theme(panel.grid = element_blank(),
        legend.position = c(0.01,0.7),
        legend.justification = c(0,1)
        )+
  # 设置部分图例不显示：
  guides(col = guide_colourbar(title = "-Log10_q-value"),
         size = "none")+
  # 添加标签：
  geom_text(aes(label=label, color = -log10(padj)), size = 3, vjust = 1.5, hjust=1)+
  # 修改坐标轴：
  xlab("Log2FC")+
  ylab("-Log10(FDR q-value)")

# 保存图片：
ggsave("vocanol_plot.pdf", height = 9, width = 10)
```



### 结果展示

![image-20211129095546621](.\figs\image-20211129095546621.png)

- OK，大功告成啦！



### 示例数据和代码获取

> 本系列**所有代码和示例数据将会和生信常用图形系列绘图**放在一起，公众号右下角添加师兄微信，**付费99元，即可加入生信绘图交流群**。群内不仅提供生信常用图形系列的代码，还会**提供本系列后续所有Figure的示例数据和代码**，我会在文章更新后第一时间上传。
>
> 当然了！如果你还想白嫖，师兄的文章中代码已经写的很清楚了！但是师兄还是希望你**点个赞再走**呗！
>
> 以上就是本期的全部内容啦！**欢迎点赞，点在看**！师兄会尽快更新哦！制作不易，你的打赏将成为师兄继续更新的十足动力！

