# 生信常用分析图形绘制 -- 列线图

![image-20220613114413517](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220613114413517.png)

有了R语言的基础，以及ggplot2绘图基础，我们的**生信常用分析图形**的绘制就可以提上日程了！本系列，师兄就开始带着大家一起学习如何用R语言绘制我们自己的各种分析图吧！

>由于本系列的所有分析代码均为师兄细心整理和详细注释而成的，所以本系列中**部分教程需要收费**的哦！ 付费即可获取对应小节的绘图数据和代码！
>
>此外，如果有小伙伴想要**一次性购买本系列所有代码和示例数据的**，也可以直接公众号右下角添加客服微信咨询 **本系列所有代码打包仅售：99元**，并且还包括**跟着高分SCI学作图系列**的**全部示例数据、代码和付费文章**哦！
>
>最后，已经购买的小伙伴还可以加入师兄的 **R语言生信绘图**交流群，群内可以讨论绘图上的疑问，我也会在闲暇的时候给小伙伴们答疑！

系列内容包括：

- 各种类型的热图你学会了吗？
  - 普通热图
  - 环形热图
- 解锁火山图真谛！
  - plot函数就能画火山图？
  - 高级函数绘制火山图--ggplot2、ggpurb
- 经典富集分析及气泡图、柱状图绘制
  - 气泡图绘制
  - 柱状图绘制
- 富集分析圈图
- 富集分析弦图
- 绘制一张可以打动审稿人的桑基图
- 生存分析 -- KM曲线图
- 基础PCA图
- 云雨图
- 韦恩图
- 环形互作网络图
- 相互作用网络图
- 聚类树美化
- 富集分析气泡图进阶版
- mantel test相关性图
- 词云图
- 瀑布图
- 森林图
- 曼哈顿图
- 哑铃图
- 三线表
- 嵌套圈图
- 列线图
- 等等，想到再继续补充！！！

### 历史图形展示

fig1

### 本期列线图结果展示

![](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220613113837389.png)

### 列线图简介

#### 简介

列线图（Alignment Diagram），又称诺莫图（Nomogram图），它是建立在**多因素回归分析**的基础上，将多个预测指标进行整合，然后采用带有刻度的线段，按照一定的比例绘制在同一平面上，从而用以表达预测模型中各个变量之间的相互关系。

列线图的基本原理，简单的说，就是通过**构建多因素回归模型（常用的回归模型，例如Cox回归、Logistic回归等）**，根据模型中**各个影响因素对结局变量的贡献程度（回归系数的大小）**，给每个影响因素的**每个取值水平进行赋分**，然后再将各个评分相加得到总评分，最后通过总评分与结局事件发生概率之间的函数转换关系，从而计算出该个体结局事件的预测值。

#### 实例理解列线图

![image-20220613103321866](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220613103321866.png)

现在假设我们是一名优秀的心内科医生，有这样一位患者，男性，60岁，吸烟，有高血压和糖尿病史，血脂异常，否认CAD家族史，冠状动脉钙化评分（CACS）为3分。

我们作为接诊医生，在和患者交代病情的时候，为了向患者说明疾病的严重性，就拿出了这张列线图，自信满满的告诉这位患者，以他目前的疾病状态，预测未来5年、10年和15年的生存概率分别是71%、48%和27%。那么，这是怎么算出来的呢？

![image-20220613103730390](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220613103730390.png)

> 其实很简单，比如该患者年龄为60岁，我们就在列线图年龄为60岁的地方向上画一条垂直线，即可得到其对应的得分（Points)约为55分。同样性别为男性，对应的分数为1分，以此类推，找出每个变量状态下对应的得分。
>
> 最后将所有变量的得分相加，得到患者的总得分（Total Points)约为165.5分，并以总得分为基础，再向下画一条垂直线，就可以知道该患者对应的未来5年、10年和15年的生存率了，是不是很简单很容易理解呢！

### 数据及模型构建

```R
# 载入R包：
library(survival)
library(rms)

# 数据：
data <- datadist(lung)
options(datadist="data")

head(lung)
#   inst time status age sex ph.ecog ph.karno pat.karno meal.cal wt.loss
# 1    3  306      2  74   1       1       90       100     1175      NA
# 2    3  455      2  68   1       0       90        90     1225      15
# 3    3 1010      1  56   1       0       90        90       NA      15
# 4    5  210      2  57   1       1       90        60     1150      11
# 5    1  883      2  60   1       0      100        90       NA       0
# 6   12 1022      1  74   1       1       50        80      513       0

# 构建Cox比例风险回归模型:
mod <- cph(Surv(time, status) ~ age+sex+ph.karno, data = lung,
           x=T, y=T, surv = T)
```



### 最简单的线段式静态列线图

```R
# 绘制最简单的线段式静态列线图
survival <- Survival(mod)

# 这里绘制一年和两年的生存情况，如果要绘制五年或十年生存率也是类似的办法
survival1 <- function(x)survival(365, x)
survival2 <- function(x)survival(730, x)

norm <- nomogram(mod, fun = list(survival1, survival2),
                 fun.at = seq(0.05, 0.95, 0.05),
                 funlabel = c("1 year survival", "2 year survival"))

plot(norm)
```

![image-20220613113858309](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220613113858309.png)

### 动态线段式列线图

```R
# 绘制动态线段式列线图
library(regplot)

regplot(mod, observation = lung[4,], #指定某一患者，4即是选择数据集中第四位患者
        interval ="confidence", title="Nomogram",
        plots=c("violin", "boxes"), clickable = T,
        failtime = c(12,24)) #设置随访时间1年、2年
```

![image-20220613113837389](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220613113837389.png)



### 示例数据和代码获取

> 本系列**所有代码和示例数据将会和生信常用图形系列绘图**放在一起，公众号右下角添加师兄微信，**付费99元，即可加入生信绘图交流群**。群内不仅提供生信常用图形系列的代码，还会**提供本系列后续所有Figure的示例数据和代码**，我会在文章更新后第一时间上传。
>
> 当然了！如果你还想白嫖，师兄的文章中代码已经写的很清楚了！但是师兄还是希望你**点个赞再走**呗！
>
> 以上就是本期的全部内容啦！**欢迎点赞，点在看**！师兄会尽快更新哦！制作不易，你的打赏将成为师兄继续更新的十足动力！



### 往期文章

1. 