rm(list = ls())
library(ggtree)

data <- as.matrix(read.table("K:/2020-2023HCC/579hcc/模型代码总结2/R-code/R代码修改版汇总/热图/data.txt",row.names = 1,header = T,sep = "\t"))

df <- hclust(dist(data))

p1 <- ggtree(df)

gheatmap(p1,data)

p2 <- ggtree(df,layout = "circular")
p2

# 设置开口方向：rotate_tree()
p3 <- rotate_tree(p2,100)

gheatmap(p3 + geom_tiplab(offset = 13),data,
         # 设置热图的宽度：
         width = 1.5,
         # 设置单元格的颜色：
         low = "#FDEBEA",
         high = "#D5281F",
         font.size = 3,
         colnames_position = "top",
         # 调整开口大小
         colnames_offset_y = 1,
         # 调节列名和顶部之间的距离：
         hjust = 0
         ) + 
  theme(legend.position = "right")

# 后续工作可以在Ai中调整；

