{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["载入需要的程辑包：grid\n", "\n", "========================================\n", "ComplexHeatmap version 2.15.4\n", "Bioconductor page: http://bioconductor.org/packages/ComplexHeatmap/\n", "Github page: https://github.com/jokergoo/ComplexHeatmap\n", "Documentation: http://jokergoo.github.io/ComplexHeatmap-reference\n", "\n", "If you use it in published research, please cite either one:\n", "- <PERSON><PERSON>, Z. Complex Heatmap Visualization. iMeta 2022.\n", "- <PERSON><PERSON>, <PERSON>. <PERSON> heatmaps reveal patterns and correlations in multidimensional \n", "    genomic data. Bioinformatics 2016.\n", "\n", "\n", "The new InteractiveComplexHeatmap package can directly export static \n", "complex heatmaps into an interactive Shiny app with zero effort. Have a try!\n", "\n", "This message can be suppressed by:\n", "  suppressPackageStartupMessages(library(ComplexHeatmap))\n", "========================================\n", "\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": [" [1] \"ICA1\"    \"DBNDD1\"  \"ALS2\"    \"CASP10\"  \"CFLAR\"   \"TFPI\"    \"NDUFAF7\"\n", " [8] \"RBM5\"    \"MTMR7\"   \"SLC7A2\"  \"ARF5\"    \"SARM1\"   \"POLDIP2\" \"PLXND1\" \n", "[1] \"A-1\" \"A-2\" \"A-3\" \"B-1\" \"B-2\" \"B-3\"\n", "[1] \"行数: 14\"\n", "[1] \"列数: 6\"\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "\"The input is a data frame-like object, convert it to a matrix.\"\n"]}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["#已经成功\n", "# library(devtools)\n", "# install_github(\"jokergoo/ComplexHeatmap\")\n", "\n", "library(readxl)\n", "library(ComplexHeatmap)\n", "\n", "# 读取 Excel 文件\n", "mat <- read_excel(\"K:/2020-2023HCC/579hcc/模型代码总结2/R-code/R代码修改版汇总/聚类热图/heatmap_example2.xlsx\", col_names = TRUE)\n", "\n", "# 确保数据是 data.frame 类型\n", "mat <- as.data.frame(mat)\n", "\n", "# 提取行名并删除第一列\n", "row_names <- mat[, 1] #第1列为行名\n", "mat <- mat[, -1] \n", "\n", "rownames(mat) = row_names\n", "\n", "# 获取列名\n", "col_names <- colnames(mat)\n", "\n", "# 打印行名和列名\n", "print(row_names)\n", "print(col_names)\n", "\n", "num_rows <- nrow(mat)\n", "num_cols <- ncol(mat)\n", "print(paste(\"行数:\", num_rows))\n", "print(paste(\"列数:\", num_cols))\n", "\n", "# 绘制热图\n", "Heatmap(mat,\n", "        name = \"mat\",\n", "        row_names_gp = gpar(fontsize = 8),\n", "        column_names_gp = gpar(fontsize = 8)\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["column_ha = HeatmapAnnotation(Group = sample(letters[1:2], num_cols, replace = TRUE),\n", "                              col = list(Group = c(\"a\" = \"red\", \n", "                                            \"b\" = \"green\")), \n", "                              show_legend = T)#显示图例\n", "row_ha=rowAnnotation(Group = sample(letters[1:2], num_rows, replace = TRUE),\n", "                     col = list(Group = c(\"a\" = \"red\", \n", "                                          \"b\" = \"green\")),\n", "                     show_legend=F)#不显示图例\n", "# Heatmap(mat, \n", "#         name = \"mat\", #图例名称\n", "#         top_annotation = column_ha,#上注解\n", "#         left_annotation = row_ha,#左注解\n", "#         cluster_rows = T,#行聚类 \n", "#         cluster_columns = T,#列聚类        \n", "#         column_title = 'Heatmap'#标题名称\n", "#         )\n", "# 绘制热图\n", "mat <- as.matrix(mat)\n", "\n", "Heatmap(mat, \n", "        name = \"mat\",\n", "        top_annotation = column_ha,\n", "        left_annotation = row_ha,\n", "        cluster_rows = TRUE,\n", "        cluster_columns = TRUE,\n", "        column_title = 'Heatmap',\n", "        row_names_gp = gpar(fontsize = 8, hjust = 1),  # 将行名放在 x 轴下方\n", "        column_names_gp = gpar(fontsize = 8, vjust = 1) # 将列名放在 y 轴左侧\n", ")\t"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["column_ha = HeatmapAnnotation(Group = sample(letters[1:3],num_cols , replace = TRUE),\n", "                              col = list(Group = c(\"a\" = \"red\", \n", "                                            \"b\" = \"green\", \n", "                                            \"c\" = \"blue\")),\n", "                              show_legend = T)\n", "row_ha=rowAnnotation(Group = sample(letters[1:3], num_rows, replace = TRUE),\n", "                     col = list(Group = c(\"a\" = \"red\", \n", "                                          \"b\" = \"green\", \n", "                                          \"c\" = \"blue\")),\n", "                     show_legend=F)\n", "Heatmap(mat, \n", "        name = \"mat\", #图例名称\n", "        top_annotation = column_ha,\n", "        left_annotation = row_ha,\n", "        cluster_rows = F,#行不聚类\n", "        cluster_columns = F,#列不聚类\n", "        column_title = 'Heatmap',#标题名称\n", "        column_title_gp = gpar(fontsize=13,fontface='bold'),#标题大小，加粗\n", "        row_names_gp = gpar(fontsize=10),#行名大小\n", "        column_names_gp = gpar(fontsize=10),#列名大小\n", "        show_row_names = T,#显示行名\n", "        show_column_names = T,#显示列名\n", "        rect_gp = gpar(col='grey')#线段颜色\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#未完全成功\n", "# install.packages(\"pacman\")\n", "# install.packages(\"https://cran.r-project.org/bin/windows/contrib/4.3/pacman_0.5.1.zip\", repos = NULL, type = \"win.binary\")\n", "\n", "# install.packages(\"pheatmap\")\n", "# install.packages(\"RColorRrewer\")\n", "library(pacman)\n", "library(pheatmap)\n", "library(RColorBrewer)\n", "library(readxl)\n", "retu=read_excel(\"K:/2020-2023HCC/579hcc/模型代码总结2/R-code/R代码修改版汇总/聚类热图/heatmap_example.xlsx\", col_names = TRUE,range = \"B1:O7\") #\n", "retu\n", "\n", "#转换成数值\n", "retu1=as.data.frame(lapply(retu, function(x)as.numeric(as.character(x))))\n", "retu1\n", "\n", "#数值转换后会丢失gene，重新赋值\n", "rownames(retu1)=row.names(retu)\n", "retu1\n", "# write.csv(retu1, file=\"retu1.trans.csv\")\n", "#调用函数画图---基础版本\n", "p_load(pheatmap)\n", "pheatmap(retu1)\n", "\n", "#注释栏，重复数目和注释类别的先后自己定义即可\n", "annotation_col <- data.frame(group = factor(rep(c(\"A\", \"B\"),each = 3)))\n", "rownames(annotation_col)<-colnames(retu1)\n", "\n", "#display.brewer.pal(9, \"<PERSON>s\") #选用\n", "#brewer.pal(n = 9, name = \"<PERSON><PERSON>\")\n", "ann_colors = list(group = c(\"Female\"=\"#FFC1C1\", \"Male\"=\"#87CEEB\"))\n", "\n", "#多样化处理\n", "# 绘制热图\n", "p1 <- pheatmap(retu1,  #要绘制热图的矩阵\n", "               #color = col,\n", "               color = colorRampPalette(c('#3288bd','white','#F08080'))(100), #热图色块颜色是从蓝到红分为100个等级\n", "               border = FALSE,\n", "               cellwidth = 30, \n", "               cellheight = 10,\n", "               #border_color = \"grey\",  #热图中每个色块的边框颜色，NA表示无边框\n", "               scale = \"row\", #“row”按行进行归一化-单个基因不同组织内归一化，\"column\"表示按列，\"none\"表示不进行归一化==基因在那就归一化那列或那行\n", "               cluster_rows = TRUE, #是否对行进行聚类\n", "               cluster_cols = TRUE, #是否对列进行聚类\n", "               treeheight_col = 8,\n", "               treeheight_row = 8,\n", "               legend = TRUE, #是否显示图例\n", "               #legend_breaks = c(-1, 0, 1), #设置图例的断点\n", "               #legend_labels = c(\"low\",\"\",\"heigh\"), #设置图例断点处的标签\n", "               show_rownames = TRUE, #是否显示行名\n", "               show_colnames = TRUE, #是否显示列名\n", "               main =\"Female vs Male (Ovary)\",\n", "               fontsize_row = 4, #设置标签大小和倾斜角度;#字体大小，可以通过fontsize_row、fontsize_col参数分别设置行列名的字体大小\n", "               fontsize_col = 8,\n", "               angle_col = 0,\n", "               #display_numbers = FALSE,  #是否显示每个色块对应的数值(经归一化后的数值)\n", "               # display_numbers = matrix(ifelse(retu1 > 1, \"*\", \" \"), nrow = nrow(retu1)),\n", "               # number_format = \"%.2f\",  #数值格式，%.2f表示保留小数点后两位;#%.1e表示使用科学计数法并保留小数点后一位\n", "               # number_color = \"black\",  #设置数值颜色\n", "               # fontsize_number = 4,     #设置数值的字体大小\n", "               annotation_col = annotation_col, #定义注释栏\n", "               annotation_colors = ann_colors,) #定义注释栏颜色\n", "\n", "pdf(\"D:/lc-heatmap.pdf\",width = 12,height = 8)\n", "p1\n", "dev.off()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}