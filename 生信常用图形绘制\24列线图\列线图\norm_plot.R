# 载入R包：
library(survival)
library(rms)

# 数据：
data <- datadist(lung)
options(datadist="data")

head(lung)
#   inst time status age sex ph.ecog ph.karno pat.karno meal.cal wt.loss
# 1    3  306      2  74   1       1       90       100     1175      NA
# 2    3  455      2  68   1       0       90        90     1225      15
# 3    3 1010      1  56   1       0       90        90       NA      15
# 4    5  210      2  57   1       1       90        60     1150      11
# 5    1  883      2  60   1       0      100        90       NA       0
# 6   12 1022      1  74   1       1       50        80      513       0

# 构建Cox比例风险回归模型:
mod <- cph(Surv(time, status) ~ age+sex+ph.karno, data = lung,
           x=T, y=T, surv = T)


# 绘制最简单的线段式静态列线图
survival <- Survival(mod)

# 这里绘制一年和两年的生存情况，如果要绘制五年或十年生存率也是类似的办法
survival1 <- function(x)survival(365, x)
survival2 <- function(x)survival(730, x)

norm <- nomogram(mod, fun = list(survival1, survival2),
                 fun.at = seq(0.05, 0.95, 0.05),
                 funlabel = c("1 year survival", "2 year survival"))

pdf("norm_plot1.pdf", height = 5, width = 6)
plot(norm)
dev.off()

# 绘制动态线段式列线图
library(regplot)

regplot(mod, observation = lung[4,], #指定某一患者，4即是选择数据集中第四位患者
        interval ="confidence", title="Nomogram",
        plots=c("violin", "boxes"), clickable = T,
        failtime = c(12,24)) #设置随访时间1年、2年



