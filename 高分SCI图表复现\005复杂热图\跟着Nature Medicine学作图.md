# 跟着Nature Medicine学作图

从这个系列开始，师兄就带着大家从各大顶级期刊中的Figuer入手，从仿照别人的作图风格到最后实现自己游刃有余的套用在自己的分析数据上！这一系列绝对是高质量！还不赶紧点赞+在看，学起来！

![image-20211117193921713](.\figs\image-20211117193921713.png)

插入公众号

> 本期分享的是昨天更新的Nature Medicine上面那篇文章中的一个复杂热图！
>
> 这个热图可以说是高端大气上档次！配色自然不必多说，**非常有特色**的就是单元格中的注释，**"+"、"*"、"#"的添加**，让这个图的解释性更强，也更加美观！再配上右边的柱状图，简直是好看到不行！
>
> 那么这么好的图！你难道不想学学怎么画的吗？本节师兄就带你**解锁热图大师级R包 -- ComplexHeatmap！**

话不多说，直接上图！

### 读图

![image-20211215131030587](.\figs\image-20211215131030587.png)

> 这张图通过计算相关性矩阵，绘制相关性热图，图中的"+"表示p值小于0.05， "*"表示p值小于0.01，"#"表示是否为之前识别出的MLGs，是则显示。
>
> 这个图的男单在于单元格注释的添加，图例的调整，以及坐标轴颜色和字体的修改，用到的R包是ComplexHeatmap，赶紧点赞，在看！学起来吧！
>
> 本教程用到的数据为模拟数据，并无实际意义！

### 示例数据和R包载入

```R
# 创建示例数据：
LU_matrix <- matrix(runif(200, 0, 0.5), nrow = 10, ncol = 20)
RD_matrix <- matrix(runif(340, -0.5, 0), nrow = 17, ncol = 20)
RU_matrix <- matrix(runif(20, -0.5, 0), nrow = 10, ncol = 2)
LD_matrix <- matrix(runif(34, 0, 0.5), nrow = 17, ncol = 2)

# 合并：
data <- rbind(cbind(LU_matrix, RU_matrix), cbind(RD_matrix, LD_matrix))
# data <- round(data, 1)

# 载入行名和列名的信息：
names <- read.table("rownames.txt")

#rownames(data) <- names[1:27,1]
#colnames(data) <- names[27:48,1]
# 查看数据：
head(data)  # 就是一个数值矩阵；
             [,1]       [,2]      [,3]        [,4]       [,5]        [,6]       [,7]
[1,] 0.0625247662 0.25274612 0.3789528 0.149857496 0.32779579 0.065104130 0.49130662
[2,] 0.1264932272 0.15448924 0.3864576 0.337225190 0.06057473 0.190244207 0.48208280
[3,] 0.0002724812 0.45060088 0.3731771 0.257355053 0.11005671 0.172036878 0.08908112
[4,] 0.2585792022 0.41509446 0.3845477 0.173292746 0.08334462 0.002908393 0.37577387
[5,] 0.3547413627 0.05910167 0.4373865 0.001809852 0.35534205 0.203135436 0.11413250
[6,] 0.4484293570 0.43187782 0.3316678 0.336549529 0.42050129 0.441636302 0.09859197
```

### 绘制

- 首先绘制基础的热图，我们用**热图大师级R包 -- ComplexHeatmap来做！**：

```R
library(ComplexHeatmap)
# 基础绘图：
Heatmap(data)

# 设置颜色：
library(circlize)
col_fun <- colorRamp2(c(-0.5, -0.1,0.1, 0.5), c("#5296cc", "#cad5f9", "#fdedf6","#f064af"))

# 再画：
Heatmap(data, 
        # 设置颜色：
        col = col_fun,
        # 调整热图格子的边框颜色和粗细：
        rect_gp = gpar(col = "white", lwd = 1),
        # 调整聚类树的高度：
        column_dend_height = unit(2, "cm"), 
        row_dend_width = unit(2, "cm"),
        # 设置单元格中添加文字：
        cell_fun = function(j, i, x, y, width, height, fill) {
          grid.text(sprintf("%.1f", data[i, j]), x, y, gp = gpar(fontsize = 10))})
```

 ![image-20211215132118392](.\figs\image-20211215132118392.png)

> 有了这些还不够，我们需要把里面的数字变成我们想要加的标签：这一块比较难理解，如果看不明白的各位可以去B站看我的视频教程！
>
> 思路大概是先要构建一个p值矩阵，根据其中的信息筛选出哪些单元格需要加"+"以及哪些单元格需要加"*"!
>
> 那么"#"的添加也是同样的道理；

- 优化单元格注释！

```R
# 还需要一个p值矩阵：
p_data <- matrix(runif(27*22, 0, 0.1), nrow = 27, ncol = 22)

Heatmap(data, 
        # 设置颜色：
        col = col_fun,
        # 调整热图格子的边框颜色和粗细：
        rect_gp = gpar(col = "white", lwd = 1),
        # 调整聚类树的高度：
        column_dend_height = unit(2, "cm"), 
        row_dend_width = unit(2, "cm"),
        cell_fun = function(j, i, x, y, width, height, fill) {
          if (p_data[i,j] < 0.01) {
            grid.text(sprintf("*", data[i, j]), x, y, gp = gpar(fontsize = 6))
          } else if (p_data[i,j]<0.05) {
            grid.text(sprintf("+", data[i, j]), x, y, gp = gpar(fontsize = 6))
          } else {
            grid.text(sprintf("", data[i, j]), x, y, gp = gpar(fontsize = 6))
          }
        })


T_data <- matrix(runif(27*22, 0, 0.1), nrow = 27, ncol = 22)
T_data <- T_data > 0.05


# 加上行名和列名
rownames(data) <- names[1:27,1]
colnames(data) <- names[27:48,1]

# 最终热图效果：
# row_ha <- rowAnnotation(foo = runif(27), bar2 = anno_barplot(runif(27)))

pdf("Heatmap.pdf", height = 6, width = 7)
Heatmap(data, 
        # 设置颜色：
        col = col_fun,
        # 调整热图格子的边框颜色和粗细：
        rect_gp = gpar(col = "white", lwd = 1),
        # 调整聚类树的高度：
        column_dend_height = unit(2, "cm"), 
        row_dend_width = unit(2, "cm"),
        # 调整行标签和列标签的大小：
        row_names_gp = gpar(fontsize = 7, fontface = "italic", # 调整字体为斜体：
                            # 调整标签颜色：
                            col = c(rep("#ff339f", 10), rep("#5facee", 20))),
        column_names_gp = gpar(fontsize = 7),
        # 添加单元格中的注释：
        cell_fun = function(j, i, x, y, width, height, fill) {
          if (p_data[i,j] < 0.01) {
            grid.text(sprintf("*  ", data[i, j]), x, y, gp = gpar(fontsize = 8))
          } else if (p_data[i,j]<0.05) {
            grid.text(sprintf("+   ", data[i, j]), x, y, gp = gpar(fontsize = 6))
          } else {
            grid.text(sprintf("", data[i, j]), x, y, gp = gpar(fontsize = 6))
          }
          if (T_data[i,j]) {
            grid.text(sprintf("   #", data[i, j]), x, y, gp = gpar(fontsize = 6))
          } else {
            grid.text(sprintf("", data[i, j]), x, y, gp = gpar(fontsize = 6))
          }
        },
        # 调整图例：
        show_heatmap_legend = FALSE,
        # heatmap_legend_param = list(col_fun = col_fun, 
        #                             at = c(-0.5, 0, 0.5),
        #                             # labels = c("low", "zero", "high"),
        #                             title = "Spearman's correlation",
        #                             legend_height = unit(2, "cm"),
        #                             title_position = "topcenter",
        #                             title_gp = gpar(fontsize = 5),
        #                             labels_gp = gpar(fontsize = 5),
        #                             direction = "horizontal",
        #                             grid_height = unit(3, "mm"))
        # 聚类树排序
        column_dend_reorder = FALSE
        # right_annotation = row_ha
        )

# 图例参数：
lgd <- Legend(col_fun = col_fun, 
              at = c(-0.5, 0, 0.5),
              # labels = c("low", "zero", "high"),
              title = "Spearman's correlation",
              legend_height = unit(2, "cm"),
              title_position = "topcenter",
              title_gp = gpar(fontsize = 8),
              labels_gp = gpar(fontsize = 8),
              direction = "horizontal",
              grid_height = unit(4, "mm")
              )

# 绘制图例：
draw(lgd, x = unit(0.9, "npc"), y = unit(0.95, "npc"))

dev.off()
```

![image-20211215132215081](.\figs\image-20211215132215081.png)

> 到这一步，我们的这个热图就非常完美了！是不是很赞呢！

- 最后绘制右边的柱状图，可以使用ggplot；

```R
# 添加柱状图：
bar_data <- as.data.frame(rowSums(data))
colnames(bar_data) <- "lncMSE"
bar_data$group <- "Pos"
bar_data$group[which(bar_data$lncMSE < 0)] <- "Neg"

ggplot(data = bar_data)+
  geom_bar(aes(x=rev(1:27),
               y=abs(lncMSE), fill = group), stat = "identity")+
  scale_fill_manual(values = c("#ff339f","#5facee"))+
  coord_flip()+
  xlab("")+
  ylab("%lncMSE")+
  theme_classic()+
  theme(legend.position = "none",
        axis.ticks.y = element_blank(), 
        axis.text.y = element_blank(),
        axis.line.y = element_blank())+
  scale_y_continuous(breaks = c(0:6))

ggsave("barplot.pdf", height = 6, width = 2.5)

# 拼图：试了一下不能使用patchwork，因为是Heatmap不是ggplot对象：
# library(patchwork)
# p1+p2+plot_layout(widths = c(3,1))
```

<img src=".\figs\image-20211215132312572.png" alt="image-20211215132312572" style="zoom:33%;" />

- 最后，用AI将两个图拼接在一起就完事啦！



### 结果展示

![image-20211215132348282](.\figs\image-20211215132348282.png)

### 示例数据和代码获取

> 本系列所有代码和示例数据将会和生信常用图形系列绘图放在一起，公众号右下角添加师兄微信，付费99元，即可加入生信绘图交流群。群内不仅提供生信常用图形系列的代码，还会提供本系列后续所有Figure的实例数据和代码，我会在文章更新后第一时间上传。
>
> 当然了！如果你还想白嫖，师兄的文章中代码已经写的很清楚了！但是师兄还是希望你点个赞再走呗！
>
> 以上就是本期的全部内容啦！欢迎点赞，点在看！师兄会尽快更新哦！制作不易，你的打赏将成为师兄继续更新的十足动力！

