{"cells": [{"cell_type": "code", "execution_count": 16, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["将程序包安装入'C:/Users/<USER>/AppData/Local/R/win-library/4.3'\n", "(因为'lib'没有被指定)\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "\"package 'Ghostscript' is not available for this version of R\n", "\n", "A version of this package for your version of R might be available elsewhere,\n", "see the ideas at\n", "https://cran.r-project.org/doc/manuals/r-patched/R-admin.html#Installing-packages\"\n"]}, {"ename": "ERROR", "evalue": "Error in library(Ghostscript): 不存在叫'Ghostscript'这个名字的程辑包\n", "output_type": "error", "traceback": ["Error in library(Ghostscript): 不存在叫'Ghostscript'这个名字的程辑包\nTraceback:\n", "1. library(Ghostscript)"]}], "source": ["install.packages(\"Ghostscript\")  # 安装readxl包\n", "library(Ghostscript)  # 加载readxl包"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Note: When multiple decision curves are plotted, decision curves for 'All' are calculated using the prevalence from the first DecisionCurve object in the list provided.\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Standardized Net Benefit (95% Confidence Intervals):"]}, {"ename": "ERROR", "evalue": "Error in eval(expr, envir, enclos): Pandoc does not support newlines in simple or Rmarkdown table format!\n", "output_type": "error", "traceback": ["Error in eval(expr, envir, enclos): Pandoc does not support newlines in simple or Rmarkdown table format!\nTraceback:\n", "1. summary(nomogram, measure = \"sNB\")", "2. summary.decision_curve(nomogram, measure = \"sNB\")", "3. pandoc.table(out, split.table = Inf, keep.line.breaks = TRUE)", "4. cat(pandoc.table.return(...))", "5. pandoc.table.return(...)", "6. paste(res, sep.top, table.expand(t.colnames, t.width, justify, \n .     sep.col), sep.hdr, sep = \"\\n\")", "7. table.expand(t.colnames, t.width, justify, sep.col)", "8. tableExpand_cpp(cells, cols.width, justify, sep.cols, style)"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0gAAANICAMAAADKOT/pAAAAQlBMVEUAAAAAAP8A/wBNTU1oaGh8fHyMjIyampqgIPCnp6eysrK9vb2+vr7Hx8fQ0NDZ2dnh4eHp6enr6+vw8PD/AAD////7mF4vAAAACXBIWXMAABJ0AAASdAHeZh94AAAgAElEQVR4nO2di3aqOBhGaVE7amv1WN7/VUfuAQIG+ElIsvdacwYR+ISwm3ALSQYAq0lc/wCAEEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABHAp0j+H2TvOT8A58wsTkXaXTyPBOYgUQj4iOQeRQshHJOcgUgj5iOQcRAohH5Gcg0gh5COScxAphHxEcg4ihZCPSM5BpBDyEck5iBRCPiI5B5FCyEck5yBSCPmI5BxECiEfkZyDSCHkI5JzECmEfERyDiKFkI9IzkGkEPIRyTmIFEI+IjkHkULIRyTnIFII+cJFUvVCcPwd+7r87z23enKDSONft08QKYT8bURKEr1JxiIdknpyg8gZv2+PIFII+eIiFf87J8epr02X470jRiBSCPnbiDSmACJpQKQQ8jcV6XZKkvRcjjmnyVlp2l0PyeFaTXmuJ2qmr7p7K7+7ZP0ZLsXIc5Kcm6jX4o+PYhnH1yHaTflBj9PbRShfdX7o85Cc1LUYzieyzazMIsZOd2TX+boi+TNDuzi1aXcpD5eKXe6YD50akY7lOYlijks92E7fiHTKB666GW7HZtr66/T5MqJcxrX9QWn++TK5COWrcvCrST+razGYTwRECiFfUySGHo2IVHEvPvxk2U+xo/8k6T27p7VI9cefYk9vB5vp66bd8flS49CboRhZ/pvWy3t9+sr37TRP/slnqX+QwSKUr27qD3193/tV3flkQKQQ8jc6/X1XR73+ORWn8W61SKfkVnw85h/LwVNn+lqk30wzQznykbVnAYvFP0ut2mad8SKUr+rBpJ6q+6u68wltMyuziLHTHdl1/hbHSIe03pkft8tREaPZ8Xsfmwl607f/DmboftXu1a+jl9O9r7HJIkYHJ36VDIgUQv4WIv2Wf7PrQw9zkfrTLxApu+THROmj84PWiTT+q2RApBDyNzlrdyobal/J4Xp7mIs0mH6JSK922fmgHiMZLmJ0cOJXyYBIIeRvItK9Ptnw+udRHSPlrb3f/jHSqT7uuCVfnen7u2xnhu5X+X/H5hhJ/RnGi1C+6hwjtUtApIad7siu8zcRqaqSckfu5dHF7e1Zu1tnevWAfjBDlvVFuuYn0s75WbtDeY5NUyNNLGLsrF21BOVXZRkiOczecf42Ij2LKums3Hd3Ui7PZN1rOuUlpqwz/SGpz2xrZsiyvkjtdaSf3q1+RosYXEdKFJGUX4VI2W53ZNf524j02vtyM77y+8CrM9uX3p0NaXuXwam+q6Cd/vfQE6kzQzYQqThb197Z0J63NlqE8lV5j8SvIpLyqxAp2+2O7DrfZZGUP8D5L9AxctPtNllWZhFjpzuy63znu/HORCruY3ieBG+lex9pZRYxdroju853vhvvTKTqzjqx+38MQKQQ8p3vxjsTKbu+jrEOFusjRAojf2e7cYwgUgj5iOQcRAohH5Gcg0gh5COScxAphHxEcg4ihZCPSM5BpBDyxW8Ryjmcn8qn5o6d5/nw+tB0p5A2V2uqazdfj/7iogCRQsjfRKT6ybqmB4fCpGdafVdadkuax8K7c8UGIoWQv8lNq49jea9ac/908emr6DDr9V15ufMrOecPIbXTPY82b8zZD4gUQv5Gd38fisqm/lTfL11URc/6OZ/01bjrzPW0emfObkCkEPI3Eql44nUgkjrlz6v6Oed3iA6miw1ECiFfVyTfZmgXVy3vWTyj2m3avVpyyjFQ/nT4b/W0AjXS9rOIsdMd2XW+pkgMPZoUKasezVa6iywePj2c6zN4hTRp2dprjqw4RtpsFjF2uiO7zt+oadcRqeku8vaVn5orTtX9FNJUbbvmrN1T9tf4ASKFkL+xSJnaXWTO7yUtToYfin/vZS8lXEfafhYxdroju87fSKRH28lI011kRSHPo2n1yXb/6yMORFK6qJjNTndk1/kbifSjvHGl6i6yU1ldGpEuGSLZmKXkfkrSa7X1F3ZLsdMd2XX+ZteRfptPVXeRp+pdK8VphkNVSz3Us3uRYlGke2HQOfl6Zo9TsqxO2umO7Drfxp0NZZX0auJdn6//HV8FeG9ePnHMJUOk7WcpKN58cy6vMjzbXjRnsdMd2XX+xvfaFSPL7iKbnhaP+WB9AuLWvCssWiyKVF0ZPykfZrPTHdl1/iYiHS/1p/L/ZXeR2f0rfX2Xn/BO2yuvaYpINmYp5ytm/CnbdAsvf+90R3adH/UuvA+sNu2+6kt1z6+Fl793uiO7zkck51gU6Zkq504X3o+10x3ZdT4iOcfqdaRzrU+69Hasne7IrvMRyTnc2RBCPiI5B5FCyEck5yBSCPmI5BxXInEdSTIfkZyzH5EShX9jfH6OfhUziOScfTbtRiM+P7cPn4IaCfR4JtI/xyYhEujxTySnJkUhUvcBWZMJx3h+Jck5u+m/NDtMvhlP6vZeP6si/V6K98snp/Pv9ITjIjlu3CGSdsIx8uK+HEamMtrxD5o+wJb+mk2xeYvQQTmdMP1g34RIbk1CJO2E4xM8xqeaYYcHN5ZbFOmcpD9lTzSPWzp90yoizcvfr0gTUyHSwqg0uTfD9+m7VqdEcmpShCLdXu2z8t7IV/1yStLyKaVz/qewnPA1eCw7P3m1OU7KDEoDJJ/uesh7GujPraaeq6DeIurvyuTXYsouP14jL8XIc6L0LVH/mux2fLV7+odnyshmQnWJ5RqoP9V4o82bfOEs5XzJ2AfziFIkdyZ5JNKHGbqlaTo4KffUtO7o5Fgc6hYTHuu+7IoRZ2WGrkintkmvzK2mXuoJeouoFp0U3RMcm8VUM9yOzbTqr7mWy+jqoIxsJuwssVgD9aca42GN5LJK8kckQ4/0IikCJHnvjz9VG+34fO2Lh/xzes/uxWMxP/nIr3JHPj6zzgxq0+6Wf/s85k+nK3N3UsuxP73MTJv804ws/62e0G1/TbG3/fR6NGhHthP2l9j5qebYPUa6Vb3OrDlGylya5I9IK+g1ycpRxT91p0KnYujWDD7LHflXXYYyWz54Kvo1fuYtJ2XuTuqtGHvqLKJeTptcTnVsR9Z96vV/jcaDdmQ74WCJ6k+dsdHmTLx4lpKjUkKHyX5t34rkyqRIROoMPG6XY2enbqZQBtXZmhm6LiiVXHf6zuyjmSPJ3a/aZb4OnE73e9alHTn42Z2F9/6MmGD3OtK5aH2mp8vy60gFiNRlS5GOzU5lKFI7w2KRdJkzRcou+QFd/+2BzUi/RVofUe9IrkyKT6Sv5HC9PWaIpMyQDVxQJxwXSZs5V6RXc+18GPb6Vo2cFun9NhqASLOIT6TiQ3+nLo8rfvPBo3JU0s49FOnUHrMoc3dSyyOnr5HM7jHSSS9S+2t6q9Jfv+PgGKlZovpTZ2w0K7OIRTQ7kiOTYhTpN7v3j1du7Xm3a36K66x0EKnMUIuUt6+Kk2OvqU+duTup5dhbL/PRSe6cY8uyvkjtrzmUZ/66NVI7sp1wsET1p87YaHMmXjyLWAQiadlQpLpn1d/Onlsc6361xzPldaRiJmWGctSh7DOqPO4pDlqUudXU8vJSdxHlzEqyetUny/oitb/mp/0VLcpI/XWkcjLlp5pvtDkTL55FLKLdkdyYFJ9IrwOW5Pg7aEtdlDsbXnu/+lqXdoZy1O+hbGpdX1JUL0+66O9sONWvKWkXUc6sJjcvM9EfPtW/pryJoX9SSxnZTNhfYvenGm+0WVMvnUUsApG0WCiSzVl0iL8fvBXJjUmItBmItAGINC/f131QvYdiK5E092lsEmNlFrEIdUdyYRIiiYJIG2Mokn2TEAn0eCySiyoJkUCPzyI5MAmRQI/nItk2CZFAj9ci2a+SEAn0+C2SdZMQCfQg0iwQCfR4LpJtk+IQqbzqcjhPPsU8A196S12D/yJZNSkmkebe/zyKN72lrsF3kSxXSbGIlP/7OM7skWp6cf46YoT3Itk1KSaR8qpkwaOiY4tDJIFZxCK0Ilk0KS6Riue+O/2ONoOJ0i9qZ0afe0tdg/8iWa2SPBLp0wzt4qrlPYsntZV+R9tBpV/Uzoxe95a6hgBEsmmSPyIZejQpUtU/Q9PvqDKo9IvamdHr3lLXgEiz8EekNXRE6nSR2gwO+kWtpve6t9Q1hCCSRZMiFEl5mqcz2J2yM2N11s+73lLXgEiziEukR/knfplIHvaWuoYgRLJnUlwi/Sjd1anjMwORfOwtdQ2INIu4RDrkxxuDLlKrSZp+UTszet1b6hrCEMmaSTGJVN3ZoPQ7qgwq/aJ2ZvS6t9Q1INIsYhFJvddO6Xe0HVT6Re3M6HVvqWsIRCRbJsUk0vFSfVT6HW0GE6VfVHVGr3tLXQMizSIOkQzQH8xbP8TfD6GIZMkkRKoTEakLIs0CkepE5dRze81mK5HUjJ0SjEh2TEKkOhGRuiDSLBAJ9IQjkhWTEAn0INIsEAn0BCSSDZMQCfQg0iwQCfSEJJIFkxAJ9CDSLKIRKa3vvh482ABaghJpe5NiEemW1E90I5IZoYm0sUmxiPSVnKtHjRDJjLBE2rxKikWkV8MuHTx1ChMEJtLWJkUiUv6M+bnsaguRzECkWUQiUv4Q92/ZuyIimRGaSBub5I9I/xmiW1zZYUJadA2HSGYg0iz8EWkNRe9BVdsOkcwITqRtTYpDpKL3oOxe9DqCSGaEKNKGJkUh0qN5+OeBSKaEJ9KmVVIUIl0akS6IZEqAIm1pUhQiHZKy851H3Z0pIr0HkWYRg0j3pre6Y3JHJENCFGlDk2IQ6dx0n3pLzohkCCLNIgaR0lQdRCQzghRpO5NiEAmWgEizQCTQE6ZIm5mESKAHkWaBSKAnUJG2MgmRQA8izQKRQE+oIm1kEiKBHkSaBSKBnmBF2sYkRAI9iDQLRAI94Yq0iUmIBHqCFknepChEqvuG5B67GQQs0hZVUiQiKd0Vgxkhi7SBSZGIlD8amyHSHAIX6W/1T1mTL48lkapnZBHJnKBFkq+SIhGpeki2FOl6SA7X8uPjlKRlZfUamV5FUz0ndJGEq6RIRMq+ig65CpGORTcox+JjWvWIkp2akVAStkjiVZI/IiWG6JaWZM+mT7ufJL1n9zTvLPJlzjO75t/c8qHnsXkmHcIXSdYkf0RaQe7PNbmWA6fCllte+yRNNXUqejN+Nr2kQOgiSTfuohEpO7xcUfpraAfLofH6LFJCF0m4cRePSL/JFyLNIAKRJKukeER6Nd/uEyKJBoZA8CLJVkkRifRIDuox0kkV6cRphj4xiCRYJUUkUtEFeO+sXf1tMTK7crKhJXyRRKukmETK0sF1pObbcmT6EM31mihEkquSohLpVt3ZkDZ3NrT/Xl/tvi88aolAJMkqKQqRYAGINAtEAj0xiCRoEiKBHkSaBSKBnihE+hMzCZFATxQiyVVJiAR64hBJrEpCJNATh0hiVRIigZ5IRJKqkhAJ9CDSLBAJ9EQiklTbDpFATywiCVVJiAR6EGkWiAR6YhFJyCREAj3RiCRzlIRIoCcekUSqJEQCPfGIJFIlIRLoiUgkiSppryKBc+YXpq8iSVRJOxWJfB/zvRVJoEoKqiDJd5vvrUgCVVJQBUm+23x/RVpfJQVVkOS7zfdXpPUvpwiqIMl3m++xSKurpKAKkny3+R6LlH2sNCmogiTfbb7PIq2tkoIqSPLd5nst0soqKaiCJN9tvs8ira2SgipI8t3m+y3SuiopqIIk322+3yKtq5KCKkjy3eZ7LdLKKimogiTfbb7nIq2qkoIqSPLd5vsu0poqKaiCJN9tvt8irauSgipI8t3mey/SiiopqIIk322+9yKteJwiqIIk322+5yKtqpKCKkjy3eb7L9LyKimogiTfbX4AIi2ukoIqSPLd5vsu0poqKaiCJN9tfggiLa2SgipI8t3mhyDS0iopqIIk322+9yKtqJKCKkjy3eYvFun5lSTHW7WQyaVsL9LCKimogiTfbf5SkZ5p0bXrqVyIa5GWVUlBFST5bvOXinROri+brumxWIhLkZZXSUEVJPlu85eKlJYzPtLDYw8iLaqSgipI8t3mLxWpdud5PLoWaXGVFFRBku82f6lIh+RZDx33IdJ8k4IqSPLd5i8V6Zp8VUOP5OhYpKVnwIMqSPLd5i8+/X1u7Lm9eTGTFZGWmBRUQZLvNn/5Bdn7qR56fDkWaWGVFFRBku823/87G3KWVUlBFST5bvPDEGlZlRRUQZLvNj8ckeabFFRBku82X0Qk1ycbsmVVUlAFSb7b/I1EUl+1/s8Gf39//14mWcmC4HEk0sII0b8IC6qkoP4iku82PxiRFpgUVEGS7zYfkdxBfkD5y0X6vZzKR5LOvwsjhDfkbJOCKkjy3eYvfrDvoJxOOC6LQCTyg8lf/mBf+nMvhh63NDkvipDekHNNCqogyXebv/zBvnszfE/SRRGIRH4w+Wsf7Bt+MI8Q35AzO4sMqiDJd5sfUo2ESOQ7y19xjHR7FEP7OUYq3zv2YTx1UAVJvtv8xae/j8pZu8NzakqrIs2pkoIqSPLd5q+4jnQuriOlp8tOriPlzKqSgipI8t3mh3NnQwEike8mPzCRMkQi30l+aCK9jpE+TE0KqiDJd5sfnEgzqqSgCpJ8t/nBiTSjSgqqIMl3m49I7iA/oPwQRfo2NCmogiTfbT4iuYP8gPLDE8m8bRdUQZLvNj9IkQyrpKAKkny3+YjkDvIDyg9TpD8jk4IqSPLd5ocpklmVFFRBku82P0CRjKukoAqSfLf5iOQO8gPKD1SkDJHIt5ofqEhGVVJQBUm+2/xQRTKpkoIqSPLd5ocoUtG2M6iSgipI8t3mhyqSSZUUVEGS7zY/XJHeV0lBFST5bvPDFel9lRRUQZLvNj9Qkcq23d/0ZEEVJPlu84MUybBKCqogyXebH6ZIxXm7t1VSUAVJvtv8YEUyMCmogiTfbX6gIhlVSUEVJPlu80MVqexMf9qkoAqSfLf5AYv0WYg0YVJQBUm+23zti/fSyReHrYnosO2GrKukcZOCKkjy3ebrRHpMv8pyTUSHbTfk99sqKaiCJN9tfr2X3xKVwxYRAzbekG8bd0EVJPlu85u9/KB69ObVYQsj+my8Id9WSUEVJPlu881fTi4S0WFrkd6ZFFRBku82P9izdtn7KimogiTfbX69l+e1kdK42yJiwOYb8o1JQRUk+W7zgxap6uIOkcjfPL/ey88XyaVqIwZY2JBF98WIRP7m+d0aSXLJg4gBdkQaNymogiTfbX4r0iNEkSarpKAKkny3+fVe/tW5IBvKMVJxo9D32K2rQRUk+W7z6738eQpTpKkqKaiCJN9tfsAXZEsmqqSgCpJ8t/nBi1RVSe7yxyE/oPyQ72woeVVJI227oAqSfLf53b38dsprpdNDMsG1SONVUlAFSb7b/M5efizPMySpqEmINAL5AeWre/k1OT5zka7J11YRHWxtyDGTgipI8t3mq3t5mjzLEw4hnf7OEIl8C/n9s3YhijRmUlAFSb7bfHUvP1Q10j2MR81bEIn8rfM1x0i3NLluFdHBokjaM+BBFST5bvM7e3l9m9BRMmEHIpVV0neBk3w95AeUP7yOlJx+JAP2JpJiUlAFSb7b/PDvbCgoG3dZ3iOKk3wt5AeUH5VIf4hE/kb5kYikrZKCKkjy3eZ39vLLIbznkSp0VVJQBUm+23x1L7+E+GBfxWddJSES+Vvkd28REr1+pIvoYHVDvkwqqiREIn+L/PAf7KupRFJMCqogyXebr+7lp+QpuWhdRAfLG3JQJQVVkOS7zVf38kd6FH0NhSaig3WRPhGJ/I3yu027cE825PRNCqogyXebH5NIGSKRv1V+LBdkSwqTEIl8+fzYRPpEJPK3yI+gFyGVjklBFST5bvMj6EWoAyKRv0l+DL0IqXy8TEIk8sXzY+hFqMNna1JQBUm+2/woehFSeVVJn4hEvnR+FL0IqXx8IBL58vlR9CLUoTDJYX4L+QHlx9GLkMpH0bhzl99CfkD5cfQi1KGpkoIqSPLd5sd1Z0PBx0dWmhRUQZLvNh+R3EF+QPn9vfz3mKRn2ef79iZS2bb7C6wgyXeb3+zl95dB1+xenGxIRU3ap0gvk4IqSPLd5td7+W9h0PmY3rPnMTlvETHA1Yb8r6qSgipI8t3m13t5Ic85SW6v4WeSbhExwNmGLEX6C6ogyXebX+/l5V1B1b1BId8ilFO17YIqSPLd5kcpUlaKpHtBs02C2pFiz49UpLJt51iloHak2PMjFsm5SUHtSLHntyJ12CJigMMNWYj0L3OrUlA7Uuz5cYtUmORMpaB2pNjzI7xFKKd9KsmhSkHtSLHnRy+SQ5WC2pFiz49UpEwRyZlKQe1IsedHLJL6qiREIn8d0YrUqZKyXCXrvyGoHSn2/FhFyqukf6pJiET+GhCpxr5JQe1IsecjUg0ikb+CeEXqnW5AJPLXoH0Zcxr480gFfZHsmxTUjhR7vk6kR/i3CGWIRP4mIt06t9oF3GVxQ95PpNu2XVA7Uuz5zV5+UD0Sfbn5TkVSu9MvsW1SUDtS7PnaYyRZditS3yREIn8x0Z61y0X6D5HIFyKyd8iqvETK6hdTlFg2KagdKfb82N4hq1JUSZ/uqiTX60++ILG9Q1alrJI+2xGIRP5SonuHrEIukkuTXK8/+YJE9w5ZNX9gEiKRv5Do3iGr5hcifSsmIRL5C4nvHbJK/sdH9l/27cwk5+tPvhzxvUNWzS+rJMUkRCJ/GRG+Q1bJL6ok1SSrfTe4X3/yxYj4zgZFpExr0tZOuV9/8sWIW6SybTdi0l/NhvlOIV+Q6EWqqqTcpFKlWpw/RCLfnEj7/q7zS5E0Jv2pNdN2+U4hX5DIRVKqpEalXCG1IkIk8t8z3MvvhyS9bRtRsYcNqVRJqkmqPIhE/nv6e/nzK0kukgE+iZSrlP/bOy7ayqQ9rD/5QvT28muSCD+NtHOR9CZ1QSTy39LZy38Pwt01DCNUdrEhG5FKkxCJ/EUoe/njlMjeZDeM6LKLDVmJ5MKkXaw/+TK0e/klSb6ekoseRvTYx4bsmYRI5C+h6dcuTQ53yQUPIwbsY0PWIk2YhEjkvyP260iKSJVJiET+AhDpJdLApO/elNuYtI/1J1+EyO+1y2mrpNyk/JmK7+/vrkqIRP4bEEmtklSTtu/wbifrT74ES0Wa0RT0SaSC/Cipp9ImJu1k/cmXYKlI13BEUtt2JcX5ho5JiET+NIubdvfUtGcHH0TqV0nFmTvFJEQif5rlx0j35LwyYi8bciBSfQp84yppL+tPvgArTjZcE7MruB6KNDQJkcifhLN2mb5K6jXuEIn8SRApG6mSuib1nvWTzXcE+YIgUqYVadC4a/tBEesQZTfrT/56ECkrRXprktKtECKR30dEJM+vI41VSf1z4Eq1JGLSftaf/NVsJJJ6sfbf/vn4eP3zX2/k52fxv+9vzQwvnbb+TeAQRyItjNjPXyRt205zNalFokraz/qTvxpEKtC17SZFkjBpR+tP/loQqWDqdMNYlbTapB2tP/lrWS7S76V8ndLp/KbjIW9F2rpK2tH6k7+WpSI9D8rphOnbV70XaSuTdrT+5K9lqUjnJP0pb7V73NLp21d9EGlB2259425P60/+SpaKlCp3rN6TdFHEnjakiyppT+tP/kqWPyE79sE8Yk8bshBpZpW01qQ9rT/5K6FGKpkWaZsqaU/rT/5KVhwj3cre9sM4RlrUtltp0q7Wn/x1LD79fVTO2h0muzr2SCS7VdKu1p/8day4jnQuriOlp0sA15EWirTOpF2tP/nr4M6GmnenG+SrpH2tP/mrQKSat1WSuEn7Wn/yV4FINW9EGjEJkcgvQKQGEZPmeLWz9Sd/DYjU8E4kvUkdkeY9hr6z9Sd/DYjU8FakNybN7tJhZ+tP/hoQqWWZSYM+URApxnxEankvUv8lFQWaasjMpL2tP/krQKSWMZE+u8dJGpf6IFJ0+YjUUoqkN6nv0ptFG5m0t/UnfwWIpKCvkrLSJfXzW5UQKbZ8RFIYFSnrv+xcxKTdrT/5y0EkhZG2XUFXpLcqIVJk+YikYl4ljd58V4FIkeUjksockUYfrSgxMGl/60/+YhBJZU7bDpHIV0CkDlarpB2uP/lLQaQOiET+MhCpg2Db7r1JO1x/8peCSB0+KrTTz66S3twKvsP1J38piNQFkchfBCLpMDXp7T13OaMm7Xf9yZ8NIumQqZIqECmGfETSgUjkzwSRtFhp2+14/cmfCyJpsVIl7Xj9yZ8LImmZLdKUUIgUQT4iafn4MLy7oRJo+qGKEZN2vP7kzwWR9Myrkt48fY5I4ecjkp65Ii1p2+15/cmfCSLpmdW2q2UaRW/Sntef/Jkg0ggzqqRvRCIfkUYYEUlXJZUKFf8OPStApODzEWmEGW27+sRd8e0Mk3a9/uTPA5FGGBNprNIpTUKkWPMRaQzjtl3NS6TXd/qvESn0fEQaY3aVNCWS1qR9rz/5s0CkMZa07fKvzKukfa8/+bNApFHG2nbjVVLxBSJFmY9Io8xv2/VEUifTPXG+8+RjVAMAABQTSURBVPUnfw6INMqoSGMmVaPrb7tn8DQm7Xz9yZ8DIo3S7QVFbef13/LSjP4u/9dMpE42NGnn60/+HBBpnA8dxTefWpUqkbKqhfeZDUzqqrT39Sd/Bog0xX9jJulUennUilR925mqb9L+1598YxBpksFRUtva6zfdcn8akz6VA6XR5p0H60++KYg0yfB0g3Lc9Nl5u2z+f0WkTJmqnRuRQs1HpEk05+06/bAqJnVFytRplA+qSR6sP/mmINI070zKmvpHEanHWJXkw/qTbwgiTaO7lKQ1qbRFa9JYleTD+pNvCCJNo70mO7h5qGngzaqSfFh/8g1BpDcYm1T8/71IiklerD/5ZiDSG8xEqhnpuEFfJXmx/uSbgUjv0B8ljU09p0ryY/3JNwKR3rFdleTH+pNvBCK9Y55Ic6okP9affCMQ6S0SbTttleTJ+pNvAiK9ZbMqyZP1J98ERHqLiEiZ5t5VT9affBMQ6T3z2nbmVZIuf/QV6Bvgy/b3Ih+R3iNfJZUmafJ1XTtshi/b34t8RHrPTJGMq6Rh/uAh2k3xZft7kY9I75ERaWiSTqTxd6DL48v29yIfkQzYqEoa5OcjLdZJ3mx/H/IRyYD5IhmZ1M8vHUIkL/MRyYC5Io28m/mNSHVdZM0kb7a/D/mIZMI2Jg1Eqv9vySR/tr8H+YhkwmyR9Cr1ROp259Xqg0ge5iOSCQtEKt8tq5CP63WKMtalsSWT/Nn+HuQjkhEzb24oGaqkqtPtFq/TnkMk//IRyYglVVKXgUmfn3/tp95xkR2TPNr++89HJCPWi1SqpLzz5TP715jUP7+ASN7lI5IREiJlhUjKO1/+NYdJfXEQybt8RDJDxKTi5ZjNuyry/PLT0BsrJvm0/Xefj0hmyIhUvNWvedFsnv86UtJdOEIk3/IRyQwpkapK6bPJf5mksQaRfMtHJEOETWryP/M6aQAi+ZaPSIaIidTeG17mm78FXRqvtv/e8xHJEEmRvjv5WpMQybN8RDJF3iRECigfkUyREynriqQ1CZE8y0ckUwRFqkyaEsmGSX5t/53nI5Ix4lVSk++oSvJs++87H5GMQSTyx0EkY7YTSWcSIvmVj0jmSJs0KdJGJqkL9W377zofkczZTiQ7VdLfX7cDSt+2/67zEckcr0X6q2lH+bb9d52PSDMQvk1IzR+aJCmS4hBvVd8mH5FmIFwldUWqaEdJmdStiRBpm3xEmsOiPlC0jIqk9oeyZMEDeu053qq+TT4izUG2bafL/1Q7ROnMsCBEsxi1fwj/tv+O8xFpDtuLpJikViT6nltNmHj61r/tv+N8RJqFaNtOn6/po0vpYnI2E4/ferj995uPSLMQrZJG8vsmVRItM0l3oIVIG+Qj0izsiPRZT6J2d7zMJO0ZC96qLp+PSPMQa9uNiFT2Ivk56DW8+q477fsU/Zk/3qoun49I85A8SNLkl+Z8Vir1u2DtmGR01DRyCv1v7GXQVgkqH5HmIdm204pU/K+9oDRm0vTpB83V194Ef5o3BtomqHxEmolg226Y36rRqDQwSWnxjZnUzDN+TTc3ycvtv9d8RJrJfJFGvmxE6jTX2uHWpO5cyoHTSJ3U3BI0eW+Exbc+j+Bl+Y+BSDOZ3bb7GPuykeG7O6ZBa5JmEV0KiUqTplXR3A9uFy/LfwxEmslckT4+xkx6WTA4LdedYqyL/c4yerR3eb9VxLVJXpb/GIg0l5ltu9dXIyoNDnV0L519Y9Kgcafe5v3OkH/9WSzjZ/mPgEhzmSdS8Y3epMGVVk1DrTBpbEf/ywYmzZKiXn9XlZKf5T8CIs1lVtuuNkhrUpNfHy1pFtA3SX1jcyFAX6Sxn62hyXdkkp/lPwIizWZGldT6ozOpzS9s0J+Cy01qd/N2l28OcNTZ5vmgrL8Tkzwtfz2INJtZIimDg0lUkb5HLwqpVVJzckA5paA27haLZOtF6qP5LkAkIQRF0pvUGTklUjYhUtZWSY1DnbZYe96vZ4P2fTH6fCcmeVr+ehBpPqZVUq8SGkyi5k/c79NUSX+tT4OLtNUX3fkG3UCM5yPSShBpPnqRKjqj+pOM50/dgVpVSaPHMUXz7nvUo1Gdut/YN8nX8teCSPOZFEk9KhpOsyi/rJImzgdUJuUTfLYzVTeQZ2MmfSKSIIg0H+1BUsWUSP0xxvlFlTSxo+dfldY0XuQedW4/GpjUjGjuMzf9OVL4Wv5aEGkBRibpj5oW5ZciTUxQmdRWMYVHWedOvr5Jn5//mqU3C7GKt+WvA5EWsEIkdaR5/vgJg5LyEKpxSGnIdU1SFvMaRiRBEGkBUyJlzb0M+i+V0XIi5RLUj9WWFVP7VfcZJ2UYkSQXhkhLMKiSxu91aL6Ykf++Siot6XfzkKN8Uo6LPtv8hSatvrHI3/LXgEhLeF8ljd7GqpgkKNLwXEJL/2nBz3p5AiKtMsnf8teASEtYIZKi0pz8dyZNfT8wqbKuL9JMk5rnB5fib/lrQKRFvDNp8uHz+nKTC5EypTuIdSLVN1ssV8nj8h+CSItYJVKt0qz8aZMmvx27aUJApFVHSh6X/xBEWsSkSOPdNHQmmdf30AqRxkxq1n/apLGu8dqhhSZ5XP5DEGkRq0Uae2x2nElXNhRppGMH9WHDhSp5XP5DEGkZJpeS3jHPpClX3h1B6U0yEql5gPCvN7o3zfQP0OJz+Q9ApGVMV0mG+WImSYmkeyvZX39IN+Eik3wu/wGItAwRkebVSeO2vL3KtFQk1Z6OSZqW3nyVfC7/ActF+r2ckpzT+XdhhM8bUkakWSatEElvUrv+6lsC/zq1kDq9IovuPYCzTfK5/AcsFel5SFqOyyK83pACJuX5g6f/JqZX7pQb+2IMU5GUQyLNGYZmzMhbl2aq5HX591kq0jlJf+7F0OOWJudFEV5vSCGReiYNn7NVaHb33mN67z3Sm6QTqWS0C9YRw5Qv3/8UTb4bdiFSmtyb4XuSLorwekNKidQxSfOcrUr70F5HJXmRxpnwKJtbKXld/n2WipQkYx/MI/zekOtNqvI1nd+NmNSIpL5ByVSkoUnK+pub9IY5Jvld/j2okZYiJpKuO1a9SeXu3qmXMlMJNCZtIdKcSsnv8u+x4hjp9iiGIj1GEhSp0mayC6KSqi5qPr3pb6vD0KRNRJphkt/l32Px6e+jctbu8FwU4feGFBSp0GbQC57GpNf+3j/PYOqR5jBpG5EGZ/5GD6v8Lv8eK64jnYvrSOnpEuN1pEzAJCVfc4JhxKQVu3zfpK1EMjXJ8/Lvwp0Ni5EUSaeNzqQ1Hg0ad2q+rEkaNCp5Xv5dEGkxoiLp0JwGXyVS3ySrImlM8rz8uyDSYjYXafaTFm/pmmRXpKFKnpd/FxGRoryOtN4kg3wJlT5M6PcUHjim29+cjURSTukl/8ZIPOc/Cxn5Tr5+CYjUY3SfrHAk0kKokUxYWSdNzO779t9VPiKtYKVJhvm6E3qjHwxmnpu/GUHlI9IK7Ig0lMHgHgiTL73f/nvKt/Bg3yjeb0hLImluelDvc53ui1IifyOCyrfwYN8o/m/IdSaZ5/cftWjO5lUfDGZblb8NQeVbeLBvFP83pC2Ruo9afGRdnYa+NCfjpPI3Iah8C49RjOL/hrQmknoxqDOmGhyZWC5/C4LKt/Bg3ygBbMhVJs3KH8qhDBvcOL42fwOCyqdGWoU9kSYxP423Tf4ygsq38GDfKAFsyJ2IlOkrKnv5iwgq38KDfaMEsCH3IpL66lrjGyEC2P77ybfwYN8oIWzINSbJimR4hmGj/CUElc+dDevYi0imZ+o2y19AUPmItI7diES+23xEWskKk4JYf/JLEGkliER+DiKtBJHIz0GktSw3KYz1J78AkdaCSORniLQeRCI/QyQBFpsUyPqTn4NIq0Ek8hFJAEQiH5EkWGpSKOtPfoZIEiAS+YgkACKRj0gCIBL5iCTBQpOCWX/yEUkERCIfkQRAJPIRSQBEIh+RJFhmUjjrTz4iiYBI0ecjkgSIFH0+IomwyKSA1p98RBIBkWLPRyQRECn2fEQSAZFiz0ckGZaYFNL6R5+PSDIgUuT5iCQDIkWej0gyIFLk+YgkxAKTglr/2PMRSQhEijsfkYRApLjzEUmK+SaFtf6R5yOSFIgUdT4iSYFIUecjkhizTQps/ePORyQxECnmfEQSA5FizkckMRAp5nxEkmOuSaGtf9T5iCQHIkWcj0hyIFLE+YgkyEyTglv/mPMRSRBEijcfkQRBpHjzEUmSeSaFt/4R5yOSJIgUbT4iSYJI0eYjkiSIFG0+Iokyy6QA1z/efEQSBZFizUckURAp1nxEkmWOSSGuf7T5iCQLIkWaj0iyIFKk+YgkCyJFmo9IwswwKcj1jzUfkYRBpDjzEUkYRIozH5GEQaQ48xFJGnOTwlz/SPMRSRpEijIfkaRBpCjzEUkaRIoyH5HEMTYp0PWPMx+RxEGkGPMRSRxEijEfkcRBpBjzEUkeU5NCXf8o8xFJHkSKMB+R5EGkCPMRSR5EijAfkTbA0KRg1z/GfETaAESKLx+RNgCR4stHpA1ApPjyEWkLzEwKd/0jzEekLUCk6PIRaQsQKbp8RNoCRIouH5E2wcikgNc/vnxE2gREii0fkTYBkWLLR6RNQKTY8p2KlLhly/z/HOebEH2+5M7sUiSX2eSTL5qPSOSTv7eFeZRNPvmIRD75e8tHJPLJ39vCPMomn3xEIp/8veUjEvnk721hHmWTTz4ikU/+3vIRiXzy97Ywj7LJJz8YkQCCAZEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAA6yKd0yQ9P6dGWM6/Htzmv/i1WAqD/PtXknw9nOU/LZf/q8C7W1so37ZIx+I9AIeJEZbzz8WI1FZJ6lb3mdorhUH+ze36P9Iy357J96SztaX2P8si/SbpPbunye/oCMv59+Trmf+R+nKUn3NKrJXCMD99jXiekrOj/K8i+Wxr+2d5uLq1xfY/yyKdk9vr35/kMjrCcv6p3AC2dmXd6v4k9kQa5P8UO/IzSR3lJ3a3/+tP5rGTJbb/WRbplOR1+D05jY6wnF9hqyA1+Y9e0drN/0rutrK1+VWr1pbI2evvRmdri+1/lkUa/AGy/BdpJO6ZHJ3lH5OHPZEG+Ycku6RF89ZN/qVq2llqkWT3XuGL7X+IlHMtKngn+Zfkx17DRrf9T8XBvqv87JqfbUivlvJ74Ygkll/wSC21LIf5RaPCqUj5yYYvWzWC7g9Jjq0KqReOSGL5Oc/UUsNO17TKTzw7FSk/RnrYuv4wyL/mTbuXyBarpCBESvu/ezDCcn7O0dpVrEH+V9GmtCfSYP0t/yEb5B+S/PDsae9CYm9dxfY/J2ftHv2zdg+7Z+06cY/D0d7VwH5++656N/m2T/8P8m2f/u5nie1/lkW6FH+Bb+31v8EIy/mvYWvtOk2+bZFGtv/D1kYY5Jc1grXrWDmdbS22/8V+Z4O1XWgkv8DhnQ2vo6Nnfozy4yj/nOT3uZ1t/SHNCeLOhlebOKfYecsVUka4yP+yWyMM1787ZD//4nb7V/e62fxrVm9t2f3Ptkjlzb5ldNIb4SLfctNquP7dIQf5t6PL7V/dfW0tP+uLJLX/2RYJIEgQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQyQbNK/nygd77+Qav66teIXj8HX47mFV5mbPZa/+0Uykjbb5ePCjYbjZYIlJSvCAYkTyB7WaDjkhj3/VGnDVvCNbt5t1Xohr/kpGRiLQQtpsNlohkNO305FO/ZGQkIi2E7WYDTdPunCbn+uM5SS/Diau3rmfFi8eT46356pwoU6uTV8tJkuchOb0GrockvRZfqkto0l5fH67tIppfBAtgu9lgKNIxP675Kj+e8uFrf+KyaZd/uJZHQdfqq16brxXp1Ez1Gjpn2ak8aZF1l9CkHZuv2190QqSFsN1soJ4UKHbbW5Les3tafjw+X3v6YTjxPSsnTvOhn3yK/FP/2KkVqV5OMZRnvP73PCa37hLqqX6qn/BTLuKn/UWwALabDQYinfLd+7WrFx97Z+fq09/3LKursFvz1fAcRCvSbzN9ceb8lOQ6PfNGnrqEeqr6Jxzrj7/1L4IFsN1sMGjaVSPawf7x/iG9NR/OrybXvdLqWFkyXHa7nGbhjbvqEgZTDT7CAthuNpgv0m+SPJoPl1eLK0kfhRup0gjsLHtKJHUJg6kQSQK2mw3mi/Rqap2U8bfzoTzC+b0n+VGNZtlaRVqaJWimQqT1sN1sMBCpc4zUmaIevLcnG7qzXpJUu+yBIqfmwKi3hO5POKkffxFpIWw3GwxE6py160yheHCqPhzyOqg5a/f6eNEteyBScR4uu+aL6S2hc5ruZ/CLYAFsNxuMXEdKpkR6FlVSucvXt94VX73qqqdm2QORqoz8yKi3BPUnNNeRTs2VLVgA280GQ5Hy+wiOv1MiZee61VXel9CeJL+Uh0+9yYci5bcuJF/FOYveEsp/r2nnzoYLdzasgO3mEs19qeAniOSE4tTb85TfyANBgEhOuJQHLen7KcEPEMkN19dBy4H6KBwQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABPgfomCw8UvvcLAAAAAASUVORK5CYII=", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["#install.packages(\"devtools\")\n", "#library(devtools) \n", "#install_github(\"mdbrown/DecisionCurve\")\n", "\n", "# install.packages(\"devtools\") \n", "\n", "## 载入包 载入数据\n", "#library(DecisionCurve)\n", "# install.packages(\"rmda\")\n", "library(rmda)\n", "Data <- read_excel(\"K:/2020-2023HCC/579hcc/clinical data/data/train30预后最终版.xlsx\")\n", "\n", "## DCA运算\n", "\n", "# Tumor_number<- decision_curve(status~Tumor_number,data = Data, family = binomial(link ='logit'),\n", "#                           thresholds= seq(0,1, by = 0.01),\n", "#                           confidence.intervals = 0.95,study.design = 'case-control',\n", "#                           population.prevalence = 0.3) \n", "\n", "ap_score<- decision_curve(status2~ap_score,data = Data, family = binomial(link ='logit'),\n", "                          thresholds= seq(0,1, by = 0.01),\n", "                          confidence.intervals = 0.95,study.design = 'case-control',\n", "                          population.prevalence = 0.3) \n", "\n", "\n", "Handcrafted_pathomics_score<- decision_curve(status2~Handcrafted_pathomics_score,data = Data, family = binomial(link ='logit'),\n", "                          thresholds= seq(0,1, by = 0.01),\n", "                          confidence.intervals = 0.95,study.design = 'case-control',\n", "                          population.prevalence = 0.3) \n", "\n", "Deep_pathomics_score<- decision_curve(status2~Deep_pathomics_score,data = Data, family = binomial(link ='logit'),\n", "                                             thresholds= seq(0,1, by = 0.01),\n", "                                             confidence.intervals = 0.95,study.design = 'case-control',\n", "                                             population.prevalence = 0.3) \n", "\n", "#decision_curve()函数中，family =binomial(link = ‘logit’)是使用logistic回归来拟合模型。\n", "#threshold设置横坐标阈概率的范围，一般是0 ~ 1；但如果有某种具体情况，大家一致认为Pt达到某个值以上，\n", "#比如40%，则必须采取干预措施，那么0.4以后的研究就没什么意义了，可以设为0 ~ 0.4。by是指每隔多少距离计算一个数据点。\n", "#Study.design可设置研究类型，是cohort还是case-control，当研究类型为case-control时，还应加上患病率population.prevalance参数。\n", "\n", "nomogram<- decision_curve(status2~nomogram2,data = Data,\n", "          family = binomial(link ='logit'), thresholds = seq(0,1, by = 0.01),\n", "          confidence.intervals= 0.95,study.design = 'case-control',\n", "          population.prevalence= 0.3)\n", "\n", "\n", "\n", "#基本和simple相同，就是那几个联合应用的变量之间用个+号连接起来。\n", "\n", "List<- list(nomogram,ap_score,Handcrafted_pathomics_score,Deep_pathomics_score)\n", "#把刚才计算的simple和complex两个对象合成一个list，命名为List。\n", "\n", "##DCA曲线绘制\n", "plot_decision_curve(List,curve.names= c('Radiopathomics nomogram','DRSAP','Handcrafted_pathomics_score','Deep_pathomics_score'),\n", "                   cost.benefit.axis =FALSE,col = c('red','gray','blue','green','purple','black'),\n", "                   confidence.intervals =FALSE,standardize = FALSE,lwd = 3)\n", "#plot_decision_curve()函数的对象就是刚才的List，如果只画一根曲线，就不需要合成的那步，直接把List替换成simple或complex就好了。\n", "#curve.names是出图时，图例上每条曲线的名字，书写顺序要跟上面合成list时一致。cost.benefit.axis是另外附加的一条横坐标轴，损失收益比，默认值是TRUE，所在不需要时要记得设为FALSE。\n", "#col就是颜色。confidence.intervals设置是否画出曲线的置信区间，standardize设置是否对净受益率（NB）使用患病率进行校正。\n", "\n", "summary(nomogram,measure= 'sNB')\n", "# summary(nomogram,measure= 'sNB')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Note: When multiple decision curves are plotted, decision curves for 'All' are calculated using the prevalence from the first DecisionCurve object in the list provided.\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Standardized Net Benefit (95% Confidence Intervals):"]}, {"ename": "ERROR", "evalue": "Error in eval(expr, envir, enclos): Pandoc does not support newlines in simple or Rmarkdown table format!\n", "output_type": "error", "traceback": ["Error in eval(expr, envir, enclos): Pandoc does not support newlines in simple or Rmarkdown table format!\n", "Traceback:\n", "\n", "1. summary(nomogram, measure = \"sNB\")\n", "2. summary.decision_curve(nomogram, measure = \"sNB\")\n", "3. pandoc.table(out, split.table = Inf, keep.line.breaks = TRUE)\n", "4. cat(pandoc.table.return(...))\n", "5. pandoc.table.return(...)\n", "6. paste(res, sep.top, table.expand(t.colnames, t.width, justify, \n", " .     sep.col), sep.hdr, sep = \"\\n\")\n", "7. table.expand(t.colnames, t.width, justify, sep.col)\n", "8. tableExpand_cpp(cells, cols.width, justify, sep.cols, style)"]}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {}, "output_type": "display_data"}], "source": ["library(readxl)\n", "library(rmda)\n", "#Data <- read_excel(\"D:/dataset/train30.xlsx\")\n", "Data <- read_excel(\"K:/2020-2023HCC/579hcc/clinical data/data/train30预后最终版.xlsx\")\n", "\n", "## DCA运算\n", "\n", "Tumor_number<- decision_curve(status~Tumor_number,data = Data, family = binomial(link ='logit'),\n", "                          thresholds= seq(0,1, by = 0.01),\n", "                          confidence.intervals = 0.95,study.design = 'case-control',\n", "                          population.prevalence = 0.3)\n", "\n", "ap_score<- decision_curve(status~ap_score,data = Data, family = binomial(link ='logit'),\n", "                          thresholds= seq(0,1, by = 0.01),\n", "                          confidence.intervals = 0.95,study.design = 'case-control',\n", "                          population.prevalence = 0.3) \n", "\n", "\n", "Handcrafted_pathomics_score<- decision_curve(status~Handcrafted_pathomics_score,data = Data, family = binomial(link ='logit'),\n", "                                             thresholds= seq(0,1, by = 0.01),\n", "                                             confidence.intervals = 0.95,study.design = 'case-control',\n", "                                             population.prevalence = 0.3) \n", "\n", "Deep_pathomics_score<- decision_curve(status~Deep_pathomics_score,data = Data, family = binomial(link ='logit'),\n", "                                      thresholds= seq(0,1, by = 0.01),\n", "                                      confidence.intervals = 0.95,study.design = 'case-control',\n", "                                      population.prevalence = 0.3) \n", "\n", "#decision_curve()函数中，family =binomial(link = ‘logit’)是使用logistic回归来拟合模型。\n", "#threshold设置横坐标阈概率的范围，一般是0 ~ 1；但如果有某种具体情况，大家一致认为Pt达到某个值以上，\n", "#比如40%，则必须采取干预措施，那么0.4以后的研究就没什么意义了，可以设为0 ~ 0.4。by是指每隔多少距离计算一个数据点。\n", "#Study.design可设置研究类型，是cohort还是case-control，当研究类型为case-control时，还应加上患病率population.prevalance参数。\n", "\n", "nomogram<- decision_curve(status~nomogram,data = Data,\n", "                          family = binomial(link ='logit'), thresholds = seq(0,1, by = 0.01),\n", "                          confidence.intervals= 0.95,study.design = 'case-control',\n", "                          population.prevalence= 0.3)\n", "\n", "#基本和simple相同，就是那几个联合应用的变量之间用个+号连接起来。\n", "\n", "List<- list(nomogram,Tumor_number,ap_score,Handcrafted_pathomics_score,Deep_pathomics_score)\n", "#把刚才计算的simple和complex两个对象合成一个list，命名为List。\n", "\n", "##DCA曲线绘制\n", "plot_decision_curve(List,curve.names= c('Radiopathomics nomogram','Tumor number','DRSAP','Handcrafted_pathomics_score','Deep_pathomics_score'),\n", "                    cost.benefit.axis =FALSE,col = c('red','gold','gray','blue','green','purple','black'),\n", "                    confidence.intervals =FALSE,standardize = FALSE, lwd = 3)\n", "#plot_decision_curve()函数的对象就是刚才的List，如果只画一根曲线，就不需要合成的那步，直接把List替换成simple或complex就好了。\n", "#curve.names是出图时，图例上每条曲线的名字，书写顺序要跟上面合成list时一致。cost.benefit.axis是另外附加的一条横坐标轴，损失收益比，默认值是TRUE，所在不需要时要记得设为FALSE。\n", "#col就是颜色。confidence.intervals设置是否画出曲线的置信区间，standardize设置是否对净受益率（NB）使用患病率进行校正。\n", "summary(nomogram,measure= 'sNB')\n", "#summary(List,measure= 'NB')\n", "\n", "pdf(\"K:/2023写文章/2024SCI投稿/文章图/最终版/投稿版/DCA1.pdf\",height = 8, width = 9)\n", "## 最简单的方法绘制柱状图：直接dotplot就可以搞定：\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Note: When multiple decision curves are plotted, decision curves for 'All' are calculated using the prevalence from the first DecisionCurve object in the list provided.\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Standardized Net Benefit (95% Confidence Intervals):"]}, {"ename": "ERROR", "evalue": "Error in eval(expr, envir, enclos): Pandoc does not support newlines in simple or Rmarkdown table format!\n", "output_type": "error", "traceback": ["Error in eval(expr, envir, enclos): Pandoc does not support newlines in simple or Rmarkdown table format!\n", "Traceback:\n", "\n", "1. summary(nomogram, measure = \"sNB\")\n", "2. summary.decision_curve(nomogram, measure = \"sNB\")\n", "3. pandoc.table(out, split.table = Inf, keep.line.breaks = TRUE)\n", "4. cat(pandoc.table.return(...))\n", "5. pandoc.table.return(...)\n", "6. paste(res, sep.top, table.expand(t.colnames, t.width, justify, \n", " .     sep.col), sep.hdr, sep = \"\\n\")\n", "7. table.expand(t.colnames, t.width, justify, sep.col)\n", "8. tableExpand_cpp(cells, cols.width, justify, sep.cols, style)"]}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {}, "output_type": "display_data"}], "source": ["library(readxl)\n", "library(rmda)\n", "#Data <- read_excel(\"D:/dataset/train30.xlsx\")\n", "Data <- read_excel(\"K:/2020-2023HCC/579hcc/clinical data/data/train30预后最终版.xlsx\")\n", "\n", "## DCA运算\n", "\n", "Tumor_number<- decision_curve(status~Tumor_number,data = Data, family = binomial(link ='logit'),\n", "                          thresholds= seq(0,1, by = 0.01),\n", "                          confidence.intervals = 0.95,study.design = 'case-control',\n", "                          population.prevalence = 0.3)\n", "\n", "ap_score<- decision_curve(status~ap_score,data = Data, family = binomial(link ='logit'),\n", "                          thresholds= seq(0,1, by = 0.01),\n", "                          confidence.intervals = 0.95,study.design = 'case-control',\n", "                          population.prevalence = 0.3) \n", "\n", "\n", "Handcrafted_pathomics_score<- decision_curve(status~Handcrafted_pathomics_score,data = Data, family = binomial(link ='logit'),\n", "                                             thresholds= seq(0,1, by = 0.01),\n", "                                             confidence.intervals = 0.95,study.design = 'case-control',\n", "                                             population.prevalence = 0.3) \n", "\n", "Deep_pathomics_score<- decision_curve(status~Deep_pathomics_score,data = Data, family = binomial(link ='logit'),\n", "                                      thresholds= seq(0,1, by = 0.01),\n", "                                      confidence.intervals = 0.95,study.design = 'case-control',\n", "                                      population.prevalence = 0.3) \n", "\n", "#decision_curve()函数中，family =binomial(link = ‘logit’)是使用logistic回归来拟合模型。\n", "#threshold设置横坐标阈概率的范围，一般是0 ~ 1；但如果有某种具体情况，大家一致认为Pt达到某个值以上，\n", "#比如40%，则必须采取干预措施，那么0.4以后的研究就没什么意义了，可以设为0 ~ 0.4。by是指每隔多少距离计算一个数据点。\n", "#Study.design可设置研究类型，是cohort还是case-control，当研究类型为case-control时，还应加上患病率population.prevalance参数。\n", "\n", "nomogram<- decision_curve(status~nomogram,data = Data,\n", "                          family = binomial(link ='logit'), thresholds = seq(0,1, by = 0.01),\n", "                          confidence.intervals= 0.95,study.design = 'case-control',\n", "                          population.prevalence= 0.3)\n", "\n", "#基本和simple相同，就是那几个联合应用的变量之间用个+号连接起来。\n", "\n", "List<- list(nomogram,Tumor_number,ap_score,Handcrafted_pathomics_score,Deep_pathomics_score)\n", "#把刚才计算的simple和complex两个对象合成一个list，命名为List。\n", "\n", "##DCA曲线绘制\n", "plot_decision_curve(List,curve.names= c('Radiopathomics nomogram','Tumor number','DRSAP','Handcrafted_pathomics_score','Deep_pathomics_score'),\n", "                    cost.benefit.axis =FALSE,col = c('red','gold','gray','blue','green','purple','black'),\n", "                    confidence.intervals =FALSE,standardize = FALSE, lwd = 3)\n", "#plot_decision_curve()函数的对象就是刚才的List，如果只画一根曲线，就不需要合成的那步，直接把List替换成simple或complex就好了。\n", "#curve.names是出图时，图例上每条曲线的名字，书写顺序要跟上面合成list时一致。cost.benefit.axis是另外附加的一条横坐标轴，损失收益比，默认值是TRUE，所在不需要时要记得设为FALSE。\n", "#col就是颜色。confidence.intervals设置是否画出曲线的置信区间，standardize设置是否对净受益率（NB）使用患病率进行校正。\n", "summary(nomogram,measure= 'sNB')\n", "#summary(List,measure= 'NB')\n", "\n", "pdf(\"K:/2023写文章/2024SCI投稿/文章图/最终版/投稿版/DCA1.pdf\",height = 8, width = 9)\n", "## 最简单的方法绘制柱状图：直接dotplot就可以搞定：\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Note: When multiple decision curves are plotted, decision curves for 'All' are calculated using the prevalence from the first DecisionCurve object in the list provided.\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Standardized Net Benefit (95% Confidence Intervals):"]}, {"ename": "ERROR", "evalue": "Error in eval(expr, envir, enclos): Pandoc does not support newlines in simple or Rmarkdown table format!\n", "output_type": "error", "traceback": ["Error in eval(expr, envir, enclos): Pandoc does not support newlines in simple or Rmarkdown table format!\n", "Traceback:\n", "\n", "1. summary(nomogram, measure = \"sNB\")\n", "2. summary.decision_curve(nomogram, measure = \"sNB\")\n", "3. pandoc.table(out, split.table = Inf, keep.line.breaks = TRUE)\n", "4. cat(pandoc.table.return(...))\n", "5. pandoc.table.return(...)\n", "6. paste(res, sep.top, table.expand(t.colnames, t.width, justify, \n", " .     sep.col), sep.hdr, sep = \"\\n\")\n", "7. table.expand(t.colnames, t.width, justify, sep.col)\n", "8. tableExpand_cpp(cells, cols.width, justify, sep.cols, style)"]}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {}, "output_type": "display_data"}], "source": ["library(readxl)\n", "library(rmda)\n", "#Data <- read_excel(\"D:/dataset/train30.xlsx\")\n", "Data <- read_excel(\"K:/2020-2023HCC/579hcc/clinical data/data/train30预后最终版.xlsx\")\n", "\n", "## DCA运算\n", "\n", "Tumor_number<- decision_curve(status~Tumor_number,data = Data, family = binomial(link ='logit'),\n", "                          thresholds= seq(0,1, by = 0.01),\n", "                          confidence.intervals = 0.95,study.design = 'case-control',\n", "                          population.prevalence = 0.3)\n", "\n", "ap_score<- decision_curve(status~ap_score,data = Data, family = binomial(link ='logit'),\n", "                          thresholds= seq(0,1, by = 0.01),\n", "                          confidence.intervals = 0.95,study.design = 'case-control',\n", "                          population.prevalence = 0.3) \n", "\n", "\n", "Handcrafted_pathomics_score<- decision_curve(status~Handcrafted_pathomics_score,data = Data, family = binomial(link ='logit'),\n", "                                             thresholds= seq(0,1, by = 0.01),\n", "                                             confidence.intervals = 0.95,study.design = 'case-control',\n", "                                             population.prevalence = 0.3) \n", "\n", "Deep_pathomics_score<- decision_curve(status~Deep_pathomics_score,data = Data, family = binomial(link ='logit'),\n", "                                      thresholds= seq(0,1, by = 0.01),\n", "                                      confidence.intervals = 0.95,study.design = 'case-control',\n", "                                      population.prevalence = 0.3) \n", "\n", "#decision_curve()函数中，family =binomial(link = ‘logit’)是使用logistic回归来拟合模型。\n", "#threshold设置横坐标阈概率的范围，一般是0 ~ 1；但如果有某种具体情况，大家一致认为Pt达到某个值以上，\n", "#比如40%，则必须采取干预措施，那么0.4以后的研究就没什么意义了，可以设为0 ~ 0.4。by是指每隔多少距离计算一个数据点。\n", "#Study.design可设置研究类型，是cohort还是case-control，当研究类型为case-control时，还应加上患病率population.prevalance参数。\n", "\n", "nomogram<- decision_curve(status~nomogram,data = Data,\n", "                          family = binomial(link ='logit'), thresholds = seq(0,1, by = 0.01),\n", "                          confidence.intervals= 0.95,study.design = 'case-control',\n", "                          population.prevalence= 0.3)\n", "\n", "#基本和simple相同，就是那几个联合应用的变量之间用个+号连接起来。\n", "\n", "List<- list(nomogram,Tumor_number,ap_score,Handcrafted_pathomics_score,Deep_pathomics_score)\n", "#把刚才计算的simple和complex两个对象合成一个list，命名为List。\n", "\n", "##DCA曲线绘制\n", "plot_decision_curve(List,curve.names= c('Radiopathomics nomogram','Tumor number','DRSAP','Handcrafted_pathomics_score','Deep_pathomics_score'),\n", "                    cost.benefit.axis =FALSE,col = c('red','gold','gray','blue','green','purple','black'),\n", "                    confidence.intervals =FALSE,standardize = FALSE, lwd = 3)\n", "#plot_decision_curve()函数的对象就是刚才的List，如果只画一根曲线，就不需要合成的那步，直接把List替换成simple或complex就好了。\n", "#curve.names是出图时，图例上每条曲线的名字，书写顺序要跟上面合成list时一致。cost.benefit.axis是另外附加的一条横坐标轴，损失收益比，默认值是TRUE，所在不需要时要记得设为FALSE。\n", "#col就是颜色。confidence.intervals设置是否画出曲线的置信区间，standardize设置是否对净受益率（NB）使用患病率进行校正。\n", "summary(nomogram,measure= 'sNB')\n", "#summary(List,measure= 'NB')\n", "\n", "pdf(\"K:/2023写文章/2024SCI投稿/文章图/最终版/投稿版/DCA1.pdf\",height = 8, width = 9)\n", "## 最简单的方法绘制柱状图：直接dotplot就可以搞定：\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Note: When multiple decision curves are plotted, decision curves for 'All' are calculated using the prevalence from the first DecisionCurve object in the list provided.\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Standardized Net Benefit (95% Confidence Intervals):"]}, {"ename": "ERROR", "evalue": "Error in eval(expr, envir, enclos): Pandoc does not support newlines in simple or Rmarkdown table format!\n", "output_type": "error", "traceback": ["Error in eval(expr, envir, enclos): Pandoc does not support newlines in simple or Rmarkdown table format!\n", "Traceback:\n", "\n", "1. summary(nomogram, measure = \"sNB\")\n", "2. summary.decision_curve(nomogram, measure = \"sNB\")\n", "3. pandoc.table(out, split.table = Inf, keep.line.breaks = TRUE)\n", "4. cat(pandoc.table.return(...))\n", "5. pandoc.table.return(...)\n", "6. paste(res, sep.top, table.expand(t.colnames, t.width, justify, \n", " .     sep.col), sep.hdr, sep = \"\\n\")\n", "7. table.expand(t.colnames, t.width, justify, sep.col)\n", "8. tableExpand_cpp(cells, cols.width, justify, sep.cols, style)"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0gAAANICAMAAADKOT/pAAAARVBMVEUAAAAAAP8A/wBNTU1oaGh8fHyMjIyampqgIPCnp6eysrK9vb2+vr7Hx8fQ0NDZ2dnh4eHp6enr6+vw8PD/AAD/1wD///85tve+AAAACXBIWXMAABJ0AAASdAHeZh94AAAgAElEQVR4nO2di3qquhZGaVG77cXqsof3f9Qj9wSCQpgkIRnj+3a3IvDLZawZEEJWAMBqMt9fACAGEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABEAlAAEQCEACRAARAJAABfIr0z2N2wPkZeGf5xkSk4PJpJHgHkWLIRyTvIFIM+YjkHUSKIR+RvINIMeQjkncQKYZ8RPIOIsWQj0jeQaQY8hHJO4gUQz4ieQeRYshHJO8gUgz5iOQdRIohH5G8g0gx5COSdxAphnxE8g4ixZCPSN5BpBjyEck7iBRDPiJ5B5FiyEck7yBSDPmI5B1EiiFfeJM0vRAcf6c+rv97zaUdfUbk7G8XJogUQ/42ImWZ2aTZIh2ydvQZkQu+X4ggUgz54iJV/ztnx2cfz53P7h2ZBSLFkL+NSFMKIJIBRIohf1ORLqcsy8/1kHOenZWm3dchO3w1Y57bkbrxm+7e6s8+i+EEn9XAc5adu6jH7I+3ah7HxyHaRflCt9PLWSgfaV/0fshO6lKMpxNZZ04mESPQHdl3vmGT/M3EODu1afdZHy5Vu9yxfHXqRDrW5ySqKT7bl/34nUin8sWXaYLLsRu3/Ti/P4yo5/HVf6G8fP/5dBbKR/XLjy79rC7FaDoRECmG/PEmmeuR0aTuZMO1evNTFD/Vjv6T5dfimrcitW9/qj29f9mN3zbtjveHGofBBNXA+m/ezu/x7qPct/My+aecpP1CM2ahfHRRv+jj88G30qeTAZFiyBevSPW/7Vd10OPPqTqNd2lFOmWX6u2xfFu/PGnjtyL9FoYJ6oG3oj8LWM3+XmvVN+tmz0L5qH2ZtWPp30qfTgZEiiF/i2OkQ97uzLfL51ERo9vxB2+7EQbj939HE+gf9Xv14+jldB1qPGcWky+ffCsZECmG/C1E+q3/zW4PPeaLNBzfQqTiszwmym/aF1on0vS3kgGRYsjf5KzdqW6ofWSHr8ttvkij8W1EerTLzgf1GGnmLCZfPvlWMiBSDPmbiHRtTzY8/tyaY6Sytfc7PEY6tccdl+xDG3+4y2oT6B+V/x27YyT1a8yehfKRdozUzwGROgLdkX3nbyJSU5JKR6710cXl5Vm7iza+ekA/mqAohiJ9lSfSzuVZu0N9js1QkZ7MYuqsXTMH5VsVBSJ5zA44fxuR7lVJOivX3Z2Un2cK/Ted+iemQhv/kLVntg0TFMVQpP53pJ/BpX6zZjH6HSlTRFK+FSIVwe7IvvO3Eemx95VmfJTXgTdntj8HVzbk/VUGp/aqgn7838NAJG2CYiRSdbauv7KhP289axbKR/U1Er+KSMq3QqQi2B3Zd77PTVJ/Ae/fwMTERbfbZDmZRIxAd2Tf+d5348BEqq5juJ8EL6V7HelkEjEC3ZF953vfjQMTqbmyTuz6nxkgUgz53nfjwEQqvh7HWAeH9QiR4sgPbDdOEUSKIR+RvINIMeQjkncQKYZ8RPIOIsWQj0jeQaQY8hHJO4gUQ77oJskUJOerRWw0Y38gUgz5iOQdRIohX36TbLurI5LlJGIEuiP7zkck7yBSDPnbiaTd6DDVH2PbCWMzRduZ4/S0mdKfZDmbfDybvYFIMeQbNsn/ZjIRZBJpsj/GphPGdoq2M8fpaZs+I6u7HE7m2ewNRIohf7xJ5no0ZZJJpMn+GJtOGNsp2s4cp6fN1LvCHwPvx/omdWU2ewORYsh3U5Em+2McdML4200xNW2m9lNSynPve1DZKYgUQ76jYyTDe/Xl9BRPpx12r7BPECmGfETyjgeRlC4qFhPojuw7f9cijTJ3iUuRrqfyPOdn32nScgLdkX3nbyzSr0mGUVeN2pTdFMZp2/4ku74ctcxd4lCka2XQOfu4F7dTZleTAt2RfedvJ9LhsaHuR5MMo64atSnLv9PTKv1JVrMpvkY+7g2HIlVPvjnXPVLc+140FxHojuw7fzuRqkd+nUwyjLpq1KYs/05PW/UyWfcn2cym7DAfkWZOV6/Hk/JmMYHuyL7ztxOp7BLyw3icM+qqUZuy+js5baY8DbO8siH7kH1YkQeci/RTt+ksu0oKdEf2nb/nPTASnDbtPtofru8flpeCBLoj+85HJO84FOmed7Xbuu++QHdk3/mI5B2nvyOdW31y20sTA92Rfecjkne4siGGfETyDiLFkI9I3kGkGPIRyTu+RLL8zeBdInsFiARmwhFJ7bzm3wTv7+9THyUNInknzKbdVMRDJL81iYoEZvYlkneTEAnM7Eykf4hkQniT1O3rw/muvOuejXw/Hx5vuov38+639eYpedVlc+nhVKTfz7rLmNP5xc35T0XyaVJKItWXZCsHr9VGu+fNZ7VllyxrbyjSp0oNl5cIHZTTCc9v7JsWqUAkA+IilX9vx3orNeeFzvW7j+x4qz6rL075KG8wU6e6H3fbo9YqHIp0zvKfa/Xqdsmfr+3nInk0KSWRyjvzLsq79i6IqhTd27vF80fjTpvq7vQZyMHgUKQ8u3avr8/X9hOR/JqUlkiXqtiMu2/o+Xn8g3gub5IdjZcazu9HMr2ZH1HuSIg0wrC+vmdinF0zv/o+Zr1pd87UswnHx4HTb9NOpyJtP0mFUEXyWpJ2I9Jcj4wmKbe7FMrJhnrzHcvzee0ZvGoz5nVrrzuy4hhps0kqHsdIl/ofszXHSIVXk3YjkkhF0kQ6tv8MXsr+FvLqVN1PtRmbtl131m6//Q6vwOXp76Ny1u7wdG0/F8lj424/Iq1hKNLjzyG/KCP8fubVyfBD9fda92TD70jbT9Lwe65+R8pPn9a/I1V//ZWktES69X0E/dYdd3dU8ty6fxd333fJavZ2ZUP9P0TS2Uikn/ZhRkXZkeNJ/ah68dmJ9FkgkotJxCJ6kfyYlJZIh+7BElX3nuVBUtuzZ3Wa4dBUqZt6di9R9imSt5KUkkiDKxvqkvRo4n09Dm9/jw+hrt3j9Y6lZIi0/SRiEYpIXkxKRST9Wrtq4L0uSef+Gq9z1233pX4Mn+y32Bc7FcmXSSmJdPxs39X/P9cF6PqRPz4rT3jn/W+BeY5ILiYRi+h3JERSSHoXDoM9i+TBJEQCM7sVyU9JQiQws2uR3JuESGBmvyJ5KUmIBGb2LZJzkxAJzOxYJB8mIRKY2bNIHhp3iARm9i6SY5MQCczsWiT3JQmRwMzuRXJrEiKBmX2L5NykJETSb5CdM+IU948sOxcX84fzrs27zB7V77V+OxfJdeMOkYwjTlHeEP15mBhr1o5fT4xIliwSyaVJiGQccXqE2/RYC+zYwYXlexfJsUmIZBzx6QiIJDeJWIRhR0KkbUW6PNpn9VPoH/XllOX1XUrnskO1esTHy2Pd+cn9UN6y1E2gdBpVjvd1yPLmIRbK1GrquQkazKL9rE5+zObw1UzwWQ08Z0rfEu23KS7HLDsOD8+Ugd2I6hzrJVC/6uyVtmx0y0nEIswiuTNpPyK9zcQ0N1WkpoOTek/N245Oqp7VTtWIx7Yvu2rAWZlAF+nUPzxBmVpN/WxHGMyimXVWdRdx7GbTTHA5duOq3+arnoeugzKwG1GbY7UE6ledzf5FclqSdiPSXI9MJmkCZGXvjz9NG+14f+yLh/J9fi2ueTn0pxz4Ue/Ix3uhTaA27S7lp/djeXe6MrWWWg/9GWQWxuSfbmD9t7lDt/82Vb++P3WPex39wH7E4Ry1rzqfOERyZtJuRFpXkbQmWT2o+tN2KnSqXl26l/d6R/5V56FMVr48Vf0a38uWkzK1lnqphp60WbTz6ZPrsY79wLZPveG3MXjQD+xHHM1R/arziUAklyVpPyKtYHCy4Xb5PGo7dTeG8lKdrJtAd0Epcvr42uSTmRPJ+kf9PB8HTqfrtdDpB46+tjbzwT8jc4hEJFcmJSjSsdupZorUT2AtkilzoUjFZ3lAN3x6YDcQkUy4Myk9kT6yw9fltkAkZYJi5II64rRIxsylIj2aa+fD4BipH/hcpNfraEQUIrlr3KUnUvVmuFPXxxW/5cujclTSTz0W6dQfsyhTa6n1kdPHRKZ+jHQyi9R/m8GiDJfvODpG6uaoftUFK83JJGIR0yK5MSlFkX6L6/B45dKfd/sqT3GdlQ4ilQlakcr2VXVy7DH2SZtaS62HXgaZNy1ZO8dWFEOR+m9zqM/86RWpH9iPOJqj+lUXrLQlI1tPIhYxtSMjkiCaSG3Pqr/anlv90vLRH8/UvyNVEykT1IMOWVUh6uOe6qBFmVpNrX9e0mdRT6wkq7/6FMVQpP7b/PTfokcZaP4dqR5N+arzV9qSka0nEYt4IpITk9IT6XHAkh1/R22pT+XKhsferz7WpZ+gHvR7qJtaXw8pmocnfZqvbDg1Vxgos6gnVpO/8v7KhmIkUvdt6osYho8PUgZ2Iw7nqH/V2Stt0di2k4hFTO7IiLR3rA7xwyEikVyYhEibgUgbsFwkRyUJkURRr6HYSiTDdRqbxDiZRCziqUgOTEIkURBpYyxEclOSEAnMRCXS9iYhEpiJRyQnJQmRwExcIm1uEiKBmYhEclGSEAnMRCbS1iYhEpiJSSQHJSkNkeqTxYfzXWh+e+nkcQ2xibSxSSmJtPSyzUl208njGqISafuSlIpI5d/bcWFHOs9nt19HZhGdSNualJJIZSmxuMNtanaIJDCJWMSrHRmRJGj3+ep2Va27xO5lpnTnqE24504e1xCfSJuatB+R3mdinF0zv3t1g6nSXWL/UunOUZtw1508riEykbY2aTcizfXIuLa0O/uU7hKVl0p3jtqEu+7kcQ2xibRx4243IolUpKa7EaVnx+7lqDvHZvxdd/K4hhhF2tCk/Yi0hkHvJ91NCNpLfUxtwuas3+46eVxDdCJtW5LSEulW/xNvJ9IOO3lcQ5QibWdSWiL9KL1sqcOLGSLtsZPHNcQn0qYmpSXSoTzeGPXs2IzSdeeoTbjrTh7XEKFIWzbuUhKpubJB6S5Real056hNuOtOHtcQqUhbmZSKSOq1dkp3if1LpTtHbcJdd/K4hhhF2rAkpSTS8bN5q3SX2L3MlO4c1Ql33cnjGmIVaSOT0hBpBuaDeeeH+OEQpUjblSREahMRSSdakbYxCZHaROXUc/+bzVYiqRmBEqdIm5UkRGoTEUknXpE2MQmRwEykIm1VkhAJzEQs0hYmIRKYiVWkjUoSIoGZmEXawCREAjPRirRNSUIkMBO1SPImIRKYiVekTUpSMiLl7W0MozuEwEjcIomblIpIl6ztGgGR5hGxSFuUpFRE+sjOzT17iDSPyEWSNikVkR4Nu7yeKSLNI2aRNjApEZHKzhrOdZ91iDSPqEWSb9wlIlLZG8Jv3U0pIs0jepFkTdqPSP/NxDS7uueRvOpjEZHmEbdI4iVpPyKtoeqGq2nbIdI84hdJ1KQ0RKq64SquVfc9iDSPyEWSNikJkW7dXXQ3RJpL7CIJN+6SEOmzE+kTkeaSgkiCJiUh0iGre7G6tf0CI9JrohdJtiSlINK16/bxmF0RaSZJiCRnUgoinbt+iC/ZGZFmEr9IoiUpBZHyXH2JSPNIQyQxk1IQCWxIQCTJkoRIYCYRkaRMQiQwk4JIgiUJkcAMIi0CkcBMKiIJmYRIYCYJkeRKEiKBmWREkjEJkcBMGiKJlSREAjPpiCRiEiKBmUREkipJiARmEhJJwqQkRGr7huQauwWkIpJQSUpEJKW7YphHSiIJmJSISOWtsQUiLSEZkWRKUiIiNffIItJ8khJpvUmJiNTcJFuL9HXIDl/129spy+ti9RiYf4mm7px0RBIpSYmIVHxUHXJVIh2rblCO1du86RGlOHUDoSYtkVabtB+RspmY5pYV965Pu58svxbXvOws8mHOvfgqP7mUr+7H7p50SEkkiZK0H5FWUPrzlX3VL06VLZey+mRdmTpVvRnfu15SIDWR1pqUjEjF4eGK0l9D/7J+NV3PEiUlkQRKUjoi/WYfiLSA1ERaaVI6Ij2ab9cnIokGxkBSIq0vSQmJdMsO6jHSSRXpxGmGIcmJtM6khESqugAfnLVrP60GFl+cbOhJS6TVJSklkYp89DtS92k9ML+J5u6a9ERaZVJSIl2aKxvy7sqG/u/Xo933gUc9iYm0tiQlIRJYkKBIa0xCJDCTmkgrSxIigZkURVphEiKBmeREKk36s54YkcBMkiLZlyREAjPpibSqJCESmElTJOuShEhgJkGR1pQkRAIziYpkW5IQCcykKNKKkoRIYCZVkSxLEiKBmSRFsr+8AZHATLIi2ZmESGAmTZGsSxIigZl0RbIyCZHATKIi2ZakUEUC7yzfmNYi3T+y7Djr0TtORLI5Ax6oSOTvMd9WpHvVc3TTYUYAItmUpKg2JPl+821FOpe94N6/8qrrdd8iWbbtotqQ5PvNtxUprye85YdbGCJZmBTVhiTfb76tSK079+MxAJHsSlJUG5J8v/m2Ih2qRxhUr45hiLTcpKg2JPl+821F+so+mle37OhfJKuSFNWGJN9vvvXp73Nnz+XFeXdXIi02KaoNSb7ffPsfZK9dX9G3D/8i/SES+T7zo7iyocSiJEW1Icn3mx+NSBYlKaoNSb7f/KhEWmhSVBuSfL/5IiIFcLLBpiRFtSHJ95u/kUjqlbT/3FCVJEdZEDmeRLKMEK5Iixt3Uf2LSL7f/HhEKv7eEIl8X/kxibS0JEW1Icn3m28v0u/nqb4l6fxrGSEu0sKSFNWGJN9vvvWNfQfldMLRLkJ6RS4tSVFtSPL95tvf2Jf/XKtXt0uena0i5EVaVpKi2pDk+823v7Hv2r2+ZrlVhLxIy0pSVBuSfL/5a2/sG7+ZHyG+IheWpKg2JPl+82OqSE1Jmj16VBuSfL/5K46RLrfqVTjHSEvbdlFtSPL95luf/j4qZ+0O92djuhNpYdsuqg1Jvt/8Fb8jnavfkfLTZyC/IxVLS1JUG5J8v/kRXdlQVCItKElRbUjy/ebHJdKykhTVhiTfb358Is0vSVFtSPL95scn0vwz4FFtSPL95kcm0qK2XVQbkny/+RGKNLttF9WGJN9vfmwiLSlJUW1I8v3mxyjS3JIU1YYk329+dCItKElRbUjy/ebHJ9L864Si2pDk+82PUqSZJSmqDUm+3/wIRZpdkqLakOT7zY9XpNcmRbUhyfebH6NIc0tSVBuSfL/5iOQP8iPKj1KkmR1zRbUhyfebH6dI87ovjmpDku83P1KRZj0uKaoNSb7f/FhFKmaUpKg2JPl+86MW6YVJUW1I8v3mRyvSjJIU1YYk329+3CI9NymqDUm+3/x4RXpdkqLakOT7zY9cpL9nI0S1Icn3mx+xSMWrtl1UG5J8v/kxi/SqJEW1Icn3mx+zSN+IRL6r/JhFetW2i2pDku83P2qRXpSkqDYk+X7zjQ/ey58+OGxNhAYikR9Nvkmk2/NHWa6J0HAi0hOTotqQ5PvNb/fyS6Zy2CJixPYr8nlJimpDku83v9vLD6pHLx4dZhkxxI1Ib5MmRbUhyfebP//h5CIRGm5Emi5JUW1I8v3mR33Wrvh+WpKi2pDk+81v9/KyGimNuy0iRjhYkYhEvpv8BESaNCmqDUm+3/x2Lz9/Ss7VGDHChUh1STKbFNWGJN9vvl6RJOc8ihjhYkVWIk2UpKg2JPl+83uRbtGKNFWSotqQ5PvNb/fyD+0H2WiOkbq2ncmkqDYk+X7z2738fopZJHNJimpDku83P+4fZIu+bWcwKaoNSb7f/PhFakvS2KSoNiT5fvPjvrKhQCTy3eTre/nlVFal000ywbNIfdtuZFJUG5J8v/naXn6szzNkuahJvkXqStLQpKg2JPl+89W9/Cs73kuRvrKPrSI0nIpUjE2KakOS7zdf3cvz7F6fcIjo9HfRXtxgMCmqDUm+3/zhWbtIRSpL0sikqDYk+X7z1b380FSkazS3mtc0bbvaJEWlqDYk+X7zDcdIlzz72ipCw61IY5Oi2pDk+83X9vL2MqGjZIJ/kYq2bdeY1KoU1YYk32/++Hek7PQjGRCCSH1J0opSVBuSfL/50V/ZUKKKpJgU1YYk329+EiIVtUiNSd3Zu6g2JPl+85MRqS9JnUlRbUjy/eZre/nnIbr7kRp0kRqTotqQ5PvNV/fyzwhv7GvQ23aNSVFtSPL95uuXCIn+fmSK0HArklaSCkQinxv7LDCVpKg2JPl+89W9/JTdJWdtitBwLNKoJD195LkDotqRUs9X9/JbfhR9DIUhQsO1SKGZFNWOlHq+3rSL9mRD0aqkDEAk8uVIR6Tqbgr12czeS1JUO1Lq+Wn8IFthMsntNxgQ1Y6Uen5KIlU3nSsmIRL5YsTfi1DHQ6Q3zSTfbbuodqTU8+PvRainManPRyTypYi/F6Eek0g+TYpqR0o9P4FehHoGR0n/PB8lRbUjpZ6fQC9CPU1JevyvyUck8oVIoRehnuZ0Q2PSP8+nG6LakVLPT6EXoZ6+JH3X+YhEvgxJ9CLUo5UkRCJfjCR6EVL4fnsbiOTPpKh2pNTzE7qyoaFv25X5iES+CGmK9K6K5M2kqHak1POHe/nvMcvPsvf3BSZSX5KqfEQiX4JuL78+DPoqrtXJhlzUpBBFekck8jcR6bcy6HzMr8X9mJ23iBjhS6S2JLUi+TIpqh0p9fx2L6/kOWfZ5fH6nuVbRIzwtSLbklTnIxL5ArR7eX1VUHNtUKyXCDW0JakTyZNJUe1IqeenLFJ9xZ2/khTVjpR6fooiFeoFd4hEvgRpiqSVJG9tu6h2pNTze5E0togY4W9FhlGSotqRUs9PVKRhSfLyLaLakVLPT+8SoZJKpKI1yVfbLqodKfX8NEWq23b/PJekqHak1PNTFemtEclnSYpqR0o9P2WRtMadhy8R1Y6Uen6iIjVHSa1JiET+ShIXqehE8mBSVDtS6vmpilQ0vRf7LElR7Uip56crUl+SvhGJ/LWkLVLXuPPStotqR0o93/gw5jzu+5Fq3vrGXeGnJEW1I6WebxLpFv0lQiVv/Snwwk9JimpHSj2/3csv2qV2sXZZrPLWmOTvdENUO1Lq+d1eflA9En24eaAilSYV/VXgiET+GozHSLIELFJrUuGlbRfVjpR6frJn7Yq6JNUmle8QifwVJPQM2RGVSIUikmOTfC8/+YKk9AzZIW+eS5Lv5SdfkJSeITvKH5Ukx/lu48jfMj+pZ8gO87uS5Kdt5335yZcjqWfIjvI1kZyXJP/LT74YaT1DdpiPSOQLkdYzZIf5/U9J5XvXbTv/y0++GIk9Q3aY77UkBbD85EuR2jNkB/ltSfLS52oAy0++FClf2VDmayI5LkkhLD/5QiBSd4dfgUjkW5O6SIXHtl0Qy0++DGn2/a3ke2zbBbH85MuASHXbrhfJnUlBLD/5Moz38ushyy/bRjSEsSK1gySnJSmM5SdfhOFefv/Isk/JgD2I9IZI5K9ksJd/ZZnw3UjBi1R4a9sFsvzkS6Dt5b8H4e4axhEqgaxITSSXJSmQ5SdfAmUvv50y2YvsxhE6gaxIRCJ/Pf1e/pllH3fJWY8jBoSyIodnG1yZFMryky9A169dnh2ukjMeR4wIZUVqZxsclqRQlp98AZL/Hanw17YLZfnJFwCRivYh580bd227YJaf/PUkf61diXaQ5K4kBbP85K8HkQpvbbtglp/89diKtKApGL5Intp24Sw/+auxFekrPpGcl6Rwlp/81Vg37a753J4ddiGSahIikb8Y+2Oka3ZeGRHQivRyvV1Ay0/+WlacbPjK5v2Cux+RHJekgJaf/LVw1q5i1HWDC5MCWn7y14JINW1PkfU7NyaFtPzkrwSRat5GfTdsb1JIy0/+ShCpQS9JlUnfhmk2y/cA+YKIiLT735EK5YmyjUsPj743Nimo5Sd/HRuJpP5Y+28X/O8h0r9/7w2PAd8lvr8V+MGTSJYRYf2LpJSksipVHm1bksJafvJXgUgt/6ueTPGQpzbpe3uTwlp+8leBSB31E2UreyqRiq2PkgJbfvLXYC/S72f9OKXT+UXHQzsSqTWpOeWwcUkKbPnJX4OtSPeDcjrh+eWrexGpbtt1JjUv3eU7h3xBbEU6Z/lPfand7ZI/v3x1LyK1JalwVZJCW37yV2ArUq5csXrNcquI0FZkW5IeAhUuSlJoy0/+CuzvkJ16Mz8itBX5v7fOpKIvSX1nDlvnu4Z8QahICn1JKtqS1F/q4CDfMeQLsuIY6VL3th/PMZKhJL0jEvmzsD79fVTO2h2ednW8H5GGJaljm5MO4S0/+das+B3pXP2OlJ8+I/kdqTCKVJ3B2+Yih/CWn3xruLJBRW3btYdHnUjiJoW3/ORbg0gaakkqmoOj+nY/RCL/GYikoYnU0BQmeZMCXH7ybUEkDa1t1/IQ6W+LkhTg8pNvCyLpTJSkvy1KUojLT74liKQzJdIWJSnE5SffEkTSMbXt3jcqSSEuP/mWINIAhyUpyOUn3w5EGjAp0p94SQpy+cm3A5EGTLbt5EtSkMtPvh2INMRdSQpz+cm3ApGGTIv0J1ySwlx+8q1ApCETbbtCviSFufzkW4FIIwwl6V0pSXImBcKmYGAAABYFSURBVLr85NuASCMm2nZdSRIzKdDlJ98GRBrxpG0n+7NsoMtPvg2INMbctivETQp1+cm3AJHGTBwktc+WHZhk/0SyUJeffAsQacxU2659jp9q0t+KZ/uFuvzkW4BIBqbadopJzeA/RCK/ApEMTLbtBo27vxbhfEeQLwgiGZi+4VwtSa1DiEQ+IpkxiKQ17kqRulqESOQjkpmJnhs6k8q2Xdeks27bhbv85C8GkUyY2nZDk3p7EIl8RDJjEkk1qRSpG45I5COSGaNI7RMqapH6H2Vt23YBLz/5S0EkI6aDpK4klepoVzcgEvmIZGayJDXPeEEk8jUQyYxZpGJSJBuTQl5+8heCSGYeIv3PMFgVaXVJCnn5yV8IIpkxHyT1Ikm07UJefvIXgkgTLD1IsjAp6OUnfxmINIGDtl3Qy0/+MhBpglciCbTtgl5+8peBSBMsFmm5SUEvP/nLQKQJ3swmaSINTFqqUtDLT/4yEGmKSZEmS9JSk8JefvIXgUhTlCK9KknqB8tVCnv5yV8EIk0xRyS9X66lJoW9/OQvApGmeHmQNO5Sf6FJYS8/+YtApEnMJUkXadhV5CKTAl9+8peASJNMijRdkpb9oBT48pO/BESa5OVB0tqSFPjyk78ERJrk7WXbbmVJCnz5yV8CIk1jK9Jck0JffvIXgEjTzBJpRUkKffnJXwAiTVOJNDLpXaxtF/ryk78ARJpm3kGS/emG0Jef/AUg0hMmS1L/Zk1JCn75yZ8PIj1hnkjWJSn45Sd/Poj0hBkirSlJwS8/+fNBpCe8GU16lypJwS8/+fNBpGdsW5LCX37yZ4NIz5gpkmVJCn/5yZ8NIj2jFsnQtlPfWpek8Jef/Nkg0jMmD5LUt9YlKfzlJ382iPSUGWcb7EvSDpaf/Lkg0lPmtu2sStIOlp/8uSDSU2a17SZMksjfFPIFQaTnzGrb9Y2773b4nJK0h+UnfyaI9Bxj2+7dcJT0Xf3thyNSWvmI9JzyoRSlSINnU5hMqj1qh88oSXtYfvJngkjPaQ6SRo9Leh//Ktt4pJgkkL8l5AuCSC+oRHobP3fsXW/ftR59Kw9sflWUdrH85M8DkV5QGvT2NmmS0u9q/WZo0hOZdrH85M8DkV7QSjR+gt/7u+5S/aJr4JU8N2kXy0/+PBDpBbVH/zM/ClNVqTVqvkm7WH7y54FIr5g63dCgWFNXpm/NpKI9fze6imgvy0/+LBDpFf0JcKNIxfBoSbnMoTfJdD3eTpaf/Fkg0kse/jwXafALreJMPfgh0repB7ydLD/5c0CkOfzvSduuZqKLrtYkRIo9H5Fm8aokaajO1LWqufDBPn8TyBcEkWaxVKSBSe/v33+IFHU+Is3iddtORXOmPRUxLkk7Wn7yX4FI87AvSX1RQqSI8xFpHotEGpzsbn9ZQqSI8xFpHovbdt/au+LPYNKelp/8FyDSTBqTZo6tmlS//pMUSbnoaNFz1HV2tf5Dz0ekuSwSqdBFKgpTSbJe/u4CvheXl79gX+s/8HxEmst/S9p2SklqXv2NS5Lt8v+NsJvPvtZ/4PmINJv/LEqS8kPsuCTJiGTfuNvZ+g87H5Fm81/xv+qGinl899QD/mqT7PM7lGZd89bOpJ2t/7DzEWk2/zW3VMwcfeBRub8PSpLl8o/EQaQA8hFpPv9NPejczPDyuqYk9cPsRRoNsDJpb+s/6HxEms9/k89nNjM42/3XmfRtl9/NZqiNpUl7W/9B5yPSfB4iLTNJ529okrVIs4a9Zm/rP+h8RFpAb5KVSo1InUmiIlmYtLv1H3I+Ii2gFGmFSdXerphktfxmZxDJdz4iLaEzyap596eaZJVfTCljVZL2t/4DzkekJVQirTKpaE2yyp82BpE85yPSEmqRur7ultLs7a1JM/M1Q6aEsSlJ+1v/Aecj0iIakyyL0p9q0tx83ZBJXxDJbz4iLaIVyd6k6v9150KzRfozv5kebR47XP/h5iPSIv7rX9qY1O3slUmz8v+GIj0bcdm32eH6DzcfkZahm7T0NPifZtJ8kf6UN8/GXPRldrn+g81HpGWMStJik+oX/dNfXk/RTfS86CwuSXtc/8HmI9IyFJHMz5d9Ti/SXJOUW/demYJIHvMRaSF6SSoWmvS31KT21r2/GRVnaUna5foPNR+RFjIQydqk73km1R20tiI1Aw1PttBnPo9drv9Q8xFpIUORLEyq/v89x6T21kC9bwZTh/z6zOexy/Ufaj4iLWRwtqFYapJakobPRtdQ7lX/Vk85mDvkH8x8Frtc/6HmI9JSRiXJ0qRKhsFjMztUico/I4+mTZr/Tfa5/gPNR6SljEVablL5v+/OJO2Bf+1HNdW43Yu+V6IpkxaVpH2u/0DzEWkpBpEqk2arpJUkk0lKyalHrUzSG3USJu1z/Qeaj0iL6U3qu7mzMal1oXao/Ft9oHfhpRYirUk31cBDJD/5iLQYTSRbk8r/qSJUd6GX3nzrQ5UTdQNxRmp1k8w1aafrP8x8RFqMqW1XLFKp2dn/9RL81d05/A3MUH86GpWf1SbtdP2HmY9IyzG17QoLk/51ElQn5dreHP608dqX5kOiCZPmfYu9rv8g8xFpORMiLTbpX/vMiqaI1CYpHswrLkOTTFMZZ7TX9R9kPiItZ0qkJefBy739X/voJOV+v2/Vg5m15VXjTrssQmGv6z/IfERajvFsQ80yk+pTC/1OXp+/026bmDOr5427v47hdHtd/0HmI5IF0yXJxiTljIJu0uzTBr1J/Q+5esyfaWa7Xf8h5iOSBU9EWmqSenbhu7qMtegMmn8i+1t5GJNuUl+Kxibtdv2HmI9IFjwRackdSlW+/hvru2rSIpGGjIvayKTdrv8Q8xHJBrUzIe2DRff61flas6xQTVpwkcLgJ6W2zunuDE3a7/oPMB+RbNB65VI/eFtiUidSd/lcUagmLbkA1XDZw/i4aPB+v+s/wHxEsmGqbff2tuSu2Sa/VeBbvfSuWObR6Adb5XSgclm5bud+13+A+YhkxUTb7m2RSW1+bVJXUrTjJGu64yTtBg3tVPiO1394+YhkxVOR5prU5bciNW/bWyrWeNSJ+W4W6a/Y9foPLx+RrDCLVEk0//5zVSTtGGdun3fP6UXSZ9aptOP1H14+IllhPttQvZ7fk0OfP7z0dLlIY1/UW3BHo//Vlyj5Jap8RLLDWJLq17N7clDyxyItM+ldRZlpMbyLvWXhScFN2PP2H4FIdphEeutEmmeSmj+4XG6hSe9DunmO+oPo8G/Snrf/CESyY0Kk9v9t4+6pSVr+4HajRSIpfRGpJrVXSkzMy7tJe97+I+xF+v08ZSWn869lxJ5X5DOR5takZ/lLStJgXNWktjsIc75nlfa8/UfYinQ/ZD1Hu4hdr0j12X2mVzNMepr/WqR3pRCZJ/7u+1Ux5/s1adfbf4itSOcs/7lWr26XPDtbRex6RY5L0uC0w2uTXon03CTT6YXhxPr/DPlezznsevsPsRUpz67d62uWW0XsekU+F2mWSc/zX5j0zKOhQdMieT1S2vX2H2IrUpZNvZkfse8VOWrbjS9yKP//xKQX+U9FUs8vTH5c/v2enlOd768o7Xv7D6Ai2TIsScYLwYtnJr0WadKkWQ2/qg+I8WVCo3xfJu17+w9YcYx0uVWvEj1GGl3cML7H75VJr/InZZmqQ6NxGpEm5tTleypK+97+A6xPfx+Vs3aHu1XEvlfksAuU8V3nr0yaIdK78kYb/vLceOvR93TbTsn3otK+t/+AFb8jnavfkfLTZ4q/IxXDm5JGHQoVL016ma/s/708k5cqjCd+jGa6BNyUbzLpz4rX38yU74NARFofsfMVqZckg0evTJoj0nv/6r097pn3U20n0mRJ0vNHGth5lOpjZRDJmsGz+0wiaSaNVHqdr1ehJ6e7pyeeLdLwpIOtR/NV2vn210Eka+aIpJs0cGmWSMoPQYs0Kpp7zJ+07Ub5qgXLWmnmebxg59tfR0SkJH9HMj1O1sDbgP89ZTj2Y/+v/76r7y14BxWR7a+xkUjKKb3s3xTZzvlPef3YVyfGWmKScf9foQ8iTTG5U9Z4EsmSiCpS2TPkBKN9er5GtQBKQUIkIUS2vwYireC/16MMKXfqifw39bMWdcNbonacp931ZF7+6hjH8gBpGbvf/iqItAILkVST/o0+MJS11R51Ao0eWjGx/EvPYluz++2v4uDGvkl2vyJtRFJM+qcNM2okIlKt0MijyeV35NH+t7+Kgxv7Jtn/ilxn0r/BAZR59PUeTT0CfXL53XgUwfZXcHBj3yT7X5FWIo1PPzzzSOty2BqTR0+W381Vd/vf/goObqOYZP8r0k4kg0mrv8lLDA9z3v/6DyjfwY19k0SwIleXpPVfwZ4I1n84+VSkVViWJLH8VZAviIMb+yaJYEUiEvk1Dm7smySCFYlI5Nc4uLFvkhhW5BqTYlh+8hu4smEdiER+BSKtA5HIr0CklawwKYrlJ78GkVaCSOSXINJKEIn8EkRai71JcSw/+RWItBZEIr9ApPUgEvkFIglgbVIky09+CSKtBpHIRyQBEIl8RJLA1qRYlp/8ApEkQCTyEUkARCIfkQRAJPIRSQJLk6JZfvIRSQREIh+RBEAk8hFJAEQiH5EksDMpnuUnH5FEQKTk8xFJAkRKPh+RRLAyKaLlJx+RRECk1PMRSQRESj0fkURApNTzEUkGG5NiWv7k8xFJBkRKPB+RZECkxPMRSQZESjwfkYSwMCmq5U89H5GEQKS08xFJCERKOx+RpFhuUlzLn3g+IkmBSEnnI5IUiJR0PiKJsdikyJY/7XxEEgORUs5HJDEQKeV8RBIDkVLORyQ5lpoU2/InnY9IciBSwvmIJAciJZyPSIIsNCm65U85H5EEQaR08xFJEERKNx+RJFlmUnzLn3A+IkmCSMnmI5IkiJRsPiJJgkjJ5iOSKItMinD5081HJFEQKdV8RBIFkVLNRyRZlpgU4/Inm49IsiBSovmIJAsiJZqPSLIgUqL5iCTMApOiXP5U8xFJGERKMx+RhEGkNPMRSRhESjMfkaSZb1Kcy59oPiJJg0hJ5iOSNIiUZD4iSYNISeYjkjizTYp0+dPMRyRxECnFfEQSB5FSzEckcRApxXxEkmeuSbEuf5L5iCQPIiWYj0jyIFKC+YgkDyIlmI9IGzDTpGiXP8V8RNoAREovH5E2AJHSy0ekDUCk9PIRaQvmmRTv8ieYj0hbgEjJ5SPSFiBScvmItAWIlFw+Im3CLJMiXv708hFpExAptXxE2gRESi0fkTYBkVLL9ypS5pct8//znD+H5PMld2afIvnMJp980XxEIp/80Ga2o2zyyUck8skPLR+RyCc/tJntKJt88hGJfPJDy0ck8skPbWY7yiaffEQin/zQ8hGJfPJDm9mOssknPxqRAKIBkQAEQCQAARAJQABEAhAAkQAEQCQAARAJQABEAhAAkQAEQCQAARAJQABEAhAAkQAEQCQAARAJQADnIp3zLD/fnw1wnP918Jv/4NfhVhjlXz+y7OPmLf/uePs/Nri+toXyXYt0rJ4DcHgywHH+uRqQu9qSpsW95+62wij/4nf5b3md787ka6atban9z7FIv1l+La559js5wHH+Nfu4l/9IfXjKLzllzrbCOD9/DLifsrOn/I8q+exq/RdluLq2xfY/xyKds8vj70/2OTnAcf6pXgGudmXT4v5k7kQa5f9UO/I9yz3lZ27X/+OfzKOWJbb/ORbplJU1/JqdJgc4zm9wtSEN+bfBpnWb/5FdXWUb85tWrSuRi8e/G9raFtv/HIs0+gfI8b9IE3H37Ogt/5jd3Ik0yj9kxWdeNW/95H82TTtHLZLiOtj4YvsfIpV8VQXeS/5n9uOuYWNa/6fqYN9XfvFVnm3IvxzlD8IRSSy/4pY7almO86tGhVeRypMNH64qgukfkhJXBWkQjkhi+SX33FHDztS0Kk88exWpPEa6ufr9YZT/VTbtHiI7LElRiJQPv/dogOP8kqOzX7FG+R9Vm9KdSKPld/wP2Sj/kJWHZ3d3PyQOllVs//Ny1u42PGt3c3vWTou7HY7ufg0c5vfPqveT7/r0/yjf9envYZbY/udYpM/qX+BL//vfaIDj/MdrZ+06Q75rkSbW/83VShjl1xXB2e9YJdq6Ftv/Ur+ywdkuNJFf4fHKhsfR0b08RvnxlH/Oyuvczq7+IS2J4sqGR5u4pNp56wVSBvjI/3BbEcbLr79yn//pd/0317q5/NesXduy+59rkeqLfevobDDAR77jptV4+fVXHvIvR5/rv7n62ll+MRRJav9zLRJAlCASgACIBCAAIgEIgEgAAiASgACIBCAAIgEIgEgAAiASgACIBCAAIgEIgEgAAiASgACIBCAAIgEIgEgAAiASgACIBCAAIgEIgEgAAiASgACIBCAAIgEIgEgAAiASgACIBCAAIgEIgEgAAiASgACIBCAAIgEIgEgAAiCSC7pH8pUvBs/nGz2ur3mE4PF3/OloUuVhzvMe+2ccSxno8vHiUcF6c4GNSFn1gGBE2gmsNxdoIk19NhhwNjwh2LSb649Enf1NJgYikiWsNxfYiDRr3OejP/smEwMRyRLWmwsMTbtznp3bt+cs/xyP3Dx1vagePJ4dL91H50wZWx29mU+W3Q/Z6fHi65DlX9WH6hy6tMfHh69+Ft03AgtYby4Yi3Qsj2s+6ren8vXXcOS6aVe++aqPgr6ajwZtvl6kUzfW49W5KE71SYtCn0OXduw+7r/RCZEsYb25QD0pUO22lyy/Fte8fnu8P/b0w3jka1GPnJevfsoxynfDY6depHY+1asy4/G/+zG76HNox/ppvsJPPYuf/huBBaw3F4xEOpW792NXr94Ozs61p7+vRdGWsEv30fgcRC/Sbzd+deb8lJU63ctGnjqHdqz2Kxzbt7/tNwILWG8uGDXtmgH9y+Hx/iG/dG/OjybXtdHq2Fgynnc/n27mnbvqHEZjjd6CBaw3FywX6TfLbt2bz0eLK8tvlRu50gjU5v1MJHUOo7EQSQLWmwuWi/Roap2U4ZfzoT7C+b1m5VGNYd5GRXq6ORjGQqT1sN5cMBJJO0bSxmhfXvuTDfqkn1lunPdIkVN3YDSYg/4VTurbX0SyhPXmgpFI2lk7bQzFg1Pz5lDWoO6s3ePtp2neI5Gq83DFVzmbwRy003Q/o28EFrDeXDDxO1L2TKR7VZLqXb699K766FGr7oZ5j0RqMsojo8Ec1K/Q/Y506n7ZAgtYby4Yi1ReR3D8fSZScW5bXfV1Cf1J8s/68Gkw+lik8tKF7KM6ZzGYQ/33K9eubPjkyoYVsN58YrguFfYJInmhOvV2P5UX8kAUIJIXPuuDlvz1mLAPEMkPX4+DlgP1KB4QCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABEAkAAEQCUAARAIQAJEABPg/+CV8icJm0iAAAAAASUVORK5CYII=", "text/plain": ["plot without title"]}, "metadata": {}, "output_type": "display_data"}], "source": ["library(readxl)\n", "library(rmda)\n", "#Data <- read_excel(\"D:/dataset/train30.xlsx\")\n", "Data <- read_excel(\"K:/2020-2023HCC/579hcc/clinical data/data/train30预后最终版.xlsx\")\n", "\n", "## DCA运算\n", "\n", "Tumor_number<- decision_curve(status~Tumor_number,data = Data, family = binomial(link ='logit'),\n", "                          thresholds= seq(0,1, by = 0.01),\n", "                          confidence.intervals = 0.95,study.design = 'case-control',\n", "                          population.prevalence = 0.3)\n", "\n", "ap_score<- decision_curve(status~ap_score,data = Data, family = binomial(link ='logit'),\n", "                          thresholds= seq(0,1, by = 0.01),\n", "                          confidence.intervals = 0.95,study.design = 'case-control',\n", "                          population.prevalence = 0.3) \n", "\n", "\n", "Handcrafted_pathomics_score<- decision_curve(status~Handcrafted_pathomics_score,data = Data, family = binomial(link ='logit'),\n", "                                             thresholds= seq(0,1, by = 0.01),\n", "                                             confidence.intervals = 0.95,study.design = 'case-control',\n", "                                             population.prevalence = 0.3) \n", "\n", "Deep_pathomics_score<- decision_curve(status~Deep_pathomics_score,data = Data, family = binomial(link ='logit'),\n", "                                      thresholds= seq(0,1, by = 0.01),\n", "                                      confidence.intervals = 0.95,study.design = 'case-control',\n", "                                      population.prevalence = 0.3) \n", "\n", "#decision_curve()函数中，family =binomial(link = ‘logit’)是使用logistic回归来拟合模型。\n", "#threshold设置横坐标阈概率的范围，一般是0 ~ 1；但如果有某种具体情况，大家一致认为Pt达到某个值以上，\n", "#比如40%，则必须采取干预措施，那么0.4以后的研究就没什么意义了，可以设为0 ~ 0.4。by是指每隔多少距离计算一个数据点。\n", "#Study.design可设置研究类型，是cohort还是case-control，当研究类型为case-control时，还应加上患病率population.prevalance参数。\n", "\n", "nomogram<- decision_curve(status~nomogram,data = Data,\n", "                          family = binomial(link ='logit'), thresholds = seq(0,1, by = 0.01),\n", "                          confidence.intervals= 0.95,study.design = 'case-control',\n", "                          population.prevalence= 0.3)\n", "\n", "#基本和simple相同，就是那几个联合应用的变量之间用个+号连接起来。\n", "\n", "List<- list(nomogram,Tumor_number,ap_score,Handcrafted_pathomics_score,Deep_pathomics_score)\n", "#把刚才计算的simple和complex两个对象合成一个list，命名为List。\n", "\n", "##DCA曲线绘制\n", "plot_decision_curve(List,curve.names= c('Radiopathomics nomogram','Tumor number','DRSAP','Handcrafted_pathomics_score','Deep_pathomics_score'),\n", "                    cost.benefit.axis =FALSE,col = c('red','gold','gray','blue','green','purple','black'),\n", "                    confidence.intervals =FALSE,standardize = FALSE, lwd = 3)\n", "#plot_decision_curve()函数的对象就是刚才的List，如果只画一根曲线，就不需要合成的那步，直接把List替换成simple或complex就好了。\n", "#curve.names是出图时，图例上每条曲线的名字，书写顺序要跟上面合成list时一致。cost.benefit.axis是另外附加的一条横坐标轴，损失收益比，默认值是TRUE，所在不需要时要记得设为FALSE。\n", "#col就是颜色。confidence.intervals设置是否画出曲线的置信区间，standardize设置是否对净受益率（NB）使用患病率进行校正。\n", "summary(nomogram,measure= 'sNB')\n", "#summary(List,measure= 'NB')\n", "\n", "pdf(\"K:/2023写文章/2024SCI投稿/文章图/最终版/投稿版/DCA1.pdf\",height = 8, width = 9)\n", "## 最简单的方法绘制柱状图：直接dotplot就可以搞定：\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Note: When multiple decision curves are plotted, decision curves for 'All' are calculated using the prevalence from the first DecisionCurve object in the list provided.\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Standardized Net Benefit (95% Confidence Intervals):"]}, {"ename": "ERROR", "evalue": "Error in eval(expr, envir, enclos): Pandoc does not support newlines in simple or Rmarkdown table format!\n", "output_type": "error", "traceback": ["Error in eval(expr, envir, enclos): Pandoc does not support newlines in simple or Rmarkdown table format!\n", "Traceback:\n", "\n", "1. summary(nomogram, measure = \"sNB\")\n", "2. summary.decision_curve(nomogram, measure = \"sNB\")\n", "3. pandoc.table(out, split.table = Inf, keep.line.breaks = TRUE)\n", "4. cat(pandoc.table.return(...))\n", "5. pandoc.table.return(...)\n", "6. paste(res, sep.top, table.expand(t.colnames, t.width, justify, \n", " .     sep.col), sep.hdr, sep = \"\\n\")\n", "7. table.expand(t.colnames, t.width, justify, sep.col)\n", "8. tableExpand_cpp(cells, cols.width, justify, sep.cols, style)"]}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {}, "output_type": "display_data"}], "source": ["library(readxl)\n", "library(rmda)\n", "#Data <- read_excel(\"D:/dataset/train30.xlsx\")\n", "Data <- read_excel(\"K:/2020-2023HCC/579hcc/clinical data/data/train30预后最终版.xlsx\")\n", "\n", "## DCA运算\n", "\n", "Tumor_number<- decision_curve(status~Tumor_number,data = Data, family = binomial(link ='logit'),\n", "                          thresholds= seq(0,1, by = 0.01),\n", "                          confidence.intervals = 0.95,study.design = 'case-control',\n", "                          population.prevalence = 0.3)\n", "\n", "ap_score<- decision_curve(status~ap_score,data = Data, family = binomial(link ='logit'),\n", "                          thresholds= seq(0,1, by = 0.01),\n", "                          confidence.intervals = 0.95,study.design = 'case-control',\n", "                          population.prevalence = 0.3) \n", "\n", "\n", "Handcrafted_pathomics_score<- decision_curve(status~Handcrafted_pathomics_score,data = Data, family = binomial(link ='logit'),\n", "                                             thresholds= seq(0,1, by = 0.01),\n", "                                             confidence.intervals = 0.95,study.design = 'case-control',\n", "                                             population.prevalence = 0.3) \n", "\n", "Deep_pathomics_score<- decision_curve(status~Deep_pathomics_score,data = Data, family = binomial(link ='logit'),\n", "                                      thresholds= seq(0,1, by = 0.01),\n", "                                      confidence.intervals = 0.95,study.design = 'case-control',\n", "                                      population.prevalence = 0.3) \n", "\n", "#decision_curve()函数中，family =binomial(link = ‘logit’)是使用logistic回归来拟合模型。\n", "#threshold设置横坐标阈概率的范围，一般是0 ~ 1；但如果有某种具体情况，大家一致认为Pt达到某个值以上，\n", "#比如40%，则必须采取干预措施，那么0.4以后的研究就没什么意义了，可以设为0 ~ 0.4。by是指每隔多少距离计算一个数据点。\n", "#Study.design可设置研究类型，是cohort还是case-control，当研究类型为case-control时，还应加上患病率population.prevalance参数。\n", "\n", "nomogram<- decision_curve(status~nomogram,data = Data,\n", "                          family = binomial(link ='logit'), thresholds = seq(0,1, by = 0.01),\n", "                          confidence.intervals= 0.95,study.design = 'case-control',\n", "                          population.prevalence= 0.3)\n", "\n", "#基本和simple相同，就是那几个联合应用的变量之间用个+号连接起来。\n", "\n", "List<- list(nomogram,Tumor_number,ap_score,Handcrafted_pathomics_score,Deep_pathomics_score)\n", "#把刚才计算的simple和complex两个对象合成一个list，命名为List。\n", "\n", "##DCA曲线绘制\n", "plot_decision_curve(List,curve.names= c('Radiopathomics nomogram','Tumor number','DRSAP','Handcrafted_pathomics_score','Deep_pathomics_score'),\n", "                    cost.benefit.axis =FALSE,col = c('red','gold','gray','blue','green','purple','black'),\n", "                    confidence.intervals =FALSE,standardize = FALSE, lwd = 3)\n", "#plot_decision_curve()函数的对象就是刚才的List，如果只画一根曲线，就不需要合成的那步，直接把List替换成simple或complex就好了。\n", "#curve.names是出图时，图例上每条曲线的名字，书写顺序要跟上面合成list时一致。cost.benefit.axis是另外附加的一条横坐标轴，损失收益比，默认值是TRUE，所在不需要时要记得设为FALSE。\n", "#col就是颜色。confidence.intervals设置是否画出曲线的置信区间，standardize设置是否对净受益率（NB）使用患病率进行校正。\n", "summary(nomogram,measure= 'sNB')\n", "#summary(List,measure= 'NB')\n", "\n", "pdf(\"K:/2023写文章/2024SCI投稿/文章图/最终版/投稿版/DCA1.pdf\",height = 8, width = 9)\n", "## 最简单的方法绘制柱状图：直接dotplot就可以搞定：\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Calculating net benefit curves for case-control data. All calculations are done conditional on the outcome prevalence provided.\n", "\n", "Note: When multiple decision curves are plotted, decision curves for 'All' are calculated using the prevalence from the first DecisionCurve object in the list provided.\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Standardized Net Benefit (95% Confidence Intervals):"]}, {"ename": "ERROR", "evalue": "Error in eval(expr, envir, enclos): Pandoc does not support newlines in simple or Rmarkdown table format!\n", "output_type": "error", "traceback": ["Error in eval(expr, envir, enclos): Pandoc does not support newlines in simple or Rmarkdown table format!\nTraceback:\n", "1. summary(nomogram, measure = \"sNB\")", "2. summary.decision_curve(nomogram, measure = \"sNB\")", "3. pandoc.table(out, split.table = Inf, keep.line.breaks = TRUE)", "4. cat(pandoc.table.return(...))", "5. pandoc.table.return(...)", "6. paste(res, sep.top, table.expand(t.colnames, t.width, justify, \n .     sep.col), sep.hdr, sep = \"\\n\")", "7. table.expand(t.colnames, t.width, justify, sep.col)", "8. tableExpand_cpp(cells, cols.width, justify, sep.cols, style)"]}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["library(readxl)\n", "library(rmda)\n", "#Data <- read_excel(\"D:/dataset/train30.xlsx\")\n", "Data <- read_excel(\"K:/2020-2023HCC/579hcc/clinical data/data/train30预后最终版.xlsx\")\n", "\n", "## DCA运算\n", "\n", "Tumor_number<- decision_curve(status~Tumor_number,data = Data, family = binomial(link ='logit'),\n", "                          thresholds= seq(0,1, by = 0.01),\n", "                          confidence.intervals = 0.95,study.design = 'case-control',\n", "                          population.prevalence = 0.3)\n", "\n", "ap_score<- decision_curve(status~ap_score,data = Data, family = binomial(link ='logit'),\n", "                          thresholds= seq(0,1, by = 0.01),\n", "                          confidence.intervals = 0.95,study.design = 'case-control',\n", "                          population.prevalence = 0.3) \n", "\n", "\n", "Handcrafted_pathomics_score<- decision_curve(status~Handcrafted_pathomics_score,data = Data, family = binomial(link ='logit'),\n", "                                             thresholds= seq(0,1, by = 0.01),\n", "                                             confidence.intervals = 0.95,study.design = 'case-control',\n", "                                             population.prevalence = 0.3) \n", "\n", "Deep_pathomics_score<- decision_curve(status~Deep_pathomics_score,data = Data, family = binomial(link ='logit'),\n", "                                      thresholds= seq(0,1, by = 0.01),\n", "                                      confidence.intervals = 0.95,study.design = 'case-control',\n", "                                      population.prevalence = 0.3) \n", "\n", "#decision_curve()函数中，family =binomial(link = ‘logit’)是使用logistic回归来拟合模型。\n", "#threshold设置横坐标阈概率的范围，一般是0 ~ 1；但如果有某种具体情况，大家一致认为Pt达到某个值以上，\n", "#比如40%，则必须采取干预措施，那么0.4以后的研究就没什么意义了，可以设为0 ~ 0.4。by是指每隔多少距离计算一个数据点。\n", "#Study.design可设置研究类型，是cohort还是case-control，当研究类型为case-control时，还应加上患病率population.prevalance参数。\n", "\n", "nomogram<- decision_curve(status~nomogram,data = Data,\n", "                          family = binomial(link ='logit'), thresholds = seq(0,1, by = 0.01),\n", "                          confidence.intervals= 0.95,study.design = 'case-control',\n", "                          population.prevalence= 0.3)\n", "\n", "#基本和simple相同，就是那几个联合应用的变量之间用个+号连接起来。\n", "\n", "List<- list(nomogram,Tumor_number,ap_score,Handcrafted_pathomics_score,Deep_pathomics_score)\n", "#把刚才计算的simple和complex两个对象合成一个list，命名为List。\n", "\n", "##DCA曲线绘制\n", "plot_decision_curve(List,curve.names= c('Radiopathomics nomogram','Tumor number','DRSAP','Handcrafted_pathomics_score','Deep_pathomics_score'),\n", "                    cost.benefit.axis =FALSE,col = c('red','gold','gray','blue','green','purple','black'),\n", "                    confidence.intervals =FALSE,standardize = FALSE, lwd = 3)\n", "#plot_decision_curve()函数的对象就是刚才的List，如果只画一根曲线，就不需要合成的那步，直接把List替换成simple或complex就好了。\n", "#curve.names是出图时，图例上每条曲线的名字，书写顺序要跟上面合成list时一致。cost.benefit.axis是另外附加的一条横坐标轴，损失收益比，默认值是TRUE，所在不需要时要记得设为FALSE。\n", "#col就是颜色。confidence.intervals设置是否画出曲线的置信区间，standardize设置是否对净受益率（NB）使用患病率进行校正。\n", "summary(nomogram,measure= 'sNB')\n", "#summary(List,measure= 'NB')\n", "\n", "pdf(\"K:/2023写文章/2024SCI投稿/文章图/最终版/投稿版/DCA1.pdf\",height = 8, width = 9)\n", "## 最简单的方法绘制柱状图：直接dotplot就可以搞定：\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["\n", "##绘制临床影响曲线（Clinical Impact Curve）\n", "plot_clinical_impact(simple,population.size = 1000,cost.benefit.axis = T,\n", "                     n.cost.benefits= 8,col = c('red','blue'),\n", "                     confidence.intervals= T,ylim=c(0,1000),\n", "                     legend.position= \"topright\")\n", "plot_clinical_impact(complex,population.size = 1000,cost.benefit.axis = T,\n", "                     n.cost.benefits= 8,col = c('red','blue'),\n", "                     confidence.intervals= T,ylim=c(0,1000),\n", "                     legend.position= \"topright\")\n", "#使用simple模型预测1000人的风险分层，显示“损失:受益”坐标轴，赋以8个刻度，显示置信区间，得到图\n", "#红色曲线（Numberhigh risk）表示，在各个阈概率下，被simple或complex模型划分为阳性（高风险）的人数；\n", "#蓝色曲线（Number high risk with outcome）为各个阈概率下真阳性的人数。意义一目了然。\n", "\n", "#DCA算法的设计原理\n", "#相当于在回归预测分析的基础上，引入了损失函数。先简单定义几个概念：\n", "#P：给真阳性患者施加干预的受益值（比如用某生化指标预测某患者有癌症，实际也有，予活检，达到了确诊的目的）；\n", "#L：给假阳性患者施加干预的损失值（比如预测有癌症，给做了活检，原来只是个增生，白白受了一刀）；\n", "#Pi：患者i有癌症的概率，当Pi > Pt时为阳性，给予干预。\n", "#所以较为合理的干预的时机是，当且仅当Pi × P >(1 – Pi) × L，即预期的受益高于预期的损失。推导一下可得，Pi > L / ( P + L )即为合理的干预时机，于是把L / ( P + L )定义为Pi的阈值，即Pt。\n", "#但对二元的预测指标来说，如果结果是阳性，则强制Pi=1，阴性则Pi = 0。这样，二元和其他类型的指标就有了可比性。\n", "#然后我们还可用这些参数来定义真阳性（A）、假阳性（B）、假阴性（C）、真阴性（D），即：\n", "#A：Pi ≥ Pt，实际患病；\n", "#B：Pi ≥ Pt，实际不患病；\n", "#C：Pi < Pt，实际患病；\n", "#D：Pi < Pt，实际不患病。\n", "#我们有一个随机抽样的样本，A、B、C、D分别为这四类个体在样本中的比例，则A+B+C+D = 1。那么，患病率（π）就是A + C了。\n", "#在这个样本中，如果所有Pi ≥ Pt 的人我们都给做了活检，那么就会有人确诊，有人白白被拉了一刀，那么净受益率NB = A × P – B × L。\n", "#但Vickers认为，知道P和L的确切值并没有什么实际意义，人们可能更关心L/P的比值，所以将上面的公式强行除以P，变成NB = A – B × L/P。根据Pt定义公式可推导出：NB = A – B × Pt / ( 1 – Pt )。以Pt为横坐标，U为纵坐标，画出来的曲线就是决策曲线。\n", "#若使用患病率进行校正，则U = A × π – B ×(1 –π) × Pt / ( 1 – Pt )。\n", "#那么两个极端情况的曲线也很好推导了。当所有样本都是阴性（Pi < Pt），所有人都没干预，那么A = B = 0，所以NB = 0。当所有样本都是阳性，所有人都接受干预，那么C = D = 0，A = π，B = 1 –π，NB = π– ( 1 –π )Pt / ( 1 – Pt )，所以它斜率为负值。\n", "\n", "#参考资料：\n", "#1.Decision curve analysis: anovel method for evaluating prediction models\n", "#2.Decision curve analysisrevisited: overall net benefit, relationships to ROC curve analysis, andapplication to case-control studies\n", "#3.Assessing the Clinical Impactof Risk Prediction Models With Decision Curves: Guidance for CorrectInterpretation and Appropriate Use"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}