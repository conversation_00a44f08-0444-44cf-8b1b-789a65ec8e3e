################### survminer包绘制 #################
library(survminer)
library(survival)

# 示例数据：survival包自带的数据集；
head(colon)
# id study      rx sex age obstruct perfor adhere nodes status differ
# 1  1     1 Lev+5FU   1  43        0      0      0     5      1      2
# 2  1     1 Lev+5FU   1  43        0      0      0     5      1      2
# 3  2     1 Lev+5FU   1  63        0      0      0     1      0      2
# 4  2     1 Lev+5FU   1  63        0      0      0     1      0      2
# 5  3     1     Obs   0  71        0      0      1     7      1      2
# 6  3     1     Obs   0  71        0      0      1     7      1      2
# extent surg node4 time etype
# 1      3    0     1 1521     2
# 2      3    0     1  968     1
# 3      3    0     0 3087     2
# 4      3    0     0 3087     1
# 5      2    0     1  963     2
# 6      2    0     1  542     1

# 构建回归模型：病人生存和sex--性别、rx -- 治疗方式、adhere -- 肿瘤是否粘附附近器官
model <- coxph(Surv(time, status) ~ sex + rx + adhere,
                data = colon )

# 绘图：使用ggforest函数：
pdf("forest_plot01.pdf", height = 6, width = 7)
ggforest(model)
dev.off()


################### forestplot包绘制 #################
library(tidyverse)
library(forestplot)
library(meta)

# 构建数据集: 这个包的输入可以是两个简单的dataframe，内容类似下方示例：
cochrane_from_rmeta <- 
  structure(list(
    mean  = c(NA, NA, 0.578, 0.165, 0.246, 0.700, 0.348, 0.139, 1.017, NA, 0.531), 
    lower = c(NA, NA, 0.372, 0.018, 0.072, 0.333, 0.083, 0.016, 0.365, NA, 0.386),
    upper = c(NA, NA, 0.898, 1.517, 0.833, 1.474, 1.455, 1.209, 2.831, NA, 0.731)),
    .Names = c("mean", "lower", "upper"), 
    row.names = c(NA, -11L), 
    class = "data.frame")

tabletext<-cbind(
  c("", "Study", "Auckland", "Block", 
    "Doran", "Gamsu", "Morrison", "Papageorgiou", 
    "Tauesch", NA, "Summary"),
  c("Deaths", "(steroid)", "36", "1", 
    "4", "14", "3", "1", 
    "8", NA, NA),
  c("Deaths", "(placebo)", "60", "5", 
    "11", "20", "7", "7", 
    "10", NA, NA),
  c("", "OR", "0.58", "0.16", 
    "0.25", "0.70", "0.35", "0.14", 
    "1.02", NA, "0.53"))

# 绘图：
pdf("forest_plot02.pdf", height = 7, width = 7)
forestplot(tabletext, 
           cochrane_from_rmeta, new_page = TRUE,
           is.summary=c(TRUE,TRUE,rep(FALSE,8),TRUE),
           clip=c(0.1,2.5), 
           xlog=TRUE, 
           # 修改颜色：
           col=fpColors(box="#6d2e80", line="#6d2e80", summary="#6d2e80"))
dev.off()


################### ggforestplot包绘制 #################
# 这个包安装起来比较费劲，于是师兄把它下载下来了，大家可以通过以下命令
# 在本地实现安装：
# devtools::install_local("./ggforestplot-master.zip")
library(ggforestplot)
library(tidyverse)

# 示例数据：
df_linear <-
  ggforestplot::df_linear_associations %>%
  dplyr::arrange(name) %>%
  dplyr::filter(dplyr::row_number() <= 30)

head(df_linear)
# # A tibble: 6 × 5
# name              trait               beta      se   pvalue
# <chr>             <chr>              <dbl>   <dbl>    <dbl>
#   1 3-Hydroxybutyrate BMI             -0.0400  0.00911 1.10e- 5
# 2 3-Hydroxybutyrate HOMA-IR         -0.136   0.00770 3.18e-70
# 3 3-Hydroxybutyrate Fasting glucose -0.0767  0.00745 7.49e-25
# 4 Acetate           BMI             -0.0746  0.0167  8.50e- 6
# 5 Acetate           HOMA-IR         -0.0754  0.0142  1.19e- 7
# 6 Acetate           Fasting glucose -0.00836 0.0133  5.28e- 1

# 可视化绘制
pdf("forest_plot03.pdf", height = 7, width = 10)
ggforestplot::forestplot(
  df = df_linear,
  estimate = beta,
  logodds = FALSE,
  colour = trait,
  title = "Associations to metabolic traits",
  xlab = "1-SD increment in cardiometabolic trait
  per 1-SD increment in biomarker concentration"
) 
dev.off()


################### forestploter包绘制 #################
library(grid)
library(forestploter)

# 读取数据：
dt <- read.csv(system.file("extdata", "example_data.csv", package = "forestploter"))

dt$Subgroup <- ifelse(is.na(dt$Placebo), 
                      dt$Subgroup,
                      paste0("   ", dt$Subgroup))

# 替换缺失值：
dt$Treatment <- ifelse(is.na(dt$Treatment), "", dt$Treatment)
dt$Placebo <- ifelse(is.na(dt$Placebo), "", dt$Placebo)
dt$se <- (log(dt$hi) - log(dt$est))/1.96

# 添加空白列，显示CI
dt$` ` <- paste(rep(" ", 20), collapse = " ")

# 创建要显示的置信区间列
dt$`HR (95% CI)` <- ifelse(is.na(dt$se), "",
                           sprintf("%.2f (%.2f to %.2f)",
                                   dt$est, dt$low, dt$hi))
head(dt)
#>          Subgroup Treatment Placebo      est        low       hi        se
#> 1    All Patients       781     780 1.869694 0.13245636 3.606932 0.3352463
#> 2             Sex                         NA         NA       NA        NA
#> 3            Male       535     548 1.449472 0.06834426 2.830600 0.3414741
#> 4          Female       246     232 2.275120 0.50768005 4.042560 0.2932884
#> 5             Age                         NA         NA       NA        NA
#> 6          <65 yr       297     333 1.509242 0.67029394 2.348190 0.2255292
#>                                                   HR (95% CI)
#> 1                                         1.87 (0.13 to 3.61)
#> 2                                                            
#> 3                                         1.45 (0.07 to 2.83)
#> 4                                         2.28 (0.51 to 4.04)
#> 5                                                            
#> 6                                         1.51 (0.67 to 2.35)

# 绘制简单的森林图
pdf("forest_plot04.pdf", height = 7, width = 10)
forest(dt[,c(1:3, 20:21)],
       est = dt$est,
       lower = dt$low, 
       upper = dt$hi,
       sizes = dt$se,
       ci_column = 4,
       ref_line = 1,
       arrow_lab = c("Placebo Better", "Treatment Better"),
       xlim = c(0, 4),
       ticks_at = c(0.5, 1, 2, 3),
       footnote = "This is the demo data. Please feel free to change\nanything you want.")
dev.off()


# 更改主题背景
# 增加汇总列并修改图形参数
dt_tmp <- rbind(dt[-1, ], dt[1, ])  # 实际上就是把第一行放到最后一行
dt_tmp[nrow(dt_tmp), 1] <- "Overall"

# 定义主题：
tm <- forest_theme(base_size = 10,
                   # 置信区间点形状，线类型/颜色/宽度
                   ci_pch = 16,  # 点形状
                   ci_col = "red",
                   ci_lty = 1, # 线条类型
                   ci_lwd = 1.5, # 线条宽度
                   ci_Theight = 0.4, # 在CI的末尾设置T形尾部
                   # 参考线宽/类型/颜色
                   refline_lwd = 1,
                   refline_lty = "dashed",
                   refline_col = "grey20",
                   # 垂直的线宽/类型/颜色
                   vertline_lwd = 1,
                   vertline_lty = "dashed",
                   vertline_col = "grey20",
                   # 更改填充和边框的摘要颜色
                   summary_fill = "red",
                   summary_col = "red",
                   # 脚注字体大小/字形/颜色
                   footnote_cex = 0.6,
                   footnote_fontface = "italic",
                   footnote_col = "red")

# 绘图：
pdf("forest_plot05.pdf", height = 7, width = 10)
forest(dt_tmp[,c(1:3, 20:21)],
       est = dt_tmp$est,
       lower = dt_tmp$low, 
       upper = dt_tmp$hi,
       sizes = dt_tmp$se,
       is_summary = c(rep(FALSE, nrow(dt_tmp)-1), TRUE),
       ci_column = 4,
       ref_line = 1,
       arrow_lab = c("Placebo Better", "Treatment Better"),
       xlim = c(0, 4),
       ticks_at = c(0.5, 1, 2, 3),
       footnote = "This is the demo data. Please feel free to change\nanything you want.",
       theme = tm)
dev.off()


# 修改森林图：
p <- forest(dt_tmp[,c(1:3, 20:21)],
       est = dt_tmp$est,
       lower = dt_tmp$low, 
       upper = dt_tmp$hi,
       sizes = dt_tmp$se,
       is_summary = c(rep(FALSE, nrow(dt_tmp)-1), TRUE),
       ci_column = 4,
       ref_line = 1,
       arrow_lab = c("Placebo Better", "Treatment Better"),
       xlim = c(0, 4),
       ticks_at = c(0.5, 1, 2, 3),
       footnote = "This is the demo data. Please feel free to change\nanything you want.",
       theme = tm)
# 改变第三行文字的颜色：
g <- edit_plot(p, row = 3, gp = gpar(col = "red", fontface = "italic"))

# 加粗分组变量的名称：
g <- edit_plot(g,
               row = c(2, 5, 10, 13, 17, 20),
               gp = gpar(fontface = "bold"))

# 改变第五行的背景色：
g <- edit_plot(g, row = 5, which = "background",
               gp = gpar(fill = "#4ca9be"))

# 在表头插入文字
g <- insert_text(g,
                 text = "Treatment group",
                 col = 2:3,
                 part = "header",
                 gp = gpar(fontface = "bold"))

# 在标题的底部添加下划线
g <- add_underline(g, part = "header")

# 在表格内部插入文字：
g <- insert_text(g,
                 text = "This is a long text. Age and gender summarised above.\nBMI is next",
                 row = 10,
                 just = "left",
                 gp = gpar(cex = 0.6, col = "red", fontface = "italic"))

pdf("forest_plot06.pdf", height = 7, width = 10)
plot(g)
dev.off()


# 多个置信区间列：
# 再添加一个空白列：
dt$`   ` <- paste(rep(" ", 20), collapse = " ")

# 设置主题：
tm <- forest_theme(base_size = 10,
                   # 设置两个组的颜色：
                   refline_col = "red",
                   footnote_col = "blue",
                   # 图例名称：
                   legend_name = "GP",
                   # 图例内容：
                   legend_value = c("Trt 1", "Trt 2"))

# 绘图：
pdf("forest_plot07.pdf", height = 7, width = 10)
forest(dt[,c(1:3, 20, 3, 22)],
       est = list(dt$est_gp1,
                 dt$est_gp2,
                 dt$est_gp3,
                 dt$est_gp4),
       lower = list(dt$low_gp1,
                   dt$low_gp2,
                   dt$low_gp3,
                   dt$low_gp4), 
       upper = list(dt$hi_gp1,
                   dt$hi_gp2,
                   dt$hi_gp3,
                   dt$hi_gp4),
       ci_column = c(3, 5),
       ref_line = 1,
       arrow_lab = c("Placebo Better", "Treatment Better"),
       nudge_y = 0.2,
       xlog = TRUE,
       theme = tm)
dev.off()




