#%%ggboxplot()函数绘制箱线图
library(readxl)
data <- read_excel('I:/肿瘤科李伟课题ICC/apdata/train_score_min_normalized.xlsx')
print(colnames(data))
data$label <- factor(data$label, levels = c(0, 1), labels = c("Ineffective group", "Effective group"))

#data <- read.csv("D:/Rcode/ch03/score.csv", sep=",", header=T)
#View(data)
head(data)
library(ggpubr) # 加载包
df <- data # 将数据集传递给df
plot <- ggboxplot(df, x ="label", y = "radiomics score",add = "jitter",#notch = TRUE,
          fill = "label", palette = c("#00AFBB", "#E7B800"))
plot
plot + stat_compare_means(method="kruskal.test")
plot + stat_compare_means(method="wilcox.test", method.args = list(paired=FALSE)) #Mann-Whitney U检验

#install.packages("ggpval")
library(ggpval) 
add_pval(plot,pairs =list(c(1,2))) #给图形加上p值
add_pval(plot,pairs =list(c(1,2)),annotation = "P<0.001") #给图形加上p值

#绘制空心箱线图
# plot <- ggboxplot(df, x ="VETC", y = "Handcrafted_pathomics_score",add = "jitter",#notch = TRUE,
#                 color= "VETC",palette = c("#00AFBB", "#E7B800"))
# plot
# plot + stat_compare_means(method="t.test")
# plot + stat_compare_means(method="wilcox.test") # nolint
# plot + stat_compare_means(method="kruskal.test")

# compare_means(score ~ group,data = df,method="t.test")
# my_comparisons <- list( c("1","2"))
# plot + stat_compare_means(comparisons = my_comparisons,method="t.test")
