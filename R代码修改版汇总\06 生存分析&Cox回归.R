#%%COX代码总结
# 安装并加载所需的包
# install.packages("readxl")
# install.packages("survival")
library(readxl)
library(survival)
library(MASS)
# 从Excel文件中读取数据
data <- read_excel('H:\\1.HCC-VETC\\734HCC\\all-HCC\\734hcctumor\\data\\PFS预后和疗效评估\\228HCC治疗后mRECIST评估最终版.xlsx') #有时不能带中文
print(colnames(data))

# 取第4列及其后的数据
#data_subset <- data[, 3:ncol(data)]
data_subset <- data[, 2:ncol(data)]
data_subset 
variables <- colnames(data_subset)
print(variables)

# 要进行单变量Cox回归的变量列表
variables <- c("resswin3D_CR_Predict_label2","resswin3D_CR_Predict_label0")

# 使用lapply批量进行单变量Cox回归
cox_models_univar <- lapply(variables, function(var) {
  coxph(formula = as.formula(paste("Surv(PFS2, status==1) ~", var)), data=data_subset )
})

# 打印每个单变量回归结果
for (i in 1:length(cox_models_univar)) {
  cat("Variable:", variables[i], "\n")
  print(summary(cox_models_univar[[i]]))
}

# 计算Concordance指数
#c_index <- survConcordance(Surv(PFS, status==1) ~ ap_score, data=data_subset)
#c_index
# 计算95%置信区间
#c_index_ci <- bootstrappingConcordance(Surv(PFS, status==1) ~ predicted_score, data=data_subset, B=1000)

# 创建一个用于存储P值小于0.05的变量的向量
significant_vars <- c()

# 循环遍历每个单变量回归结果
for (i in 1:length(cox_models_univar)) {
  # 获取回归模型的摘要
  model_summary <- summary(cox_models_univar[[i]])
  
  # 若变量的P值小于0.05，则打印该变量名并将其添加到significant_vars向量中
  if (model_summary$coefficients[1, "Pr(>|z|)"] < 0.1) {
    cat("Variable:", variables[i], "\n")
    print(model_summary)
    significant_vars <- c(significant_vars, variables[i])
  }
}

# 打印出P值小于0.05的所有变量
print(significant_vars)
formula_vars <- paste(significant_vars, collapse="+")
print(formula_vars)


#%%保存单变量回归结果到excel
library(openxlsx)  # Load the library for writing Excel files

# Get variable names
variables <- colnames(data_subset)

# Create an empty dataframe to store results
result_df <- data.frame(Variable = character(),
                        HR = numeric(),
                        CI_lower = numeric(),
                        CI_upper = numeric(),
                        P_value = numeric(),
                        Coefficient = numeric(),  # Add a column for coefficient values
                        stringsAsFactors = FALSE)

# Loop through each variable and perform single-variable Cox regression
for (i in 1:length(variables)) {
  var <- variables[i]
  
  # Fit Cox model
  cox_model <- coxph(formula = as.formula(paste("Surv(PFS, status==1) ~", var)), data = data_subset)
  
  # Extract HR, CI, and P-value
  cox_summary <- summary(cox_model)
  hr <- round(cox_summary$coefficients[1, "exp(coef)"], 3)
  ci_lower <- round(cox_summary$conf.int[3], 3)
  ci_upper <- round(cox_summary$conf.int[4], 3)
  p_value <- round(cox_summary$coefficients[1, "Pr(>|z|)"], 3)
  coefficient <- round(cox_summary$coefficients[1, "coef"], 3)
  
  # Append results to the dataframe
  result_df <- rbind(result_df, data.frame(Variable = var,
                                           HR = hr,
                                           CI_lower = ci_lower,
                                           CI_upper = ci_upper,
                                           P_value = p_value,
                                           Coefficient = coefficient))
}

# Save results to an Excel file
write.xlsx(result_df, file = 'H:\\1.HCC-VETC\\734HCC\\all-HCC\\734hcctumor\\data\\PFS预后和疗效评估\\疗效评估Cox结果-R语言.xlsx', sheetName = "Results", row.names = FALSE)


#%%执行多变量Cox回归分析:enter法
cox_model_multivar <- coxph(Surv(PFS, outcome==1) ~ HBCV+AST+GGT+AFP+diameter+resswin3D_CR_Predict_label, data=data_subset)

#打印多变量回归结果
summary(cox_model_multivar)


# 计算相关系数矩阵
significant_vars <- setdiff(significant_vars, c("VETC", "PFS")) #去掉VETC和PFS
print(significant_vars)
correlation_matrix <- cor(data[significant_vars])
correlation_matrix

# 绘制相关性矩阵的热力图
heatmap(correlation_matrix, 
        annot = TRUE, 
        cmap = colorRampPalette(c("blue", "white", "red"))(100), 
        cexRow = 0.8, 
        cexCol = 0.8, 
        main = "Correlation Matrix")

#删除共线性变量，剔除高度相关性变量
library(caret)
highlycor= findCorrelation(correlation_matrix, cutoff = 0.90, verbose = T, names =TRUE)
print(highlycor)# 获取需要删除的变量名


##%%R语言进行AIC或BIC逐步回归方法用于多变量cox回归
# 安装并加载所需的包
# install.packages("survival")
# install.packages("MASS")
library(survival)
library(MASS)
library(readxl)
library(openxlsx)
library(ggplot2)
library(pec)

# 从Excel文件中读取数据
data <- read_excel('K:/2020-2023HCC/579hcc/clinical data/data/test预后最终版.xlsx')#522HCC预后完整版
#data <- read.csv('K:/2020-2023HCC/579hcc/clinical data/data/test预后最终版.csv')#522HCC预后完整版

# 获取变量名
variable_names <- colnames(data)
print(variable_names)

# 取第4列及其后的数据
data_subset <- data[, 4:ncol(data)]

# 执行多变量Cox回归分析:enter法
cox_model_multivar <- coxph(Surv(PFS, status==1) ~ diameter+resswin3D_CR_Predict_label, data=data_subset)

# 打印多变量回归结果
summary(cox_model_multivar)

# 执行AIC逐步回归方法
cox_model_stepwise <- coxph(Surv(PFS, status==1) ~ mRecist+resswin3D_CR_Predict_label, data=data_subset)  # 使用所有变量进行初始拟合

# 使用stepAIC函数进行AIC逐步回归
AIC_model <- stepAIC(cox_model_stepwise, direction="both", trace=TRUE)  #both,forward和backward  

# 打印最终模型的摘要
summary(AIC_model)

# 使用step函数进行BIC逐步回归
BIC_model <- step(cox_model_stepwise, direction="both", trace=TRUE, k=log(nrow(data_subset)))
summary(BIC_model)

# 向前逐步选择变量，基于似然比检验
stepwise_model <- step(cox_model_stepwise, direction = "backward")
summary(stepwise_model)
stepwise_model <- step(cox_model_stepwise, direction = "forward", test = "wald")
summary(stepwise_model)


#cox回归模型的线性预测值，可用于绘制ROC和DCA
data_subset$linear.predictors<-cox_model_multivar$linear.predictors
head(data_subset$linear.predictors)

# data_subset$linear.predictors<-predict(cox_model_multivar,type="lp",newdata=data_subset)
# head(data_subset$linear.predictors)

write.xlsx(data_subset, file = "D:/dataset/test.xlsx", sheetName = "Results", row.names = FALSE)


# 计算Concordance指数和95% CI
c_index <- summary(cox_model_multivar)$concordance

# 设置bootstrap重复抽样次数
n_bootstrap <- 1000

# 创建一个空向量，用于存储bootstrap抽样得到的Concordance值
bootstrap_concordance <- numeric(n_bootstrap)

# 进行bootstrap重复抽样计算Concordance
for (i in 1:n_bootstrap) {
  # 对原始数据进行有放回抽样
  bootstrap_sample <- sample(1:nrow(data_subset), replace = TRUE)
  
  # 根据bootstrap样本计算Cox回归模型
  bootstrap_model <- coxph(Surv(PFS, status==1) ~ number+ap_score + bingliscore + deep_score, data=data_subset[bootstrap_sample, ])
  
  # 计算bootstrap样本的Concordance指数
  bootstrap_concordance[i] <- summary(bootstrap_model)$concordance
}

# 计算bootstrap抽样得到的Concordance的标准误差
bootstrap_se <- sd(bootstrap_concordance)

# 计算95%置信区间
lower_ci <- c_index - 1.96 * bootstrap_se
upper_ci <- c_index + 1.96 * bootstrap_se

# 打印结果
cat("Concordance:", c_index, "\n")
cat("lower_ci:", lower_ci)
cat("upper_ci:", upper_ci)


# DCA决策曲线绘制

# 安装并加载dca.R软件包
install.packages("dca.R")
library(dca.R)

# 使用dca()函数分别计算Cox模型和变量的决策曲线
coxm1_dca <- dca(coxm1, data = data)
var1_dca <- dca(coxm1, data = data, variables = "var1")
var2_dca <- dca(coxm1, data = data, variables = "var2")
var3_dca <- dca(coxm1, data = data, variables = "var3")

# 绘制决策曲线
plot(coxm1_dca, main = "Cox Model")
plot(var1_dca, main = "Variable 1")
plot(var2_dca, main = "Variable 2")
plot(var3_dca, main = "Variable 3")



#绘制cox多因素回归的森林图
# 加载库
library(survival)
library(survminer)
library(ggsci)
# 创建一个多因素回归森林图
forest_plot <- ggforest(AIC_model, data = data_subset)

# 创建一个多因素回归森林图，并进行美化
forest_plot <- ggforest(
  AIC_model,
  data = data_subset
) +
  ggtitle("Cox Regression Forest Plot") +  # 添加标题
  xlab("Hazard Ratio") +  # x 轴标签
  scale_fill_jco() +  # 使用 R 包 "ggsci" 中的 "jco" 调色板
  theme( # 使用主题修改图形外观
    legend.position = "right",  # 图例位置
    legend.title = element_text(face = "bold", size = 12),  # 图例标题样式
    legend.text = element_text(size = 11),  # 图例文本样式
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),  # 标题样式
    axis.title.x = element_text(face = "bold", size = 12),  # x 轴标签样式
    axis.text = element_text(size = 11)  # 轴文本样式
  )

# 显示图形
print(forest_plot)


#绘制cox多因素回归的森林图
library(ggplot2)
library(broom)

# 将模型结果转换为数据框
tidy_model <- tidy(AIC_model)

# 计算置信区间的宽度
tidy_model$conf.low <- tidy_model$estimate - 1.96 * tidy_model$std.error
tidy_model$conf.high <- tidy_model$estimate + 1.96 * tidy_model$std.error

# 绘制森林图
# 绘制森林图并添加文本标签
ggplot(tidy_model, aes(x = estimate, y = term, color = term)) +
  geom_point() +  # 绘制点
  geom_errorbarh(aes(xmin = conf.low, xmax = conf.high), height = 0.2) +  # 绘制水平误差线
  geom_vline(xintercept = 0, linetype = "dashed", color = "grey") +  # 绘制垂直参考线
  geom_text(aes(label = paste0("HR=", round(estimate, 2), " (95% CI ", round(conf.low, 2), "-", round(conf.high, 2), ")")),
            hjust = 1.1, size = 3, color = "black") +  # 添加文本标签
  labs(title = "Forest Plot of AIC Model", x = "Hazard Ratio", y = "Variables") +
  theme_minimal() +  # 使用简洁的主题
  theme(legend.position = "none",  # 不显示图例
        axis.text.y = element_text(color = "black"))  # 设置y轴文本颜色为黑色


#%%第6章代码开始

#Cox回归案例1
library(foreign)
library(survival)

pancer <- read.spss('pancer.sav')
pancer <- as.data.frame(pancer)
head(pancer)

pancer$censor <- ifelse(pancer$censor=='死亡',1,0)
pancer$Gender <- as.factor(ifelse(pancer$sex=='男',"Male","Female"))
pancer$ch <- as.factor(ifelse(pancer$ch=='CH3', "ch","nonch"))

#pancer$ch <- relevel(pancer$ch,ref="CH0") #设置因子的参照水平
#pancer$ch<- factor(pancer$ch,order=TRUE) #设置为等级变量
#options(contrasts=c("contr.treatment", "contr.treatment")) #指定等级变量的参照水平
#pancer$Gender <- relevel(pancer$Gender,ref='Female')

f<-coxph(Surv(time,censor==1)~age+Gender+trt+bui+ch+p+stage,data=pancer)
summary(f)
sum.surv<-summary(f)
c_index<-sum.surv$concordance
c_index

#Cox回归案例2
library(survival)
example15_4  <- read.table ("example15_4.csv", header=TRUE, sep=",")
attach(example15_4)
coxmodel  <- coxph(Surv(days, censor)~group)
summary(coxmodel)
coxmode2  <- coxph(Surv(days, censor)~group+renal)
summary(coxmode2)
anova(coxmodel,coxmode2)
detach(example15_4)

#Cox回归案例3
data('GBSG2',package = 'TH.data')
head(GBSG2)
plot(survfit(Surv(time, cens)~horTh,data = GBSG2),lty = c(2,1), col = c(2,1), mark.time = T)
legend('bottomright', legend = c('yes','no'), lty = c(2,1), col = c(2,1))

coxreg <- coxph(Surv(time,cens)~.,data = GBSG2)
summary(coxreg)

#install.packages("party")
library(party)
tree <- ctree(Surv(time,cens)~.,data = GBSG2)
plot(tree)


# Kaplan-Meier法估计
library(survival)
library(ISwR)
attach(melanom)
names(melanom)

#Surv(days, status==1)
#survfit(Surv(days, status==1)~1)
surv.all <- survfit(Surv(days,status==1)~1)
summary(surv.all)
plot(surv.all,col="blue")

surv.bysex <- survfit(Surv(days,status==1)~sex)
summary(surv.bysex)
plot(surv.bysex)
plot(surv.bysex, conf.int=T, col=c("red","blue"))
legend(locator(n=1),legend=c("male","female"),lty=1,col=c("blue","red"))


## log-rank test
f1<-survdiff(Surv(days,status==1)~sex,rho=0)
f1
f2<-survdiff(Surv(days,status==1)~sex,rho=1)
f2
f3<-survdiff(Surv(days,status==1)~sex+strata(ulc)) # With rho = 0 this is the log-rank or Mantel-Haenszel test, and with rho = 1 it is equivalent to the Peto & Peto modification of the Gehan-Wilcoxon test.
f3

## cox regression
f4<-coxph(Surv(days,status==1)~sex)
summary(f4)
#summary(coxph(Surv(days,status==1)~sex))
f5<-coxph(Surv(days,status==1)~sex+log(thick)+ulc)
summary(f5)
#summary(coxph(Surv(days,status==1)~sex+log(thick)+strata(ulc)))
plot(survfit(coxph(Surv(days,status==1)~
                     log(thick)+sex+ulc)),col=c("red","blue"))
#plot(survfit(coxph(Surv(days,status==1)~
                       #log(thick)+sex+strata(ulc))),col=c("red","blue"))
legend(locator(n=1),legend=c("ulceration present","ulceration absent"),lty=1,col=c("red","blue"))

detach(melanom)

# LifeTable寿命表法
hmohiv<-read.table("hmohiv.csv", sep=",", header = TRUE)
attach(hmohiv)
head(hmohiv)
library(KMsurv)
library(nlme)
t6m<-floor(time/6)
tall<-data.frame(t6m, censor)
die<-gsummary(tall, sum, groups=t6m)
total<-gsummary(tall, length, groups=t6m)
rm(t6m)
ltab.data<-cbind(die[,1:2], total[,2])
detach(hmohiv)
attach(ltab.data)

lt=length(t6m)
t6m[lt+1]=NA
nevent=censor
nlost=total[,2] - censor
mytable<-lifetab(t6m, 100, nlost, nevent)
mytable[,1:5]
plot(t6m[1:11], mytable[,5], type="s", xlab="Survival time in every 6 month", 
     ylab="Proportion Surviving")
detach(ltab.data)

#logrank检验案例1
#install.packages("survival")
library(survival)
example15_3 <- read.table ("example15_3.csv", header=TRUE, sep=",")
attach(example15_3)
total <- survfit(Surv(t, censor==1)~1)
summary(total)
plot(total,conf.int=F)
separate <- survfit(Surv(t, censor==1)~group)
summary(separate)
plot(separate, lty = c('solid','dashed'), col=c('black','blue'),
     xlab='survival time in days',ylab='survival probabilities')
legend('topright', c('Group A',' Group B'), lty=c('solid','dashed'),
       col=c('black','blue'))
survdiff(Surv(t, censor)~group)
survdiff(Surv(t, censor)~group,rho=1) # rho = 1 it is equivalent to the Peto & Peto modification of the Gehan-Wilcoxon test.
detach(example15_3)

#logrank检验案例2
library(coin)
data(glioma)
library(survival)
g3 <- subset(glioma, histology =='Grade3')
fit <- survfit(Surv(time, event)~group,data = g3)
plot(fit, lty = c(2,1), col = c(2,1))
legend('bottomright', legend = c('Control','Treatment'), lty = c(2,1), col = c(2,1))
survdiff(Surv(time, event)~group,data = g3) 
logrank_test(Surv(time, event)~group,data = g3, distribution ="exact")
logrank_test(Surv(time, event)~group|histology,data = glioma, distribution = approximate(B = 1000)) #两组比较,coin包 logrank_test函数#SurvivalTests {coin}

#画一幅高水准的生存曲线
library(survival)
library(survminer)
fit <- survfit(Surv(time, status) ~ sex, data = lung)

ggsurvplot(fit,
           pval = TRUE, # 在图上添加log rank检验的p值
           conf.int = TRUE,# 添加置信区间
           risk.table = TRUE, # 在图下方添加风险表
           risk.table.col = "strata", # 根据数据分组为风险表添加颜色
           linetype = "strata", # 改变不同组别的生存曲线的线型
           surv.median.line = "hv", # 标注出中位生存时间
           ggtheme = theme_bw(), # 改变图形风格
           palette = c("#E7B800", "#2E9FDF")) # 图形颜色风格


ggsurvplot(
  fit,                    
  pval = FALSE,             
  conf.int = TRUE, 
  fun = "cumhaz",
  conf.int.style = "ribbon",  # 设置置信区间的风格
  xlab = "Time in days",   # 设置x轴标签
  break.time.by = 200,     # 将x轴按照200为间隔进行切分
  ggtheme = theme_light(), # 设置图形风格
  risk.table = "abs_pct",  # 在风险表中添加绝对数和相对数
  risk.table.y.text.col = TRUE,# 设置风险表的文字颜色
  risk.table.y.text = FALSE,# 以条柱展示风险表的标签，而非文字
  ncensor.plot = TRUE,      # 展示随访过程中不同时间点死亡和删失的情况
  surv.median.line = "hv",  # 添加中位生存时间
  legend.labs = 
    c("Male", "Female"),    # 改变图例标签
  palette = 
    c("#E7B800", "#2E9FDF") # 设置颜色
)


ggsurvplot(fit,
           conf.int = TRUE,
           risk.table.col = "strata", 
           ggtheme = theme_bw(), 
           palette = c("#E7B800", "#2E9FDF"),
           fun = "cumhaz")
dev.off()