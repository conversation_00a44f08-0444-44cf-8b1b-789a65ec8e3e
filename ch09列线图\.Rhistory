library(multcomp)
levels(cholesterol$trt)
fit.aov <- aov(response ~ trt, data=cholesterol)
summary(fit.aov)
fit.lm <- lm(response ~ trt, data=cholesterol)
summary(fit.lm)
fit.lm <- lm(response ~ trt, data=cholesterol, contrasts="contr.helmert")
summary(fit.lm)
# fit.lm <- lm(response ~ trt, data=cholesterol, contrasts="contr.helmert") # wrong
fit.lm <- lm(response ~ trt, data=cholesterol, contrasts=list(trt="contr.helmert"))
summary(fit.lm)
# fit.lm <- lm(response ~ trt, data=cholesterol, contrasts="contr.helmert") # wrong
fit.lm <- lm(response ~ trt, data=cholesterol, contrasts=list(trt="contr.SAS"))
summary(fit.lm)
fit.lm <- lm(response ~ trt, data=cholesterol, contrasts=list(trt=“contr.helmert”))
fit.lm <- lm(response ~ trt, data=cholesterol, contrasts=list(trt="contr.helmert"))
library(foreign)
library(rms)
mydata<-read.spss("lweight.sav")
mydata<-as.data.frame(mydata)
head(mydata)
View(mydata)
mydata<-read.spss("lweight.sav")
library(foreign)
library(rms)
mydata<-read.spss("lweight.sav")
mydata<-as.data.frame(mydata)
View(mydata)
head(mydata)
mydata$low <- ifelse(mydata$low =="低出生体重",1,0)
library(foreign)
library(rms)
mydata<-read.spss("lweight.sav")
mydata<-as.data.frame(mydata)
View(mydata)
attach(mydata)
mydata$race1 <- ifelse(mydata$race =="白种人",1,0)
mydata$race2 <- ifelse(mydata$race =="黑种人",1,0)
mydata$race3 <- ifelse(mydata$race =="其他种族",1,0)
fit1<-lrm(low~age+ftv+ht+lwt+ptl+smoke+ui+race1+race2,data=mydata,x=T,y=T)
fit1
summary(fit1)
dd<-datadist(pancer)
options(datadist='dd')
dd<-datadist(mydata)
options(datadist='dd')
fit1<-lrm(low~age+ftv+ht+lwt+ptl+smoke+ui+race1+race2,data=mydata,x=T,y=T)
fit1
summary(fit1)
nom1 <- nomogram(fit1, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="pCR rate")
plot(nom1)
nom1 <- nomogram(fit1, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="low weight rate")
plot(nom1)
cal1 <- calibrate(fit1, cmethod='hare',method='boot', B=1000)
plot(cal1,xlim=c(0,0.18),ylim=c(0,0.18))
plot(cal1,xlim=c(0,1.0),ylim=c(0,1.0))
mydata$race <- as.factor(ifelse(mydata$race=="白种人", 0,1))
dd<-datadist(mydata)
options(datadist='dd')
dd<-datadist(mydata)
options(datadist='dd')
fit2<-lrm(low~age+ftv+ht+lwt+ptl+smoke+ui+race,data=mydata,x=T,y=T)
fit2
mydata$race <- as.factor(ifelse(mydata$race=="白种人", "白种人","黑人及其他种族"))
library(foreign)
library(rms)
mydata<-read.spss("lweight.sav")
mydata<-as.data.frame(mydata)
head(mydata)
mydata$low <- ifelse(mydata$low =="低出生体重",1,0)
View(mydata)
mydata$race1 <- ifelse(mydata$race =="白种人",1,0)
mydata$race2 <- ifelse(mydata$race =="黑种人",1,0)
mydata$race3 <- ifelse(mydata$race =="其他种族",1,0)
attach(mydata)
dd<-datadist(mydata)
options(datadist='dd')
fit1<-lrm(low~age+ftv+ht+lwt+ptl+smoke+ui+race1+race2,data=mydata,x=T,y=T)
fit1
summary(fit1)
nom1 <- nomogram(fit1, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom1)
cal1 <- calibrate(fit1, cmethod='hare',method='boot', B=1000)
plot(cal1,xlim=c(0,1.0),ylim=c(0,1.0))
mydata$race <- as.factor(ifelse(mydata$race=="白种人", "白种人","黑人及其他种族"))
dd<-datadist(mydata)
options(datadist='dd')
fit2<-lrm(low~age+ftv+ht+lwt+ptl+smoke+ui+race,data=mydata,x=T,y=T)
fit2
summary(fit2)
nom2 <- nomogram(fit2, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom2)
cal2 <- calibrate(fit2, cmethod='hare',method='boot', B=1000)
plot(cal2,xlim=c(0,1.0),ylim=c(0,1.0))
fit3
fit3<-lrm(low~ht+lwt+ptl+smoke+race,data=mydata,x=T,y=T)
fit3
summary(fit3)
nom3 <- nomogram(fit3, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom3)
cal3 <- calibrate(fit3, cmethod='hare',method='boot', B=1000)
plot(cal3,xlim=c(0,1.0),ylim=c(0,1.0))
library(foreign)
library(rms)
mydata<-read.spss("lweight.sav")
mydata<-as.data.frame(mydata)
View(mydata)
head(mydata)
mydata$low <- ifelse(mydata$low =="低出生体重",1,0)
mydata$race1 <- ifelse(mydata$race =="白种人",1,0)
mydata$race2 <- ifelse(mydata$race =="黑种人",1,0)
mydata$race3 <- ifelse(mydata$race =="其他种族",1,0)
attach(mydata)
dd<-datadist(mydata)
options(datadist='dd')
fit1<-lrm(low~age+ftv+ht+lwt+ptl+smoke+ui+race1+race2,data=mydata,x=T,y=T)
fit1
coefficients(fit1)
exp(coefficients(fit1))
confint(exp(coefficients(fit1)))
confint(fit1)
summary(fit1)
nom1 <- nomogram(fit1, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom1)
cal1 <- calibrate(fit1, cmethod='hare',method='boot', B=1000)
plot(cal1,xlim=c(0,1.0),ylim=c(0,1.0))
mydata$race <- as.factor(ifelse(mydata$race=="白种人", "白种人","黑人及其他种族"))
dd<-datadist(mydata)
options(datadist='dd')
fit2<-lrm(low~age+ftv+ht+lwt+ptl+smoke+ui+race,data=mydata,x=T,y=T)
fit2
summary(fit2)
nom2 <- nomogram(fit2, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom2)
fit3<-lrm(low~ht+lwt+ptl+smoke+race,data=mydata,x=T,y=T)
fit3
summary(fit3)
nom3 <- nomogram(fit3, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom3)
cal3 <- calibrate(fit3, cmethod='hare',method='boot', B=1000)
plot(cal3,xlim=c(0,1.0),ylim=c(0,1.0))
library(foreign)
library(rms)
pancer <- read.spss('pancer.sav')
pancer <- as.data.frame(pancer)
head(pancer)
View(pancer)
pancer$censor <- ifelse(pancer$censor=='死亡',1,0)
pancer$Gender <- as.factor(ifelse(pancer$sex=='男',"Male","Female"))
pancer$ch <- as.factor(ifelse(pancer$ch=='CH3', "ch","noch"))
dd<-datadist(pancer)
options(datadist='dd')
coxm <- cph(Surv(time,censor==1)~age+Gender+trt+bui+ch+p+stage,x=T,y=T,data=pancer,surv=T)
coxm
surv <- Survival(coxm)
surv1 <- function(x)surv(1*3,lp=x)
surv2 <- function(x)surv(1*6,lp=x)
surv3 <- function(x)surv(1*12,lp=x)
plot(nomogram(coxm,fun=list(surv1,surv2,surv3),lp= F,funlabel=c('3-Month Survival probability','6-Month survival probability','12-Month survival probability'),maxscale=100,fun.at=c('0.9','0.85','0.80','0.70','0.6','0.5','0.4','0.3','0.2','0.1')),xfrac=.30)
library(survival)
f<-coxph(Surv(time,censor==1)~age+Gender+trt+bui+ch+p+stage,data=pancer)
summary(f)
cal <- calibrate(coxm, cmethod='KM', method='boot', u=6, m=20, B=1000)
plot(cal,lwd=2,lty=1,errbar.col=c(rgb(0,118,192,maxColorValue=255)),xlim=c(0,1),ylim=c(0,1),xlab="Nomogram-Predicted Probabilityof 6 m OS",ylab="Actual 6 m OS (proportion)",col=c(rgb(192,98,83,maxColorValue=255)))
library(foreign)
library(rms)
mydata<-read.spss("lweight.sav")
mydata<-as.data.frame(mydata)
head(mydata)
mydata$low <- ifelse(mydata$low =="低出生体重",1,0)
mydata$race1 <- ifelse(mydata$race =="白种人",1,0)
mydata$race2 <- ifelse(mydata$race =="黑种人",1,0)
mydata$race3 <- ifelse(mydata$race =="其他种族",1,0)
attach(mydata)
dd<-datadist(mydata)
options(datadist='dd')
fit1<-lrm(low~age+ftv+ht+lwt+ptl+smoke+ui+race1+race2,data=mydata,x=T,y=T)
fit1
summary(fit1)
nom1 <- nomogram(fit1, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom1)
cal1 <- calibrate(fit1, cmethod='hare',method='boot', B=1000)
plot(cal1,xlim=c(0,1.0),ylim=c(0,1.0))
mydata$race <- as.factor(ifelse(mydata$race=="白种人", "白种人","黑人及其他种族"))
dd<-datadist(mydata)
options(datadist='dd')
fit2<-lrm(low~age+ftv+ht+lwt+ptl+smoke+ui+race,data=mydata,x=T,y=T)
fit2
summary(fit2)
nom2 <- nomogram(fit2, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom2)
cal2 <- calibrate(fit2, cmethod='hare',method='boot', B=1000)
plot(cal2,xlim=c(0,1.0),ylim=c(0,1.0))
fit3<-lrm(low~ht+lwt+ptl+smoke+race,data=mydata,x=T,y=T)
fit3
summary(fit3)
nom3 <- nomogram(fit3, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom3)
cal3 <- calibrate(fit3, cmethod='hare',method='boot', B=1000)
plot(cal3,xlim=c(0,1.0),ylim=c(0,1.0))
## 绘制nomogram图
## 第一步 读取rms程序包及辅助程序包
library(rms)
#library(Hmisc)
#library(grid)
#library(lattice)
#library(Formula)
#library(ggplot2)
## 第二步 读取数据，以survival程序包的lung数据来进行演示
## 列举survival程序包中的数据集
library(survival)
#library(Hmisc)
#library(grid)
#library(lattice)
#library(Formula)
#library(ggplot2)
## 第二步 读取数据，以survival程序包的lung数据来进行演示
## 列举survival程序包中的数据集
#library(survival)
data(package = "survival")
## 读取lung数据集
data(lung)
## 显示lung数据集的前6行结果
head(lung)
## 显示lung数据集的变量说明
help(lung)
## 添加变量标签以便后续说明
lung$sex <-
factor(lung$sex,
levels = c(1,2),
labels = c("male", "female"))
## 第三步 按照nomogram要求“打包”数据，绘制nomogram的关键步骤,??datadist 可查看详细说明
dd=datadist(lung)
options(datadist="dd")
## 第四步 构建模型
## 构建logisitc回归模型
f1 <- lrm(status~ age + sex, data = lung)
## 绘制logisitc回归的风险预测值的nomogram图
nom <- nomogram(f1, fun= function(x)1/(1+exp(-x)), # or fun=plogis
lp=F, funlabel="Risk")
plot(nom)
## 构建COX比例风险模型
f2 <- psm(Surv(time,status) ~ age + sex, data = lung, dist='lognormal')
med <- Quantile(f2) # 计算中位生存时间
surv <- Survival(f2) # 构建生存概率函数
## 绘制COX回归中位生存时间的Nomogram图
nom <- nomogram(f2, fun=function(x) med(lp=x),
funlabel="Median Survival Time")
plot(nom)
## 绘制COX回归生存概率的Nomogram图
## 注意lung数据的time是以”天“为单位
nom <- nomogram(f2, fun=list(function(x) surv(365, x),
function(x) surv(730, x)),
funlabel=c("1-year Survival Probability",
"2-year Survival Probability"))
plot(nom, xfrac=.6)
## 评价COX回归的预测效果
## 第一步 计算c-index
rcorrcens(Surv(time,status) ~ predict(f2), data =  lung)
## 重新调整模型函数f2，也即添加x=T, y=T
f2 <- psm(Surv(time,status) ~ age+sex, data =  lung, x=T, y=T, dist='lognormal')
## 构建校正曲线
cal1 <- calibrate(f2, cmethod='KM', method="boot", u=365, m=76, B=228)
## 绘制校正曲线，??rms::calibrate 可查看详细参数说明
par(mar=c(8,5,3,2),cex = 1.0)
plot(cal1,lwd=2,lty=1,
errbar.col=c(rgb(0,118,192,maxColorValue=255)),
xlim=c(0.25,0.6),ylim=c(0.15,0.70),
xlab="Nomogram-Predicted Probability of 1-Year DFS",
ylab="Actual 1-Year DFS (proportion)",
col=c(rgb(192,98,83,maxColorValue=255)))
library(foreign)
library(rms)
mydata<-read.spss("lweight.sav")
mydata<-as.data.frame(mydata)
head(mydata)
library(foreign)
library(rms)
mydata<-read.spss("lweight.sav")
mydata<-as.data.frame(mydata)
View(mydata)
head(mydata)
mydata
head(mydata)
mydata$low <- ifelse(mydata$low =="低出生体重",1,0)
mydata$race1 <- ifelse(mydata$race =="白种人",1,0)
mydata$race2 <- ifelse(mydata$race =="黑种人",1,0)
mydata$race3 <- ifelse(mydata$race =="其他种族",1,0)
attach(mydata)
dd<-datadist(mydata)
dd<-datadist(mydata)
options(datadist='dd')
?lrm
fit1<-lrm(low~age+ftv+ht+lwt+ptl+smoke+ui+race1+race2,data=mydata,x=T,y=T)
fit1
summary(fit1)
?nomogram
nom1 <- nomogram(fit1, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom1)
cal1 <- calibrate(fit1, cmethod='hare',method='boot', B=1000)
plot(cal1,xlim=c(0,1.0),ylim=c(0,1.0))
mydata$race <- as.factor(ifelse(mydata$race=="白种人", "白种人","黑人及其他种族"))
dd<-datadist(mydata)
options(datadist='dd')
fit2<-lrm(low~age+ftv+ht+lwt+ptl+smoke+ui+race,data=mydata,x=T,y=T)
fit2
summary(fit2)
nom2 <- nomogram(fit2, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom2)
cal2 <- calibrate(fit2, cmethod='hare',method='boot', B=1000)
plot(cal2,xlim=c(0,1.0),ylim=c(0,1.0))
fit3<-lrm(low~ht+lwt+ptl+smoke+race,data=mydata,x=T,y=T)
fit3
nom3 <- nomogram(fit3, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom3)
fit3<-lrm(low~ht+lwt+ptl+smoke+race2+race3,data=mydata,x=T,y=T)
fit3
cal3 <- calibrate(fit3, cmethod='hare',method='boot', B=1000)
plot(cal3,xlim=c(0,1.0),ylim=c(0,1.0))
library(foreign)
library(rms)
help(package="rms")
help(package="Hmisc")
?read.spss
pancer <- read.spss('pancer.sav')
pancer <- as.data.frame(pancer)
View(pancer)
head(pancer)
pancer$censor <- ifelse(pancer$censor=='死亡',1,0)
attach(pancer)
pancer$Gender <- as.factor(ifelse(pancer$sex=='男',"Male","Female"))
pancer$ch <- as.factor(ifelse(pancer$ch=='CH3', "ch","noch"))
dd<-datadist(pancer)
options(datadist='dd')
?cph
?coxph
coxm <- cph(Surv(time,censor==1)~age+Gender+trt+bui+ch+p+stage,x=T,y=T,data=pancer,surv=T)
coxm
surv <- Survival(coxm)
surv1 <- function(x)surv(1*3,lp=x)
surv2 <- function(x)surv(1*6,lp=x)
surv3 <- function(x)surv(1*12,lp=x)
nom<-nomogram(coxm,fun=list(surv1,surv2,surv3),lp= F,funlabel=c('3-Month Survival probability','6-Month survival probability','12-Month survival probability'),maxscale=100,fun.at=c('0.9','0.85','0.80','0.70','0.6','0.5','0.4','0.3','0.2','0.1'))
plot(nom)
coxm <- cph(Surv(time,censor==1)~age+trt+bui+p+stage,x=T,y=T,data=pancer,surv=T)
coxm
surv <- Survival(coxm)
surv1 <- function(x)surv(1*3,lp=x)
surv2 <- function(x)surv(1*6,lp=x)
surv3 <- function(x)surv(1*12,lp=x)
nom1<-nomogram(coxm,fun=list(surv1,surv2,surv3),lp= F,funlabel=c('3-Month Survival probability','6-Month survival probability','12-Month survival probability'),maxscale=100,fun.at=c('0.9','0.85','0.80','0.70','0.6','0.5','0.4','0.3','0.2','0.1'))
plot(nom1)
cal <- calibrate(coxm, cmethod='KM', method='boot', u=6, m=20, B=1000)
plot(cal,lwd=2,lty=1,errbar.col=c(rgb(0,118,192,maxColorValue=255)),xlim=c(0,1),ylim=c(0,1),xlab="Nomogram-Predicted Probabilityof 6 m OS",ylab="Actual 6 m OS (proportion)",col=c(rgb(192,98,83,maxColorValue=255)))
cal1 <- calibrate(coxm, cmethod='KM', method='boot', u=3, m=20, B=1000)
plot(cal1,lwd=2,lty=1,errbar.col=c(rgb(0,118,192,maxColorValue=255)),xlim=c(0,1),ylim=c(0,1),xlab="Nomogram-Predicted Probabilityof 3 m OS",ylab="Actual 3 m OS (proportion)",col=c(rgb(192,98,83,maxColorValue=255)))
library(survival)
f<-coxph(Surv(time,censor==1)~age+Gender+trt+bui+ch+p+stage,data=pancer)
summary(f)
lines(cal[,c("mean.predicted","KM")],type="b",lwd=2,col=c(rgb(192,98,83,maxColorValue=255)),pch=16)
abline(0,1,lty=3,lwd=2,col=c(rgb(0,118,192,maxColorValue=255)))
abline(0,1,lty=3,lwd=2,col=c(rgb(0,118,192,maxColorValue=255)))
summary(f)
help(package=rms)
library(foreign)
library(rms)
mydata<-read.spss("lweight.sav")
mydata<-as.data.frame(mydata)
View(mydata)
mydata$low <- ifelse(mydata$low =="低出生体重",1,0)
mydata$race1 <- ifelse(mydata$race =="白种人",1,0)
mydata$race2 <- ifelse(mydata$race =="黑种人",1,0)
mydata$race3 <- ifelse(mydata$race =="其他种族",1,0)
attach(mydata)
fglm <- glm(low ~ age+ftv+ht+lwt+ptl+smoke+ui+race2+race3,data=mydata,family = binomial())
summary(fglm)
dd<-datadist(mydata)
options(datadist='dd')
?lrm
fit1
fit1<-lrm(low~age+ftv+ht+lwt+ptl+smoke+ui+race2+race3,data=mydata,x=T,y=T)
fit1
0.746/2+0.5
0.476/2+0.5
summary(fit1)
nom1 <- nomogram(fit1, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom1)
nom1 <- nomogram(fit1, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=T, funlabel="Low weight rate")
plot(nom1)
nom1 <- nomogram(fit1, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom1)
cal1 <- calibrate(fit1, cmethod='hare', method='boot', B=1000)
plot(cal1,xlim=c(0,1.0),ylim=c(0,1.0))
mydata$race <- as.factor(ifelse(mydata$race=="白种人", "白种人","黑人及其他种族"))
dd<-datadist(mydata)
options(datadist='dd')
fit3<-lrm(low~ht+lwt+ptl+smoke+race,data=mydata,x=T,y=T)
fit3
summary(fit3)
nom3 <- nomogram(fit3, fun=plogis,fun.at=c(.001, .01, .05, seq(.1,.9, by=.1), .95, .99, .999),lp=F, funlabel="Low weight rate")
plot(nom3)
cal3 <- calibrate(fit3, cmethod='hare',method='boot', B=1000)
plot(cal3,xlim=c(0,1.0),ylim=c(0,1.0))
f<-glm(low ~ age+ftv+ht+lwt+ptl+smoke+ui+race2+race3,data=mydata,family = binomial())
f
##
library(foreign)
library(rms)
pancer <- read.spss('pancer.sav')
pancer <- as.data.frame(pancer)
View(pancer)
head(pancer)
pancer$censor <- ifelse(pancer$censor=='死亡',1,0)
pancer$Gender <- as.factor(ifelse(pancer$sex=='男',"Male","Female"))
pancer$ch <- as.factor(ifelse(pancer$ch=='CH3', "ch","nonch"))
f<-coxph(Surv(time,censor==1)~age+Gender+trt+bui+ch+p+stage,data=pancer)
f
summary(f)
dd<-datadist(pancer)
options(datadist='dd')
coxm <- cph(Surv(time,censor==1)~age+Gender+trt+bui+ch+p+stage,x=T,y=T,data=pancer,surv=T)
coxm
summary(coxm)
surv <- Survival(coxm)
surv1 <- function(x)surv(1*3,lp=x)
surv2 <- function(x)surv(1*6,lp=x)
surv3 <- function(x)surv(1*12,lp=x)
nom1<-nomogram(coxm,fun=list(surv1,surv2,surv3),lp = F,
funlabel=c('3-Month Survival probability',
'6-Month survival probability',
'12-Month survival probability'),
maxscale=100,
fun.at=c('0.9','0.85','0.80','0.70','0.6','0.5','0.4','0.3','0.2','0.1'))
plot(nom1)
coxm2 <- cph(Surv(time,censor==1)~age+trt+bui+p+stage,x=T,y=T,data=pancer,surv=T)
coxm2
summary(coxm2)
surv <- Survival(coxm2)
surv1 <- function(x)surv(1*3,lp=x)
surv2 <- function(x)surv(1*6,lp=x)
surv3 <- function(x)surv(1*12,lp=x)
nom2<-nomogram(coxm2,fun=list(surv1,surv2,surv3),lp = F,
funlabel=c('3-Month Survival probability',
'6-Month survival probability',
'12-Month survival probability'),
maxscale=100,
fun.at=c('0.9','0.85','0.80','0.70','0.6','0.5','0.4','0.3','0.2','0.1'))
plot(nom2)
0.305/2+0.5
f2 <- coxph(Surv(time,censor==1)~age+trt+bui+p+stage,data=pancer)
f2
summary(f2)
cal <- calibrate(coxm2, cmethod='KM', method='boot', u=3, m=20, B=1000)
plot(cal,lwd=2,lty=1,errbar.col=c(rgb(0,118,192,maxColorValue=255)),xlim=c(0,1),ylim=c(0,1),xlab="Nomogram-Predicted Probabilityof 3 months OS",ylab="Actual 3 months OS (proportion)",col=c(rgb(192,98,83,maxColorValue=255)))
lines(cal[,c("mean.predicted","KM")],type="b",lwd=2,col=c(rgb(192,98,83,maxColorValue=255)),pch=16)
?plot
?par
lines(cal[,c("mean.predicted","KM")],type="b",lwd=2,col=c(rgb(192,98,83,maxColorValue=255)),pch=1)
abline(0,1,lty=3,lwd=2,col=c(rgb(0,118,192,maxColorValue=255)))
cal1 <- calibrate(coxm2, cmethod='KM', method='boot', u=6, m=20, B=1000)
plot(cal1,lwd=2,lty=1,errbar.col=c(rgb(0,118,192,maxColorValue=255)),xlim=c(0,1),ylim=c(0,1),xlab="Nomogram-Predicted Probabilityof 6 months OS",ylab="Actual 6 months OS (proportion)",col=c(rgb(192,98,83,maxColorValue=255)))
library(foreign)
library(rms)
library(foreign) #导入外部数据
data2<- read.csv("C:/Users/<USER>/Desktop/ch05/data2.csv")
mydata<-as.data.frame(data2)
head(mydata)
library(foreign)
library(rms)
library(foreign) #导入外部数据
data2<- read.csv("E:/Rcode/validation.csv")
mydata<-as.data.frame(data2)
head(mydata)
dd<-datadist(mydata)
options(datadist='dd')
fit1<-lrm(DPHCC~rim.hyperenhancement.ap+margin+capsule+SIPP+SIRAP,data=mydata,x=T,y=T) #group为因变量，x为自变量,如结果提示变量有问题，可删除变量之一再试试
fit1
summary(fit1)
nom1 <- nomogram(fit1, fun=plogis,fun.at=c(seq(.1,.9, by=.1), .95),lp=F, funlabel="risk of group2")
plot(nom1,fun.side=c(1,3,1,3,1,3,1,3,1,1),label.every=2)
plot(nom1,fun.side=c(1,3,1,3,1,3,1,3,1,3,1,3),label.every=1)
cal1 <- calibrate(fit1, cmethod='hare', method='boot', B=1000)
plot(cal1,xlim=c(0,1.0),ylim=c(0,1.0))
