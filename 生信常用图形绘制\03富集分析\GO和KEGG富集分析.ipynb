{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# install.packages(\"clusterProfiler\")\n", "# install.packages(\"devtools\") \n", "# devtools::install_github(\"BioSenior/clusterProfiler\")\n", "\n", "install.packages(\"BiocManager\")\n", "BiocManager::install(\"topGO\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["载入需要的程辑包：AnnotationDbi\n", "\n", "载入需要的程辑包：stats4\n", "\n", "载入需要的程辑包：BiocGenerics\n", "\n", "\n", "载入程辑包：'BiocGenerics'\n", "\n", "\n", "The following objects are masked from 'package:stats':\n", "\n", "    IQR, mad, sd, var, xtabs\n", "\n", "\n", "The following objects are masked from 'package:base':\n", "\n", "    anyDuplicated, aperm, append, as.data.frame, basename, cbind,\n", "    colnames, dirname, do.call, duplicated, eval, evalq, Filter, Find,\n", "    get, grep, grepl, intersect, is.unsorted, lapply, Map, mapply,\n", "    match, mget, order, paste, pmax, pmax.int, pmin, pmin.int,\n", "    Position, rank, rbind, Reduce, rownames, sapply, setdiff, sort,\n", "    table, tapply, union, unique, unsplit, which.max, which.min\n", "\n", "\n", "载入需要的程辑包：Biobase\n", "\n", "Welcome to Bioconductor\n", "\n", "    Vignettes contain introductory material; view with\n", "    'browseVignettes()'. To cite Bioconductor, see\n", "    'citation(\"Biobase\")', and for packages 'citation(\"pkgname\")'.\n", "\n", "\n", "载入需要的程辑包：IRanges\n", "\n", "载入需要的程辑包：S4Vectors\n", "\n", "\n", "载入程辑包：'S4Vectors'\n", "\n", "\n", "The following object is masked from 'package:utils':\n", "\n", "    findMatches\n", "\n", "\n", "The following objects are masked from 'package:base':\n", "\n", "    expand.grid, I, unname\n", "\n", "\n", "\n", "载入程辑包：'IRanges'\n", "\n", "\n", "The following object is masked from 'package:grDevices':\n", "\n", "    windows\n", "\n", "\n", "\n", "\n", "\n", "\n", "clusterProfiler v4.10.1  For help: https://yulab-smu.top/biomedical-knowledge-mining-book/\n", "\n", "If you use clusterProfiler in published research, please cite:\n", "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. clusterProfiler 4.0: A universal enrichment tool for interpreting omics data. The Innovation. 2021, 2(3):100141\n", "\n", "\n", "载入程辑包：'clusterProfiler'\n", "\n", "\n", "The following object is masked from 'package:AnnotationDbi':\n", "\n", "    select\n", "\n", "\n", "The following object is masked from 'package:IRanges':\n", "\n", "    slice\n", "\n", "\n", "The following object is masked from 'package:S4Vectors':\n", "\n", "    rename\n", "\n", "\n", "The following object is masked from 'package:stats':\n", "\n", "    filter\n", "\n", "\n", "##############################################################################\n", "Pathview is an open source software package distributed under GNU General\n", "Public License version 3 (GPLv3). Details of GPLv3 is available at\n", "http://www.gnu.org/licenses/gpl-3.0.html. Particullary, users are required to\n", "formally cite the original Pathview paper (not just mention it) in publications\n", "or products. For details, do citation(\"pathview\") within R.\n", "\n", "The pathview downloads and uses KEGG data. Non-academic uses may require a KEGG\n", "license agreement (details at http://www.kegg.jp/kegg/legal.html).\n", "##############################################################################\n", "\n", "载入需要的程辑包：graph\n", "\n", "载入需要的程辑包：GO.db\n", "\n", "载入需要的程辑包：SparseM\n", "\n", "\n", "载入程辑包：'SparseM'\n", "\n", "\n", "The following object is masked from 'package:base':\n", "\n", "    backsolve\n", "\n", "\n", "\n", "groupGOTerms: \tGOBPTerm, GOMFTerm, GOCCTerm environments built.\n", "\n", "\n", "载入程辑包：'topGO'\n", "\n", "\n", "The following object is masked from 'package:IRanges':\n", "\n", "    members\n", "\n", "\n", "Warning message in read.table(file = file, header = header, sep = sep, quote = quote, :\n", "\"输入链结'D:/R-code/gene.csv'内的输入不对\"\n", "Warning message in read.table(file = file, header = header, sep = sep, quote = quote, :\n", "\"incomplete final line found by readTableHeader on 'D:/R-code/gene.csv'\"\n"]}, {"ename": "ERROR", "evalue": "Error in barplot.default(ego, showCategory = 10, x = \"GeneRatio\"): 参数3有多个与之相对应的正式参数\n", "output_type": "error", "traceback": ["Error in barplot.default(ego, showCategory = 10, x = \"GeneRatio\"): 参数3有多个与之相对应的正式参数\nTraceback:\n", "1. barplot(ego, showCategory = 10, x = \"GeneRatio\")"]}], "source": ["# rm(list = ls())\n", "### R包载入：\n", "library(org.Hs.eg.db)\n", "library(clusterProfiler)\n", "library(pathview)\n", "library(topGO)\n", "library(ggplot2)\n", "\n", "# 读取文件，差异分析结果：\n", "degs <- read.csv(\"D:/R-code/gene.csv\",fileEncoding = \"UTF-8\")\n", "\n", "DEG_list <- as.character(degs[,1])\n", "\n", "#################################\n", "# GO富集分析：\n", "ego <- enrichGO(gene          = DEG_list,\n", "                OrgDb         = org.Hs.eg.db,\n", "                keyType = \"SYMBOL\",\n", "                ont           = \"ALL\",\n", "                pAdjustMethod = \"BH\",\n", "                pvalueCutoff  = 0.01,\n", "                qvalue<PERSON><PERSON>ff  = 0.01)\n", "\n", "# 保存富集分析结果：\n", "# write.table(ego, file = \"Go_Enrichment.txt\",sep=\"\\t\", row.names =F, quote = F)\n", "\n", "\n", "# 转换为data.frame,方便查看：\n", "ego_results<-as.data.frame(ego)\n", "\n", "pdf(\"GO_barplot.pdf\",height = 8, width = 9)\n", "## 最简单的方法绘制柱状图：直接barplot就可以搞定：\n", "barplot(ego, showCategory=10, x = \"GeneRatio\")\n", "dev.off()\n", "\n", "pdf(\"GO_dotplot.pdf\",height = 8, width = 9)\n", "## 最简单的方法绘制柱状图：直接dotplot就可以搞定：\n", "dotplot(ego,showCategory=10)\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["library(clusterProfiler)\n", "library(pathview)\n", "library(topGO)\n", "library(ggplot2)\n", "\n", "# 读取文件并转换为DataFrame\n", "# 读取文件并转换为DataFrame\n", "data <- read.table(\"D:/R-code/Go_Enrichment.txt\", header = TRUE, sep = \"\\t\")\n", "\n", "# 将读取的数据存储在DataFrame中\n", "ego <- as.data.frame(data)\n", "\n", "# pdf(\"GO_barplot.pdf\",height = 8, width = 9)\n", "## 最简单的方法绘制柱状图：直接barplot就可以搞定：\n", "barplot(ego, showCategory=10, x = \"GeneRatio\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#####################\n", "# KEGG富集分析：\n", "# 先转换ID：将gene名转换为ENTREZID\n", "eg = bitr(DEG_list, fromType=\"SYMBOL\", toType=\"ENTREZID\", OrgDb=\"org.Hs.eg.db\")\n", "\n", "kegg <- enrichKEGG(\n", "  gene = eg$ENTREZID,\n", "  keyType = \"kegg\",\n", "  organism  = 'human',\n", "  pvalueCutoff  = 0.05,\n", "  pAdjustMethod  = \"BH\",\n", "  qvalueCutoff  = 0.05\n", ")\n", "\n", "# 保存富集分析结果：\n", "# write.table(kegg, file = \"kegg_Enrichment.txt\",sep=\"\\t\", row.names =F, quote = F)\n", "\n", "# 转换为data.frame,方便查看：\n", "kegg_results<-as.data.frame(kegg)\n", "\n", "pdf(\"KEGG_barplot.pdf\",height = 8, width = 9)\n", "## 最简单的方法绘制柱状图：直接barplot就可以搞定：\n", "barplot(kegg, showCategory=10, x = \"GeneRatio\")\n", "dev.off()\n", "\n", "pdf(\"KEGG_dotplot.pdf\",height = 8, width = 9)\n", "## 最简单的方法绘制柱状图：直接dotplot就可以搞定：\n", "dotplot(kegg,showCategory=10)\n", "dev.off()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# 安装和加载所需的包\n", "install.packages(\"pathview\")\n", "library(pathview)\n", "\n", "# 导入KEGG富集分析结果数据\n", "enrichment_results <- read.table(\"enrichment_results.txt\", header = TRUE, sep = \"\\t\")\n", "\n", "# 提取关键列数据\n", "gene_set <- enrichment_results$GeneSet\n", "p_value <- enrichment_results$PValue\n", "gene_count <- enrichment_results$GeneCount\n", "\n", "# 进行气泡图绘制\n", "pathview(gene.data = gene_set, \n", "         pathway.id.type = \"KEGG\", \n", "         pv.in = p_value, \n", "         gene.data.type = \"id\", \n", "         keypos = \"topright\", \n", "         col = \"blue\", \n", "         size = gene_count)"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}