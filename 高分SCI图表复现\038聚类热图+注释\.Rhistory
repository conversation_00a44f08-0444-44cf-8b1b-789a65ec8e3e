}
}
)
# 绘图：
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 6))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 6))
}
})
# colorbar:
top_anno <- HeatmapAnnotation(Module = colnames(data),
col = list(Module = col_anno),
border = T,
# 去除图例：
show_legend = F)
# 绘图：
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 6))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 6))
}
}
)
？Heatm
?Heatmap
# 绘图：
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 6))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 6))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)")
)
# colorbar:
top_anno <- HeatmapAnnotation(Module = colnames(data),
col = list(Module = col_anno),
border = T,show_annotation_name = F,
# 去除图例：
show_legend = F)
# 绘图：
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 6))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 6))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)")
)
# 绘图：
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 6))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 6))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)",
title_position = "left")
)
# 绘图：
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 6))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 6))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)",
title_position = "leftcenter")
)
# 绘图：
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 6))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 6))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)",
title_position = "rightcenter")
)
# 绘图：
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 6))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 6))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)",
title_position = "lefttop-rot")
)
# 绘图：
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 6))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 6))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)",
title_position = "leftcenter-rot")
)
# 绘图：
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 6))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 6))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)",
title_position = "rightcenter-rot")
)
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)"
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)")
# 绘图：
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 6))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 6))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)"))
# 绘图：
pdf("plots.pdf", height = 5, width = 10)
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 6))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 6))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)"))
dev.off()
# 绘图：
pdf("plots.pdf", height = 3, width = 10)
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 6))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 6))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)"))
dev.off()
# 绘图：
pdf("plots.pdf", height = 2, width = 10)
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 6))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 6))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)"))
dev.off()
# 绘图：
pdf("plots.pdf", height = 2, width = 10)
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 10))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 10))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)"))
dev.off()
# 绘图：
pdf("plots.pdf", height = 2, width = 10)
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 列聚类树高度：
column_dend_height = unit(10, "mm"),
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 10))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 10))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)"))
dev.off()
# 绘图：
pdf("plots.pdf", height = 3, width = 10)
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 列聚类树高度：
column_dend_height = unit(20, "mm"),
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 10))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 10))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)"))
dev.off()
# 绘图：
pdf("plots.pdf", height = 3, width = 10)
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 列聚类树高度：
column_dend_height = unit(30, "mm"),
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 10))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 10))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)"))
dev.off()
# 绘图：
pdf("plots.pdf", height = 3, width = 10)
Heatmap(data,
# 行不聚类：
cluster_rows = F,
# 列聚类树高度：
column_dend_height = unit(35, "mm"),
# 设置颜色：
col = col_fun,
# 调整热图格子的边框颜色和粗细：
rect_gp = gpar(col = "black", lwd = 1),
# 行名放左边：
row_names_side = "left",
# 列名放上边：
column_names_side = "top",
# colorbar:
top_annotation = top_anno,
# 加星号：
cell_fun = function(j, i, x, y, width, height, fill) {
if (p_mat[i,j]) {
grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 10))
} else {
grid.text(sprintf(""), x, y, gp = gpar(fontsize = 10))
}
},
# 修改图例标题：
heatmap_legend_param = list(title = "Correlation\n(BiCor)"))
dev.off()
