{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[90mggtree v3.10.1 For help: https://yulab-smu.top/treedata-book/\n", "\n", "If you use the ggtree package suite in published research, please cite\n", "the appropriate paper(s):\n", "\n", "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>.\n", "ggtree: an R package for visualization and annotation of phylogenetic\n", "trees with their covariates and other associated data. Methods in\n", "Ecology and Evolution. 2017, 8(1):28-36. doi:10.1111/2041-210X.12628\n", "\n", "Guangchuang Yu. Using ggtree to visualize data on tree-like structures.\n", "Current Protocols in Bioinformatics. 2020, 69:e96. doi:10.1002/cpbi.96\n", "\n", "G Yu. Data Integration, Manipulation and Visualization of Phylogenetic\n", "Trees (1st ed.). Chapman and Hall/CRC. 2022. ISBN: 9781032233574 \u001b[39m\n", "\n"]}], "source": ["# rm(list = ls())\n", "library(ggtree)\n", "library(readxl)\n", "\n", "data <- read_excel(\"K:/2020-2023HCC/579hcc/模型代码总结2/R-code/R代码修改版汇总/热图/data.xlsx\")\n", "\n", "data <- as.matrix(data[, -1]) # 去掉第一列\n", "# data <- as.matrix(data) # 去掉第一列"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[22mCoordinate system already present. Adding new coordinate system, which will\n", "replace the existing one.\n"]}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0gAAANICAMAAADKOT/pAAACYVBMVEUAAABNTU1oaGh8fHyMjIyampqnp6eysrK9vb3Hx8fQ0NDVKB/WKiDWLCLXLiPXMCTYMiXYNCfZNijZOCnZ2dnaOivaOyzbPS3cQC/cQTDdQzLdRDPeRjTeRzXfSTffSzjgTDngTTrgTzzhUD3h4eHiUz/iVEDjVUHjV0PkWETkWkXkW0blXEjlXknmX0rmYEznYk3nZE/oZVDoZlHpaFPpaVTpalXp6enqa1bqbVjrblnrb1rrcVzscl3sdF/tdWDtdmLteGPueWTuemXve2fvfWjvfmnwf2vwgGzwgW3w8PDxg2/xhXHxhnLyh3PyiHXyiXbzi3fzi3jzjHnzjXr0jnv0kH30kX71k4D1lIH1lIL1lYP1loP1loT2l4b2mYf2moj2mon3m4r3nIv3nYz3no33n473oI/4opH4opL4o5P4pJT5pZX5ppf5p5f5qJj5qZn5qpv6q5z6rJ76rZ/6rqD6r6D6sKP7sqT7s6X7s6b7tKf7taj7tqr7uKv8uaz8uq78u6/8vLH8vbL8v7T8wLX8wLb8wrf9wrj9w7j9xLr9xbv9xrz9xr39yL79yL/9ycD9ysH9y8P9zMT9zcT9zsb9z8f9z8j90Mn90cr90sr90sv90sz908z94d794t794t/94+D94+H95OH95OL95eL95eP95uP95uT95+T95+X95+b96Ob96Of96ef96ej96uj96un96+r+083+1M3+1M7+1c7+1c/+1s/+1tD+19L+2NL+2dP+2dT+2tT+2tX+29b+29f+3dj+3dn+3tn+3tr+39v+4Nz+4N3+4d3///+idjW+AAAACXBIWXMAABJ0AAASdAHeZh94AAAgAElEQVR4nO3did8tR5kX8EMYAgPuxn1Fx31fIu67RhM3rksUxwURBaIoi6ioTDSio96RgMMMioZr7lzmktwwzE0GhDGQcRgS6q/ydPfp093VVU89T9VTS3f/fp+Z3Pc97zl1zqk+X57qOtXdJ4MgSHJOtV8AguwhgIQgCgEkBFEIICGIQgAJQRQCSAiiEEBCEIUAEoIoBJAQRCGAhCAKASQEUQggIYhCAAlBFAJICKIQQEIQhQASgigEkBBEIYCEIAoBJARRCCAhiEIACUEUAkgIohBAQhCFABKCKASQEEQhgIQgCgEkBFEIICGIQgAJQRQCSAiiEEBCEIUAEoIoBJAQRCGAhCAKASQEUQggIYhCAAlBFAJICKIQQEIQhQASgigEkBBEIYCEIAoBJARRCCAhiEIACUEUAkgIohBAQhCFABKCKASQEEQhgIQgCgEkBFEIICGIQgAJQRQCSAiiEEBCEIUAEoIoBJAQRCGAhCAKASQEUQggIYhCAAlBFAJICKIQQEIQhQASgigEkBBEIYCEIAoBJARRCCAhiEIACUEUAkgIohBAQhCFABKCKASQEEQhgIQgCgEkBFEIICGIQgAJQRQCSAiiEEBCEIUAEoIoBJAQRCGAhCAKASQEUQggIYhCAAlBFAJICKIQQEIQhQASgigEkBBEIYCEIAoBJARRCCAhiEIACUEUAkgIohBAQhCFABKCKASQEEQhgIQgCgEkBFEIICGIQgAJQRQCSAiiEEBCEIUAEoIoBJAQRCGAhCAKASQEUQggIYhCAAlBFAJICKIQQEIQhQASgigEkBBEIYCEIAoBJARRCCAhiEIACUEUAkgIohBAQhCFABKCKASQEEQhgIQgCgEkBFEIICGIQgAJQRQCSAiiEEBCEIUAEoIoBJAQRCGAhCAKASQEUQggIYhCAAlBFAJItXLqEriL47fwo5AawUaplNPsv/R9xl9O15uw0doLtkmlTJAuNebUUZl+PC3/2t12fRg2WnvBNqmU0+KH01Bx+v+/FJ/T+H+L6gVIjQbbpFYW+zoXMhMgQNpYsE0qZhy1nU5+SNOcBCC1HGyTqrH0eCrSdF9AajXYJpVyrTIzNYC03WCbVMoCkj20w2TD5oJtUivXnZ/uH7sirae/8YVs28FGaTDYKNsLtlmDwUbZXrDNGgw2yvaCbYYgCgEkBFEIICGIQgAJQRQCSLUiPLAP3x+1HWycSpmtVgjcZ/oRG6vdYNtUygSJdWAfIDUebJtKER7YB0iNB9umVmQH9o1LW5FGg21TMZID+y5LW5FGg21TNZID+7CxWg62TaXID+xT21gv6jSDzANIlSI8sO86c6eQF18EJfUAUq0ID+zT+z72xT5KjSGXAFKDybtRXnwRlPQDSA0m60Z5cUrOpzlaAKnBlIIESXoBpIPlxWVqv5zd5EiQ7tZ+AS3kRTu1X9BOchxId+8CksMRKOnkKJDu9qn9KuapczySCxIoKeQYkO7ebQ5SneOR3I5AKT1HgHR3Su2XMqXO8UheSKCUmANAutswJDMRKXE8EuEIktKye0h3l6n9cqYILzSmso9EQgKllOwd0t1mIRnR8UgqFSngCJISsm9INqPWJPGPR8q/jwRKSdk1JIejZiAJj0fSgHTr1q0wpbSnOG52DMnFqB1JEccjKUACpVzZLySPo1YgRRyPpOGIQyntaQ6avULyMWpIkj95Nsqta1CU9LNTSISjo0K6NQuKknp2CYlitAVJ2SGBknr2CCnE6Iu1X2CN3LITolT7BW8s+4MUZPRFQGJRqv2Kt5XdQQo4+mKf2i+yQhyQgrMOtV/zlrIzSIxydExITkcoSnrZFyRWOWpDkuy4vhPj7mQ8kEKUEp7xYNkTJGY5agLSafZf+j7+3wXxOgIlpewIEp9RA5ImSKzj+qaHxIWCFKCU8i4PlN1AEjFqBdL4w3hM3+n6o304kslWkAZKKEqJ2QskGaMWJMmO60vcUmFJKEpp2Qkk0pGDUX1IRnRcn0nbUl9HUcqdfUCSlqNGJPGP6zNJW+rr55igJRSllOwBkrwc1YckPK4vsSANAaWM2QGkOEeVJQmP60suSFxKkBSZ7UOKGdbVhyQ9rk8HUpgSJEVm85Aiy1F9Sf4ob5OvL5PwnZLu69pXtg4p3pExb6n94j3JCymlKOm+sF1l45Bih3Udo7ccA5LtiFGUMLyTZ9uQYstRz6hdSapxQAoVJQzv5NkypNhh3cjoEJBcjpKKUu031Gg2DCl2WHdldAhJHkjBogRJsmwXUnI5OgQkn6MgJVxLSZbNQop19JZlqr1+4QX7TORxfQSkICVIEmSrkOKGdcZ2VE2S9MC+k4naVqSjECVIEmSjkHTKUROQ+Bfsi0lIEjnrgOEdP9uEFOXIUY7qSRIe2Be7mV5CUSqULUKKHNY5GdUrSaID+05x+0gvncOgBEnp2SAkzXJUe77h8k/wwL7I8d1LL7EoUUXJJ8nc1+yK7Wd7kOIceRlVnQJnH9gXdyTFS2PClKSSzP37kDTP5iDFOCLKUS1JwgP7EiGFKQnPfNc5gqR5tgYpylGA0YcqvA/hgX1RkF5aJEhJUJQGR5A0y8YgRUwz0OXozOhDNSCVuGDfSy+JKPEljY5Aacq2IOmXow/1qf2+llHaJrajICVqeLeAdP8+JNnZFCS5I0Y5OhKkM6XIomTcBQmSxmwJUoQjTjlqTpLONnE6ChYlxpTD0hEkDdkQJGVHZuaoMUkq8UAKFKXw8M52BEl9tgNJ7CgwrPvQh3YNyesooSi5HUFSl81Akjvil6M9SiIgBSiRwzuXI0gy24F01y3JP+0tKUc7hEQ6CozvqHlwpyNA2gykzoza7tG6HFWQJDywT3rFvhCkOEnmZUjyZBuQBjVKu0dORqUlxRzYJ8gTjJpEUPJIMi+/DEmebALS6EZj98hdjipCynJg3xNPcCgRklyUOkeQ5MkWIE1yFBz5GBWWJD2wTzaye6JPkJJseDc4giR3NgBpMbcQdhQ1rKtQkoQH9hnJtnriCR4lyfBudARJzrQPyRrOhabr4oZ1teYbLv8ED+wzl5t4eWJKQlEyHkeQ5ErzkFYTDHmGdT2jt5Z+c+wD+673ZuWJuaT4orSQ9PLLkESldUiOKe88js6M3loQUs4D+55YJkiJI8m8zJCk2kMbS+OQ1o46STGOAsO6jlFJSREH9kUVJA4l4rzGHkeQtErbkFyO4uoRoxwVhZTxwL6VIwYlryTjdgRJdjYIydz3SIp1dGVUVJI/idvEBSlEiS5KDkcvv4zdpEWahuRxdN8tiTrhFmNUtxdIbkdhSn5JTkeYcFimZUiegd19tyTCEbccNSIpE6TAXLhfktMRJC3TMCTCkUNSrKO3vrU5SEkhHAWKkkfSeSQNSeG0C4l0tJLkdUQO6+xytANJJCS6KLmvN9v1NSQF0yykgCNLkt+RqBxtH1LAkbgoXc4EKZJUuw+qZLuQ5pKiHLnK0fYlMSgJJE1ngoQkOq1CCjuaJHmXqdLDOjejUpCkV+zjbaqnn346TIkvaT4AgCQyjULiOBolqZajUpKkB/Zd1juE8vTTHErU8G4hadHZkESlTUg8R4OkOEd+RqUhsQ7s628Mt/r0JSlzDsZZkCApkA1Bcp4IKsoRVY4KSRIe2LdaCe7O09doSFoNACDJnyYhsR2dt1eMoxCj7y/wHmUH9vEgPT1LqChRZ5A0TkcySTn6rOW0CEni6EseSX5HgXJ0ZvT9JSAZ0YF9vNXfTz8tokQXJecAAJJ8aRCSyNGX3JIIR2FGxSSxD+zjHY70tJ3QOgdKkqfDMbjzpD1IQkdOSZGORkYFIMkO7DudGLPla0ghSpQkZ4dDkjfbgEQ5ckiKdfT9U3K/S+GBfbN/vHE4Cs06+K9ZcVNBUq6+azLNQeI7ukKyJRFnrmOVozIlSXZgn4mFFFWUzM2bkCRLa5DEA7u1JL8jNqNye0muxG0Tj6NgUfI4Eko6/OCuMUhxjhaSYhytGO0KEl2UHNeavXkTkqTZACSGo0kScUZiCaOqkqK2CeEoSMntSEOSdte0m7YgRTsaJcVMMzgZVS1JMaEh0eO7paSbNyFJnqYg8Qd2K0eDpAhH7nK0QUkhSlRRmg/vzE2GJAzu7DQPieuokxQxXedltDFI9+7dC1PiFKWFI68k7CbZaQlSmqOzJLVhXQFJERcao+577x6DElmUnI4UJCn01RbSEKSEHaTB0dc9kiKGddkhCY9HCq0RundJkFJgeLdyBEnMbBGSz9HXnZKihnUFIfEvNMaAFKREFyWHI5GkAw/u2oGk4MglKXJY1zHK+RGQHo+0eMwq9+YJUSIkuRxBEivNQEreQfq6U1KsI2MyfwKExyMZch/p3jKRksxjIkkY3M3TMqQIR7ak6GmGEh8BtQuN3bNDFyXP8M489hgkRacVSNyCFHLUXUAhdZrBmFKfAO7xSNO9nVlBiilKnSMFSUcd3DUCSWUHaVWU4qYZTIlPgOx4JHqyweEoWJQ8jh57zA3JLQklaUq7kKIdXSVFOTKmyCdAeDySHFKgKNnDu9GRryRBUiBtQNJ1NEryQSIdlfoE6F1ozONIVJSujvySXNsDkq5pAlLaDpLz1O9xjozjWct3h3CbeCEFKM0lPfZYUBJKEplWIQmW2DkcdZJiHLXxCZBtE8JRYHxnXAUJkuLSAiTtgd0gSezIVY6qfAI0IZFFyTgdQVJMGoCUx9GPeq7ZIitH7X8A7qdQMk5HmSTV7qm82Q4koaMfdUmSDus28AnoX1+IEilp7Shd0vFKUn1IuRw5JMmHdc1/AMZXGC/J5cgnyd1LGNyZBiBlmGi4OFpJihjWNf8BmF5irCSnI5EkDO7MdiDFOLIkxTrq90PUo3Ng3/xlRkkyn4UkldSGlGFgNzk6SzJhR+Sw7v5lL0Q7Sgf2LV8pKclJyXz2s7kkHWxw1x4kTUezouR3FGSU4wOgc2Cf/WqlRalzJJSUtnxVvR+bSWVI+gM7y9EoKcrRfBdEOToH9q1fsEjS4AiSNNIcJG1Hg6Q4Rzk/ANID+4xjWzk/qHxJoyMNSbzdJPVebCd1IakP7ByOOkkxjuwdEP0ID+wLF6ThlbIlffazmSVZdzT9t3s7zWYh8R2dN17ENMNq/yNHJAf2MR3RRWlxmdnPMiQpDu4uX+7tNFUhlXLkkcQd1uWAJDywLzhlt3ytnKK0cOSVpFaSzPXLvX2mJqQSO0i9I3dNYg/rckgSHtgnchQoSk5HmSWZ2Xd7+8yeIPkduSQJhnUZIAkP7HNd+pKARBalfni3cpRH0vjtwWKD7DIVIZV0tJYkdVRwyom3TUhHoaLkcCSSJCpJZrllMvdepbQOSWEHySmJ2MHw78QXCnebhCgRklyOskgyNqO9SqoHKb4gRTlaSiIcEZ/MQmFtkxs3boQoeSWdP93lJDm2yR7TOCR2QQo7mkuKctTW14k3boQp+c7U1f1JIom9m7SCZMwrB5FUDVKRgZ297i7kKPjBbCY3LomYcxgekSyJUZLOjF5xQdqjpO1Binc0SvI7CjB6pFZnrXPjBo+S43TG419yS+oZuSXV7r0MqQWpiqNBUpyjM6NH2oF0Y57A63Y7yiRpgnRhdJSS1AyklJkGtqNOUqSjR/rodYDwwD7rtxtLSfyitFiI65aksptkro4OUpIqQXp+RUm9IPmmH2IcmYsjPUjCA/suyx3G3LDDlrT8Q67B3ZzRQUpSLUjPW5SYBSnd0T33Z45cvzoyUpQkPLDPnAwJiS5KxlmQMkkyFqODlKQ6kJ7vM6NUztHqwzQ8F/UxfOSRXJDGHzgH9tGQ6KJk3I4ySXrFzhFKUk1IM0oJkKSOXJKY5UhVkvTAvtndXY7oouR74xJJvN0ks6pHxyhJVSA9P2WgpF2QSEfrDxT1CXzkkTyQjPTAviAksij5SnGqJOexe0eUVBvSQIkHKXVg59tVoNbd2Y50p8DjDuzzOQpI8vxRc3B3WWkPSGXyvBX3yQfUB3bzyavFLLD/w7dmpAVJfGAfCxI1vPOuxVUrSdOxeweU1AKksyTHbov2wG75xeTse0nvJ8/pSEmS9MC+2ZYiHBFFyXgn/pUkzY/dA6QSWTv63Oc+Z1NSL0j2Wpnr9vc6cjNSK0myK/YxK5JXUr/vkkWS89i940kqD2nlqIdkUcru6Lp2U+wo/0KhwDZ5d4CS8zj5oRMkkkQlyT52D5Dyx+NoSSkBEtPR5WiCCEeVIb37nBAlt6Puo5tBkvPYPY6k3P1YNMUh+QrSglKBgtRLinKUXVIYUoiSPU6edUQOSY7+PlxJqg9p5mikxFxkl+jI88XKff80w8jo4dKdNsu7x9Czd4v3s+gKPiSnpBUkYz4VK6liN6qnNCSyII2USgzsOkd3PJ+qAKOHm4BEU5oP4qz9F9WSdGb0KRckh6R9l6TqkGxHHaVSju44JdG7Rw/3KdxrU969CKso2X2iKKln5JZ0tJJUGFK4IPU1aUUpA6TOkUsSOV338MNNQaKLkttRsqQJ0oVRfEmq1o36aRDScIxFGUdrSZSjh6eU7bZr3r1KaHjn7CY2JEqSuTpCSSoNiVeQhj8YOaQIR7YkvyMzd9QOpEBRcneJwuBuzgglqUVIs2MsSjhaSiIcPbxM0X4b43AUKEquLlEY3C0ZoSRVh+QtSAtKCZDCjuaS+I5aguSXZB7PJOlTdg5fkopCEozs5pTUC9LS0SSJWF5nO6oiyePIO7wzjz+eQ5JZ1aP4klShF/OkOUj2nc6UeN/FxjsaJfkdrRm1BcldlDpHMkkcSMa8973vjZW035JUEpK8IA13Uh7YrR0NkmSOKkgiHLkkDY60JXWMAGmd1iC57mRWlLQddZJ8jlzDugYhrYZ3o6N0ScYqR5DkSFVIvIL0wgsv2JQcm8P5UeFDuuN15GFUZXAXoLSA9PjjIUns3SRjMwKkVQpCii1IL7xgU9IvSHfMx9ySCEdVJu7YkszjQUnSkjQx0pRUoxczpHlIg6MFpSyOPuaS5B3W1ZNEUppdpeLxx5UlmYUjlCQr5SAlFaQFJR4kqSOHpAYZdWEUJctRqiRjM+JKOs4MeOuQZo5GSgkFiXS0kkQ6KtZvroQkrRwlS7IZoSRZqQhJWpBGSryZhghHlqR2HRmK0nl453AkkbSCZMzHYyUdpiQVg6RRkC6UOJAkM98fmzI7Nra1aQYrVFFyOXr8cTYkS9KZ0cejIR1muqEepKiCdHZ0+7ZNKa0gzR3NilJT096u+B096ZYUN7jrGSVIOsrYbouQbErMgsRyNEpq3pHxrgV/8slkSROkCyNNSPssSaUgqTpaUtJ1NEjyOyrUX6z4HKlJMldHKEmBbBXSjFIKJIejTtI2HBkHpcGRSJJ3cDdnBEiBNA2JcnSlpF2QOkkbYWTW53B48kkdScZilFdS7V5USC1IyQXpSsmx5VIdvdLKem9GPI7SJX3cDkoSlUKQchQke19JDsnn6BW3pDJdJc0c0pNPhiQxd5PMqh7FSwIkxYQhxRSkQdLqvG3pjlySynRURNwFSSRpdd297iqKKEmSVIKkVZBum1dffdW+FELaTMO4rVt2ZG01t6NoScOFfWNLEiBlTLaRXQ9pSUmlIK0kFekmblYbze3ILSkEabpAdr6SBEiR0RnZeR0tKHEhBRwtJRXpJXbWG83t6MknxSXpygglSZY6kJQL0pySmqO5pApdRsT1AtyO+IM7Mx/VpZUkQMqW/AVpopQEydrixl2PKktyP73TkXBwZxaOUJIkaRaStCBdKGk6GiVV6TN/PE+fKMnYjFCSRKkCSe1LpFdXcUBizzS41lc694+qSvI+eaokmxGzJLHOurr/6YY2ICkVpLOjV9c1Kb4g9ZKq9Zo7xFOnSDLmx1iSUJI8KfGRKDay6yC9alNKcnTe4NW6LeKZYyGdGf0YICWlBiTGyC7BkU2JC0niqB6kwBNHSeoZaUo64oGyTUBSLUgWpSyOqkkKPm2EpAuj6iWpRP/lS4EPRK6RHeFoRikJUt2Oi3pSqSRzdRQtaUfTDafozdompNSCNFHK5aiKJNZTiiDNGRUuSYAkTbFdpPUNxgVJYWBXqOfinpEvackob0naxtiuZUh1RnZXSixIEY4qSOI+IVvSj9kpOt1QuSQ9enqg//eB03eZh77jdHrdg2aANGDq//voG06nNzzKaq5JSEoFqbvtzoqSzsCuTN/FPR1vN8ms6lHhklR7bPcdp7ed//u2s6c3n/o8uIL0uu7mB1itlYekNLLjFaRejsnjqLAkwZMxIBnzgQ98QK0kbRLSmzs55sHTm89F6TuN+a4R0QTpjd09Hjy9idNaA5AYI7uIqYYJ0pISd2DHeWslIYmeKyipY+SCxJIUN93QHCTzwOvO/3ld9x/ztje/8fVrSA8MP30Hp7HsnwWVXaTIkd3k50pJ09G2JBmrHLklHakkven0kHno9MbzT68fxnY2pNNpvD2c4pCKj+wWlDQHdl2KSlIa3F0ZZS1JG4D06OkN54Hbo8a84fTAm978tq1DKlGQrpR0C5LZ1G6SmY/qKpSk5iCdAb2tH7cNM3QLSG+bhna85P4gZBrZiQvShZK2o81JMgtHeiVpk5AeOlebh0xH5yHz6HUf6XWn77z89mA32fCdp9dz2moQktbIzvUV0pqS89gJSVqSNP+zC5LFKGtJihnbZe07Rx4Y5rYfPM33kfrf3tj99Gg//X36Lk5TpSHF7CJpFaQ7/Zoho1qQSn+bRD7b4o+ukmQzYpak3Y7t3tTNe5tujHd6/UPXYd2Drzu98TLA6//Aaqo6pIIjuzvDOtYFpeSCVFyS9+nsv6wYmY+wJMWVpE1CUkzmT0GekV2SowUlBUetrHBY32wz+khlSEFJmTsuazYASbkgLSipQGpCkuNGm5GmJPWxXfeCX345b8flTGFI5UZ2pKOREu2I3zeFJxzWT+ce8E2OPvIRZUiqY7vutb48JHPf5csWIaUXpJGS67xB885h905BSSfH0/meflmOEiRlgvSKmSMCJG/q7SKFHXWUggM7bveUg3RyPJ//2S1GZcd2wZ2k7hW+vEzGnsub9iHlGdldatJ6sGH3D7ODSkk6Wf8GnttipFiSUneSulf33HO2I0Byp9rIjueoH57TkNg9VEbSafVD4Jk/YqeFsV33wp7rs4K0WUlHh2RRcnYRr48qSQq8uhWkSElqixu6F/XclN2UpKybv9DILmqqYXS0oJTSSYUGd5ak4LNmK0kRkLrX89wygMRJNUiCgrSglNRLNSSFnzOuJOmM7YyF6Fvfsh3VhPQWKuLW6kKqObKzrpdpCEfM4V15SZxnLDe28+4kdS/jW33CkMpJ+mlUxK0VhZRnF4l1AAVZkEZKqR1VeDeJ93yckpRxJ6l7Cd+aEhzalYP006mIW2sMUrmRneuEUak9Vbok8VJtbNc/+7eWaWhs9zOoiFvLue2b3kUSQ+IM75qUFFWSUiF1T/yJT9iMmoL0M6mIW6sKqdguEmNkx1l7vBdJucZ2Zo6oS9OQfhYVcWtFv0fKAiluF0lekFoa3Mmeh1GS1CbAu+f7xJSVpIYg/Wwq4tZybvkf/uHhX/7IbgVJZ2RXrCBtRVKesV33VJ9YpmVIP4eKuLW8kIYMv1XcRSpWkNj3Sk52SMKxXfc073mP7ajpsd3PpSJuLeN2/+Fl+tvahaTVVS2WJJ2xnRdS9xTv6bMpSD+Piri1cpDOlH7gB/o/ZJ5ryDSya+2IiiRJOjtJZ0ld6++ZEhzaNQTp51MRt1YYUp/hz0q7SFGQ8hUkyR3TogsppiR1Lb9nmXBJagfSL6Aibq0gpNHRnFOluYaMBWkbJSkZUtfoO99pO9oUpF9IRdxaPUhnSqtpPY1dJMa3sVkLUpOSVHeSugbf2ScCUlhSru6y84uoiFurDMma1ssCqXBBkt45IQmSYifAu7beOUU+tGsH0i+mIm4t3zb37iKtHM05FZlryA2pwd0khbFd3847l8kBqZSkX0JF3FpDkM73OPdql8y7SNkdNVOSZssD08Z2XQv379uMtr2T9EupiFsrB8k/sptDGrq2T0u7SNJuKiIpdOWexZ/jx3bdo+/32RekX0ZF3FpLkKwu7ltpYxdJ3kv5JZ3oI2RXyqLGdt0D709ZSdKYbagF6ZdTEbfWMKRzF//UTw1tbWxkF/mQiPZ9z+KoVeKS1D3o/jLhkrQhSL+Ciri11iENGVqsBimmk/JCukJxPo1zyCeC1D3gG9+wHe0L0q+kIm6tHiT/LtLK0ZxThpFdnk7KKGkGZf0s3j0nLqTuvt/oEwFpQztJb6Uibi3b5l4zSYZ0pjRsZpMJkucjGNlH2br25P2FPIyXU5K6+31jinxotyFIv4qKuLWGIPlHdnNI1//R7Dipj+ycn8PYPsrVt35I5DReCFJ3n28ss2tIv5qKuLVikFJ2kWxHc07pkKwOWX0Yo7uofEkin9EPafj7e99rO2KM7TYM6ddQEbe2ZUhnSj/0Q8Ozqe4iWZTiu6hCSaIe5pA0/OF6dN6hIP1aKuLWNg9pyPCcanMNc0opPVRckgBSf5N9zoV0SBGHJFWC9OuoiFtrGJJ/F2nlaM4pbWS37pakHsrUvamQut/e9z7HyUtCJSliJ6lZSL+eiri1XUE6U3rhhf7ZAcmROaIuB4f0G6iIW6sGKWL22z+ym0Malrv2if4WSctRa5K6/7xvyvrEdFV2kiqdtvg3UhG31g6klF0k29GcU11ImTo4DtIcESD9Jiri1nYN6Uzpa1/rXgwBKdQryR2UXxIb0vvahFRpbPebqUx3O/WZ/+Jsbf+QunQvqMYukkoD4Va5koKQNKbtNgPpt1Cx7ksubQz8JS3rPSB9SP5dJBvShKn0yE6niUCjgBST30pledfT6odVSkHK8TWSDNKIyZSGlF9SvrFdli+SGoH026gs7xouSA1Dipj9XjmyIZ0pnYtSl2IjOxhtxMkAACAASURBVLVGqDa5LzjDTtJ2IfX57asMNy/uNBUk7y7SliDF7yItIQ37Sn12CCltbHckSL+DyuKey5Fd2cmGRiFZk+GeF08d7hOVDL3sgUSfx6HKtF2r38j+TiqLe3LWXh4bkpvSvCBtR9I4TQtIzPwuKvM7Wj3aFqQsCxsyQDL+UbEo+v28hHQ6OW+30ugXSXUg/W4q8ztaA5TGIa0HzvUg2Z9KBUyZIXGfqc357zpLGx6mMr/jElJjkw1VZr/5FekiZzHIS+uqNiS1CalOSfo9VOZ3PE3/tDdrVweSYw2rr08cVTytMKn3tAKkLF8kbQXS76Uibm1PkMJfI7EKkhdSf0s8Ju2urgMpPP+9FUi/j4q4tUyQQo5ahnSa/uvZr4zUpNzXgJSW309F3BogebqEgDT8PYJTGUn4RpaXP0BF3Np2IEVM2uWEdLmvSBMgtQTpD1IRtwZI7i7hObo8gI1Jt7cjxnZVvkhqFNIfoiJubdeQEr6PlUDq78rDpNrdgJSUP0xF3BogubtECqm/O8OSZn8HIZ2uuf6tBqSVpDYg/REq4tYAyd0lMZBYDyggac1nCiBd80epiFsDJHeXZIOk2eMRTYUgFflGtg1If4yKuDVAcvfIQSDVWdrQBqQ/TkXcGiC5eyQfJMUuB6SU/Akq4taODcnfIxkh6fU5IKXkT1IRtwZIQzecrBmuKEjc+2t1eglI4bMWbxXSn6Iibg2Q/P2SqSABUhuQ/jQVcWuA5O+XbJC0eh2QUvIIFXFrgOTvlnyQlLodkFLyZ6iIWwMkf7egIu0a0p+lIm4NkPzdAki7hvQYFXFrgOTvFkw27BrSn6Mibg2Q/N3SJKT5KroMkFaO9gvpz1MRtwZI/m6R902BL2QvX3PFHKDbBZCu+QtUxK0Bkr9bGoYU3ZAY0n5XNvxFKuLWMkGynQCS8E6sRwNSSv4SFXFrgER0S4OLVk/eX2QBJPN2KuLWjg2pxmyDXkGKaep7L7FvV4S0ctQopL9MZXY/+xqy7ta2AynidFw7hxTT0vdaMTeHrO6oCKnRczb8FSqz+52sH93dDkjuZNtJSuzwXJDGmC9dsnrk7iD9VSqz+wESIK3DhnQFdQVkZ+uQ/hqV6W6rHj8gpMhzf1/7JQOk1P6uB+kKyidra5D6PL7KcPN0j8Uu0vU/q9SCtJJUHhL5+nNN2ykWpJS5Bp8jCaTrDZ8asnqyxiH9dSrT3eZ4AGkOaXyJr53jfwN5SpJmQWoN0hjz3y9ZPXtbkP4GFeu+x4JE7yRdX9prs/jfwMlEXVts45DCjgSQrqC+esnq5VSF9DepWPdtBlJ4J0kOiTnbML6iHz9ngSgAKfI6SOSjknu7/i5SCqQrqP9zyer1lYT0t6hMd2traFcB0vhKfnyWFSS/pNiO2TukoCMJpCuo/3lJav9I8repTHc7mamrDwVpfAX9Rv5xK5KSFPfGqYepOnK3FqijrUMqCervUJndb5y1O81+XmdDkOidpOszLzZyeUjE49L7OlyQTvSYtAaklaNGIP1dKuLW2oXEnG0Yn/AL56w38r4gMaYahv/t9GICpCl/j4q4ta1Cuj7RF2ZpApL3gboFyTOym35yYSox+x2GFHZUBNK7qIhb2xqksf2fPGeByCkpBZJ6SdItSCFI/W+2JkCa5e9TEbfWDKTATtLY7k/OEobUUkkqUZBct84tiUd2lSAldxUn/4CKuLVqkJizDWN733fOQpFTUhuQOMUiudkorYA0yz+kIm6tUUjXdr5vloKQuv8ZT+gaV2GIb83VgoqjLHMNm4FkH9i7XMIuTSlIzJ2k8eHPnrNAVA7SZcfipFySWoSUZRdpM5Ds01csz2UhTRuQrg97dpYISBGzDQtJ851z3ZIka8t1b84yuxYgbeX7WPMEFXFruSDxZhvGO3/7nAWibJCIkmTNcCVBsh8rdRQqaYCUnn9ERdxaeUjzO317ljCklSRdSOue0ZMkamm+usvdgv3X+dXR/JHvIu35+1jzj6mIWysCafGH25csFLkkhSHlnbZLK0nR60vdKGiXp2FQql6QwpAidpFagfRPqIhbywpp/LlncttKEFKh2QaiJKV1zsn5I/tRBJ21o+Gf/JA05hpWkOo4Mv+Uiri1bJC6LJgUgZQ622B1TaKkxep75mOcPwYLEud5giO7QpAaKUjmA1TErWWDtGLSCKRyYzszlQrpAy6/cKqT4ToKF6RjzTWYD1IRt7Z3SGk7STqS2I2sxmZuiLkgZZlraBfSh6iIW8s3tAtBiphtWEHKvZOksNCU34hrF8cFUcnR4SF9mIq4tWKQIkpSoWm7nGM7E54CoO/mmAqvCMlxBoftQvoIFXFrLUNqYCeplCT/fVaYIx3F7CKVmbRbQQp3l0r+ORVxa7uHVHts17cTOCUR+dfQGgm1glRorqEZSP+Cirg1QMo+trs05cMUQsb8XVqQ2oFUaWRn/iUVcWuAlH9sNzW3vPIr5zqwIUdFR3a7mmsw/4qKuLWMX8imQ1JZ29BKSZo1yr+UMr3kzuQtSDuH9K+piFurCOmAJUmaoCM2pDxfx254yaox/4aKuLVykFoe2xUuSdz4vlha/xp8kR81S0trRyqQNOYaSkH6GBVxa0eEtJWS5HzixYjQs4ZonY92MTNL1UZ2zcw1mO+hMrvfarfW2VrbkFaSYtY2JI3tKpYk3/M617UG9rg+ekn3c/TIbl+7SOZJKtPdFotL/L28MUi5ZhsaLEmMbeZZ4rrOR+c5/55rZLcpSP+WynS3w0KKGtsRb6NOSWJ9R2vvMHkf81Er55taGdnVg/TvqFj3da8edtwjR2wm7e4kGfMTP0H0UOSVkhLC+3ZpfSff42xIHzX/zKZ0PEh9nlpluNm62wip/NUourS7k7SQ1DEiIJUvSAy43kXlzhsdjs4xc0sMR4XmGopB+vdUlnddDAGKTzY0vJM0gzQwakgSd7W4b4M6bnZDWliKKUh5dpGKQfoPVJZ3PXl/Cdyqk4Z3kkZJV0atQOIPI/3rxe2/eB0NljKO7JqG9B+pLHuU+I2+VSdySDE7SQklacaoDUmCvTFyMmL5RxLSUJaqjewq7iL9JyqL7lz+VB9SntmGOEhnSUtGFKRCkiSTGqHZiNnfV44sSIOl40H6z1Tmfbn8sYHJBhVIUWM7x1dJNiOPpPC8p1JkV1FnTEdcfwo7Okv6nE1J46zfTc81mP9CZdaR41TdyRAHw7QGqVBJMubu2pED0vWki5khyRTJXg6jIJ0dnWPmllQKUtuQVv8TvfiUSZP1E9IOpIWkM6NzwpJOs0qesZ+kioQvhleQhpirpUyQGpprMP+Viri1XUIiS9LAiAHp5PpHPRHf9qY5oiD1lkqO7GpCepqKuLWikDLtJElK0sgoKGl1dsZMPZXZEX9kN6Nkio3saq5r+CQVcWutQco7tpsxckKaJK2XWLcCSd+RBWmwFHK0+V2kdUVdvBlp8u5FF4LElLRgxNpLWvRRG5KEd5cXpEHSCxalmJFd27tI5r9REbe2QUiR83Y2o0BJcvRRnr7KN19nYgvS2dE5Zm5ph5A+TUXcWmVIuXaSbEnnz8TKkbgk5eksUaPSV5ACqbeUd2RXden3D1IRt5b5CxJ5SYpZbhcqScPnYS2JW5KyStL9HnaZREdTWcpUkKpC+gwVcWvNQVIf25nxf1Z5JYkc3OXoLsHyunRHYkhDWWIUpM2N7Mz/oCJuba+QRkmLcf62S5L8uSMdWZCsvaWskMRvMSH/i4q4tdqQosZ27JK0/AAklKSckphNajiKKUi9pDv2JF6Mo8YgvURF3Fru1ZjVIJ0lrb8MiYeUURL3QD5pYh2tIZk755hFcd/+LpL5ChVxa4UhZRvbrSSZ9XAkqSTlk8RqUMVRfEEaYqZZvO3vIq0L5uK1SrMFSBElqdviri9lWZAKS4qsSKGHKRekqyU3pO2N7MzXqIhbqw4pw9jOeKZsE0vSddasiiT7TqGzhOk5WkAaytIeRnbm/1IRt7ZRSISkaUinXZLIQ40TElGS5kd4OBM9sAtCGixtf2RnvkFF3Fr2Qz/Lju0MuayFXZIoSbVmG2TraHM6Okv6sEVJZ2RXFtIrVMSt1YekNbY7S7LnF9QHd+ERVUzEkMLfD/McxUIyl+t+b3oXyfw/KuLWtgGJdeoGx7eG+iWp5toGx2yH56Gfkh0YKy9IQ8zV0gZ3kcxPURG31iKkmJLkXMeyEUnsFtfnj3A/tP9oRzniF6SrJTek9kd25ltUxK3lPz1OiZ0k3zQdD9KmJFkL7lwPvRSJXAO7BaShLG1xF8lxSRLeVRXcaQBS6tjOXFelxpYk2W5SrWk749xDWz/2ut9i8hekq6UNQnK8/VlHSNMkJElJoqfpMpSkDBN3ggXg4Zvmc2kFCtIg6RWL0gYcmf9NRdxaeUiqYztrVapmSTLucfLJMb5KDXu2wXlH68bFrLQROYosSB82lwnjbUFa/Q/64jMpTX5IGUsS5xQdPEgrSf2+qKe/Ql+GipO4/ntxu/1FqfbAzgtpYWkLkD5PRdzadiEZ16rU6JJkSTKeuZtp3kyz43hrhFj3Wq/dUS5Irl2kxReZ0csaSkNyfQqmj8MU+2LM7tYqQOKM7YKSjHn1VRYbx53CgzvjmwUdlxTo7ihxmuI93cpRP7wrVJCmsrSFgmSepzLdbb4sjFgi1gQkcUnqGDEhRUiaf8Xg6y1VSeGWmDtlDkcdpbwF6cOrxTZmdYwC49vY4pC+SGW6WzuQosZ21FFJAyOuJC6kqyTj/WbO+ipUb8oh1BDziTyOfpB1Kjs9R2dJNy1KDRYk86NUprs1DCmtJF0ZaZekiyT7G2/fu9LcUaLb4YL1OepOLhV2pAipvwigmVtqERIzO4U0Y5RSkryDO8fKEd/b6iXp9B/ZCvcp/I46SjoFKTDVMIfUW2p4ZMdNQ5CUxnZnSUtGKSXJI8m5AMv3tk5qlAIVidUG6WgpqURBGimZjRekpiHFlSRjM1IvSXc9Cxm9byx4fB0zCpDcjiZIs+FdvCNZQbqWpS0XpKYgaZSkbnn3ypFySTKvSCUpje8CDTDaDzqaFSXewE6jIF3LUhBSag/my8Yg0SXJXJZ355XUfSbkkjSKUjIkjqNRUtmC1Et6waK0oYLUNiRZSZpOAhULiSVp+Ez4DlPxvzn5pSsdbST9meloGN4xHekVpJvjtS22Cem6muE0+9lxrzIvJh6SWSzvVi1JS0njR8IDiTrUq/bYjuuoL0rxA7v4gnRp8mppQyM7bipB4kqyz5SbrSRN58LwHjlJdmNiP6Z8I+th5HR0lsRzlPJlrMfRYAmQ0hIDyTjOwpBJ0mLdZYykREoJkISOPs06DEnR0c1l6+dXvK2RHTPtQLIlua8mwoIklmQtu4ySlEQp+FDvHaSOuovRFS1IN9dnbjWoSNGRju28Z2HQLUmDJPvz4D8vBt2X8ZRiIfkYeRz94HC9R1PV0bn1mxal2G5rKKUgicZ2xn8WhhwlyTHzFCPpRE3qhBInKc7RUpLLUV5I41I8QIoJvyQFzsIQXZJ8klyn3IyRdDnoLw5TFKSnJPN1M0eL4V2NgnT509WSvL+aS3OQwmdhiC5JnhsdjihJPkqLoyjFmBiHJNk3PPWUD1LQ0awoMQuSw1FSQbpaAiRhGJDOkjgnHdYd3BmfJGlRWhysdHYkoyTfDk895ZPku173AtJFUu6BXQDSUJbE773BFIPEOeKceRYGzZLU3eI5nbqsKNkdKSxK0u3w1CWOkzTwHA3DuwRHGgXpamn7aQcS/6TD8SXJvvEyN+iGREyDv8LrSMkQT7YhnvJC8jFaOeqLUvWCtBtJFSEtJPmvDRYryTPd7fpFKqlf3coNc49JtCGeesonSeLoLKmFgrQLRwUhUSVJeNJhpZI0/SyTdLm36M0HLQk2xFNPeSEJHd3PPGUHSDniLUmzXSPmuUtUJC1M+SQ5KE2r8kTRgvSUFZPg6L5DkmZBOs4uUgOQjLtIkDetIckHd8tBngeSqyjN7it5+zqQbEZzSd5pBrejTw+jaRN0hILESEFITknh+W7tknT/8vmxbmRLWp0OkZeU9d3XOBhNkLyMKEf3raLEdISCZKcqJMO7yB4Lkmxw5/Dql2T8jviU4pelXuNkdJUU62gpqfjADpAiYjPisWGWJMngztmkV9KiKLk+QpwkQ/IxGiT5h3VBR3NJKEixqQZJcpG9aEicOXCZJN8yiGDSIBGKekh+Rh5Hn17uIlZzBEgxWY/pcpck92OfkUsyJKSIy8kLQivqJaU5GqccXI6YkBxEAClf5sVIwEa3JJlnnvFJChYl0lqeMBiZTwrn62xH94eihIIUn/KQIi6yp1mSTOfIJyk0vCP+nMdSWNHZ0Sc/6YXEdtRJQkFKSFlI6+XdqiWJIWlgdI4bUmB4F4CkjOmDbEceSUbg6PzW4x3xCtKOpxqKQ4pkk1KSlisYRke+kkRJeoXhSA3TB7uEJZnBkVOSh5HP0fMmeu4bBak+pPwlab4q6JlnEiR1i765lJ5L6qUPXhKENDJySfI5ckPqL2C3kpS5IAFSdHhGOHcSSLo+4plnUiQNi755jvrEddEHp4SmvWeOVpAiHK0kxTs6WkEqDkmxJEkHd2bpiJDk+bLo8g9nR+m5a2S980Er5Newc0YrSVGOnl8O71yOUJDcaRJSlsGdzYiQ5CxK021hSjNIXE82oRAkm5ElSebo/uzKxAYDu5gUh5S7JPkkORzJJBnPzyxHXlRuP0FIDkZzSL7pOoajWVFSHdgBknZYRvQluRxRklYnjVz+RlIKQLpwChgiIDkZzSR5GXEcXYsSBnaSlIdUZXBn7kglWUXJsei7BCSXJB+jq6RUR4OkBEeAVCSKkLiS+g9GkiTnou8UR7GQCEaDJP+wju2oH94xB3YoSEMqQCou6TLol0uazS+4xXhuzgeJZtRB8jMSOOoooSCJsiVI7MHdUtJ1GkosaSpK/kXfmSEtJIUYdZK0HL2Dd3w5CtIlNSCVLUnz6Vy5JEM66imtd5/yQOIwMvdS5uvmjt7xDmNTYjoCpGKJVyOXtPiu3icpUJTCi77ljmSQul4LMvqkuXeO54BY33J3r6OOUtARCtKYKpAKSrLXvLglBYpSeC2Dme9PqUI6SzIcRH058kLyLuKgHFmSUJCo7BXSIGm9njlCErkg3GFJuSLxFF3KkUeSzNEEaT68gyMydSAVKknOIz59kvzfzZJHzlqWDNcRB1LX3vt5jszV0VqS960FHc2KEtcRIBVNghq+JOeRal5J/jWs973rWN2YdCCdG3p/H87O0YzRSpL/fyEYjkZJ3B2kozqqBUm3JHkkOR2JJV1u5Ut61bAsUZC6Hnr/mCAki5EFKdHRZXinO7ADJLXEq2EfUPGaiqTrbezxXf/kJojJB6l76PvnCX0HazNaSCJGrExHfVHCwC6UWpASvoJ1fiocjl5TkTS7iUVpeiXD+5RA6h/w/lUoSS5GM0nEsg2+o7MkXUeApJlYSDxJnaMYSXbjy985x/TZL+QaCtJwj7WhACQPo6skNUfxa4MOUpBagqQpaXD02mseSH5JVlFauQof0+faXbNIreMhREvyMxogEd8zexy5ITkPsUdBWqYeJF5JYg/u5pLM6MhbkriSHC8ytMzBC8l2ReMJQqIY9ZKoL5kljt7hWibFdXSUgtQUpLSSNEm6MkqW5KZNnrGL6ygRUoBRJ0nXkXWIcIIjQNKOtiSHozhJhn4eilIWSCtJDEbmWel8XXCiYb4MCgXJSluQNHaTlo6iJI1FiTpUyWOpBKQwo3vm2Wd9kEyso1lRgqNVakJK+grWJ8l2FCnJkI78lrJDMhxGnSOPJB8jjyNr5ttgYOdJY5AEAzm3pLWjOEnPuC+DZluKdySCdJXEUDSUI5+kNEfD8M7p6OAFqS4k9cHdfZejSEnkgvCJkikHyfAYXR2tJXmHdVxHfVFCQXKkLqSkxQyu276pJ8msLtjMsJQL0lkSS9GsHDkgeRkJHGFg584mIHElmW9+U09SeC/JYUkASbaTxGW0cGRJUnL0BaxpcKQyJE1JpnMkl0SeyIFXlO6Pu0sSR2xIXTfdYzmyGS0laTn6whfMisjhC9KmIS1vHRid44Tkl+QpSsb1HDQlow+p66ILEuGobgVJx1EP6Qt2UXI5OlZBqg5JrSRdHXlKEiWJPL8+uyj1lhR3krrWZkoiytFMkn+awefIX5DWklCQNgMpdOPkKELSuigtax2/Kg2YeJwoSF0blpOIcjRJIhhFOVpKgqMGIOkM7uaOoiQZAhKb0ryMmaAm71ETxjmzQErylKNRkrwchRzNd5QwsDMbgkRJMktHfkncouRa9C2CNGkiDqtwCTLe6TnqmAmCUQdJy9EC0lSUUJBMC5DSB3c2I78k7vDO+ZrClDyLxYd4IM06IjB2i2PUScrjaJQER10agJQ6uHM4ipNkKEcDCbmjFSgrtJ4wpDAj81XZMlW2o2F4xx3YAVL2cD+THkkuR1GSxqJEHclDYeFP7w13ZxvyS+Iw+qoPkneageuoL0ooSH1agJQkydxWl0SCoI7fJt2s754MKcjo2Y6RT5Jous7j6CyJ6QiQSoSLxnHT7dtiSYHhXciD93z0gcet7p8GyTAYXRy5JMmmvf2O7hz4PA3zNAuJJcl0jsSSAkWJManAL6JUK0JIC0lhRTNGDkjC3SMfpP5SiDalIxakRiBFDu4GRue4IUVK4ilwzJCzHji7fzQkw2D07IzRWpKio67L4KgVSHGrgkZHvpJESPIvYv0R7revq/LI8zPdPw6S4ShalKM1JF1HS0nHdNQypOCqoNu34yX5zx35Iz8SR0nqSA7pLMmwFNnlyJak5GiCNB/eHXEHybQDSTDfPf7x9u0MkjpHEkoz1xJE/QPEjpiKVuVoKck7zRDvaFaUDlqQ2oEkHNyZpaMoSQ5KF0d8SpOlvJD6HuI6cjCaIHkZJTkaJR3VUduQiFVBt1fxSpIUpQkSn1K/ZkHuSADp2jsxe0eWpFyOhuHdYR01BEkiyeHIL0kwvJs7klC6YMoBadGwbM7bIck/rPM6Ck00LIrSQXeQTFOQBJJcjuIkGcKRiNJ4KJIipHV7rAVBBCQ/Iw1H5w48bEHaJCRzW0/SoiitIfGnHa4/+N6JCJKvFfp4CZJRJym3I9d5UWp+ogqmJUhMSf2qoBySXI64lKzK4Xs3DEjkY6nj94KMzLvE83VCR47zotT8PJVMU5A4ksZVQYqSDA2JRcm1DND/nlyQqD4IQ+IwOkd0nhPBRMMV0upsDkdJ+5CscwW5ASVJuhQlryMOJcKL792ZOR/ucDCS0Vd7Rh5I/otrRDiyJJX/DFVKW5Akq4LEkgLDO8pRkBKLAfcNk23EMbo4ckryM4pytBjelf781EtjkCSrgqSSAsO7ACSakvxbpLjHrCGZMKOvXhm5JEnLUdDRrCjV/jQVTGuQSEnWqiBNSa8FHZGUakFiKJqVI5ckJUd3XOdFqf1ZKplNQBrPFWSLUZRkvmx4lBj7cVwTEY9ZSDIcRl9dMrIh5XF0Gd7V/iyVTHOQRKuC9CSZL3+ZR8lZluKKS8yDrpAMS5FdjixJ4mkGrqO+KNX+JBVNe5BEq4Lkkjzn2P9yHy6l1EOR4h90gcRTtC5HS0nEpXCTHXVF6UhpEJJoVZBYkrMoXRyxKdllqSCksyTDZuR0NEqiLikNR9JsBJK5l1XSBOlMiSVpWZZKViSuIi+jCyQ/IziKSIuQHKuCuq8uhZIEw7u5I3ZR6i2ZFBIxiMzXEqvRRRJRjlQcAVITcTgSS+IXpaUjCaULpthBmlTR1/qwpuooRp0kPyM4ikqbkKw9kHE1WiZJK0giSv45cUVIZlTEghRkdN7PgiPdNAppvv8xLe4US2IN7xyOhJTMCybGEv9g9kkRA1KI0bu6ib+M896HdNQspOn4nsUKaaEkVlFyQ5LsKw1nKzBSTIy7d41+zQ599F6Q0WUC3c1IxREgNRSXI2VJhnQkmAufTkVlJJgCd+0aWyEKQAoyetd1ZYRLko8RHAXTLCTjcqQrqS9KhCMuJetEo9Orj4U0NOFEREpiMJqvMIIjxbQLybgc+SXFTDl0RYmGxKHkvkDQ9Cb4kMYH+QlRkMKM3rVY8mpD8g7rfI6wgzRLw5CM+2Bs7xdHMZK+GXLUU6ItOSHZoIz3ZOGze4QAUZA4jOyl47xyBEectAzJc1IDL5gISeaOYVCiyxIJyW1qHq4eUhKD0bvWxwQaOFJL05Dckvxg5JL6Q2MZkghKXEcOWTGGXJBYjBzH1hrGsE7oCJDaTGZJl5M1JFGqDonDyFGO5pIIRnDES+OQ1CS5r9h8/VRwJHl2lmpAmiQZHiPf2YdM5LAOjuxsElKEJCel+WVJWJRclipCMixFfkaDpIhhHRyt0joksSQBpeXpinmSektGxVEKpLMkw1NEMuog+RnBkSDNQ5JKIoqSRcm+LAmb0qIw1YFkmIoCjDpJcKSS9iFpSlrMOjiuS8KndLVUHlLXJ2/XqEYdI3Nf/DUsHLmyAUiZJDmvSyKh1FtKcBQF6fycb+8TOeNtOeq+EPaxgCNJtgBJV5IhIEkpdZYKQjKjIg4kxnUyL0srROUIjtzZBCRVSZei5P8fXNkI7xUTbUkEqeuGt88SPnYvyOi6wh6O0rMNSPqS/J8UUVm6fA9jojDxIZkloqAkzlWb58dOsod1cOTLRiDJJQWGdyQkflmaL7IRY+JB6hpeKSIhsS5+vrg0AbccwZE3W4HkvSpXZFGiHbHLkrWAuosipL49FyIKEo+RdVYMOErNZiD5LhQZ94XSXWL0IqHkOMz08nJTII1v2WeIgBTDaCkJjqKyHUiakszduyqUqJPDXcOFND2CBERJimQ0hwRHcdkQJK8k8XqhzpEKJer0cGFSJoaPH5KJZjRJoqYZ4IjKliB5LwMuLEoXRwqU2eVsrQAACc5JREFUWJBcpKZI+fggsRQt5xjWkuTlCJAu2RSkGEmuq8HcncKQRFASQrJdxSOyJBkuI+qig3CUkm1BipC0LkpzR7yi5JsNT3OkBompiGTUQZIP6+Domo1BipJkXTHzrhWOJLelFiAZHUadJHk5gqNrtgYpRtKyKK0g8YrSYMm0BOksiauIwcjcER5VDkfzbA5SnCRDOBJQsgpT7YrEZuSfY5iXI98yXjhiZHuQoiRNRckJiTu+WxamREdJkLoX8RnNcnTHIwmMeNkgpEhJhnIkKUpXTLUgdU/+mT4sSeHzJ08ngYGj2GwRUpykvih5HYkp9ZgqQDIjIiYkBqPZOWBWbxGOmNkkJL+kUFGiIIkpmc+bNEtSSd1b/8w84UNgw2fzX54ChlmO4MjONiFFFyXSkZCS+XyX7sUUgWQsRAxJHEb2KWDgKC5bhRQ3eXfLKFIaIKVgYkPqnmCtKACJc20Zx07R9Qc4kmSzkGKGd+bWrVtBSfyvlT4/z/Ci1CENzToR0ZBYjBzvdLyJYARHjmwXkleS/8Ivt7qEi9KZEmvd0OfX6V+YEqS+LS8hGhLrSmee741Cjipu8oazYUiEJPei71uXhCVxypLLkbA4+SANDQQM+SUZHiPvQgYM6yKyZUh+Sc6idIXEKUrhsuSHNOdEgrIhjQ9hEfJBYl52k1hZh2FdRDYNiZK0XvR9axaOpIClECQbFCMCQD5IyYzuYPcoKtuGJChKC0fMokRZ4joijUXIISWpMCLOYgxH3mwdErsoWZD4lDyW2oNkNBjdIc5iDEdENg+JkDQvSitHEkquqYe2IHUdwVEUYnSHOIsxHFHZPiRSkqEgyShZq2cUHKlAOksyTEQMRsRZjMGIzA4gUZLGouR2JKK0LEvNQDJsRUFGd4izGMNRIHuAFC5KXkdSStMCmiYgnV/Qd2tVo4WjNaTam7j17AMSXZQMCUlE6WqpAUjnl/LdXTSmGObDOqek2hu4+ewEEinpHu1ISKm3pOIoBZK5KOJBEjOyJdXevO1nL5DIefDzZ1+VUmep4qxd9+zfPUVy5B5nVLeGVHfDbiP7gUQsves++iFJwqrUfTxNqqaopQwLRAxJHEbOFgwcCbIjSN5zg1+KiCIlM31KTYImKaTuyWxFAUiskzL7HgtG/OwJkofSyCMs6UyJuQjP+rCaOE0CSP1zOBDRknjnNvcrhCN29gXJJWl+yvwwJebKcNdn1og1MSCNb8xniIBkeEdVeRn1ZzFGmNkZpLWkhQuOJE5Z8g6GuuhAurwfEhAFiXecb+Db3GqbcXvZG6QVJfuU+RxKobLE+vTFQ+ofzhLkk2SYh8uDkVr2B8kQjrhFKVCWmF+BjuFCGu8vMuSAxD+XORypZYeQDAmJW5SossRe3Sb7RMoBOSAZMKqSPUKaUfKcMp9NyW1JCMn/YY2n45HEPplYaKlr7S24uewTkqEcSSg5y5KWI21IBozqZaeQLpT8+zkSSnYrDUI6S5Jcmib0uhBxdgvJkI5ElOyy1CAkA0aVs19IZ0qB74NElGZtNQape6tf0RrUwVFk9gyJdUpViSWj60gBUveivtJH4/tXMIrPriFxlvsIKF0wNQPJjIiYkFgXp0DisnNI6pQGTPUhda/iK/NEHLoHRorZPSR9Subb3+7brQfJWIgYkngXp0CicwBIPEr86fBvX9K1XB5S96xrRQFIYJQ9h4DEOsiIS+kKKR2TcGVqHyciGhIYFcgxIHEpsQ5Y+raVrvnMkPq34CVEQwKjIjkKJO6hr+GzpNiOrpjknDiQ+oYDhvySWC+q3jbZUY4DiU0pcBI8J6Q5J8GpT4MDOaYhJyTuK6mwJXaYI0HiX8olEpK0Pq3PBzSFD8gJifX8YKSXY0HiX8klxRG7Ppl0PU5J5JOCUZYcDFIyJQEkG5QvaXrWkCRn1Ue0cjhIkuuL6UAiiKkZmiCBUZ0cEJLookhbgnSWBEa1ckhIsiu5mFyO1CsSGNXLQSFJLz/RPKTuLd3AFEO9HBaS+PITDUM6v7obffC9UbUcGJKMUm9J15EKpO593BiDZQzVcmhIQkr9DlNLkLrXc2MeHABbLQeHJK1KX/pS/6AGIHUv48YqOOljrRwekpFM4X3pku5RFSV1T79GFIBUt493H0DqIoV0wVQFkvEhIiHV7uHdB5CGyCFpFCYppO4J/Yq8kOp27TECSGMiII2YojlJDpLoQiLySarYpwcKIM0S4WjBSQyKAWlsOWzICalORx4wgLRILKQFKAVI0ytiAnJDqtSLRwwg2UmCNC9PckjTixDycUqq0nuHDSCtkwrJGu4RpIwWnzWk8t127ACSMwqOnKRc0eBjQSrTScgsgOSNHiS/MF1EF0m1O+6QASQy24NUu8eOGkAKZUOQanfVkQNIjORypAqpdicdPIDETNuQavcOAkj8NAqpdrcgXQBJlOYg1e4Q5BJAEqcZSLU7ApkFkKJSGVLtt4+sAkgJqQGp9ntG3AGk1JSDVPudIkQASSW5IdV+f0gogKQafUi13xHCCyBlSTqk2u8AkQWQ8iUCUu2XjMQGkIrGCan2i0IUAkgIohBAQhCFABKCKASQEEQhgIQgCgEkBFEIICGIQgAJQRQCSAiiEEBCEIUAEoIoBJAQRCGAhCAKASQEUQggIYhCAAlBFAJICKIQQEIQhQASgigEkBBEIYCEIAoBJARRCCAhiEIACUEUAkgIohBAQhCFABKCKASQEEQhgIQgCgEkBFEIICGIQgAJQRQCSAiiEEBCEIUAEoIoBJAQRCGAhCAKASQEUQggIYhCAAlBFAJICKIQQEIQhQASgigEkBBEIYCEIAoBJARRCCAhiEIACUEUAkgIohBAQhCFABKCKASQEEQhgIQgCgEkBFEIICGIQgAJQRQCSAiiEEBCEIUAEoIoBJAQRCGAhCAKASQEUQggIYhCAAlBFAJICKIQQEIQhQASgigEkBBEIYCEIAoBJARRCCAhiEIACUEUAkgIohBAQhCFABKCKASQEEQhgIQgCgEkBFEIICGIQgAJQRQCSAiiEEBCEIUAEoIoBJAQRCGAhCAKASQEUQggIYhCAAlBFAJICKIQQEIQhQASgigEkBBEIYCEIAoBJARRCCAhiEIACUEUAkgIohBAQhCFABKCKASQEEQhgIQgCgEkBFEIICGIQgAJQRQCSAiiEEBCEIUAEoIoBJAQRCGAhCAKASQEUQggIYhCAAlBFAJICKIQQEIQhQASgigEkBBEIYCEIAoBJARRCCAhiEIACUEUAkgIohBAQhCFABKCKASQEEQhgIQgCgEkBFEIICGIQgAJQRQCSAiiEEBCEIUAEoIoBJAQRCGAhCAKASQEUcj/B2AFD3rWe/MOAAAAAElFTkSuQmCC", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["df <- hclust(dist(data))\n", "\n", "p1 <- ggtree(df)\n", "\n", "gheatmap(p1,data)\n", "\n", "p2 <- ggtree(df,layout = \"circular\")\n", "p2\n", "\n", "# 设置开口方向：rotate_tree()\n", "p3 <- rotate_tree(p2,100)\n", "\n", "gheatmap(p3 + geom_tiplab(offset = 13),data,\n", "         # 设置热图的宽度：\n", "         width = 1.5,\n", "         # 设置单元格的颜色：\n", "         low = \"#FDEBEA\",\n", "         high = \"#D5281F\",\n", "         font.size = 3,\n", "         colnames_position = \"top\",\n", "         # 调整开口大小\n", "         colnames_offset_y = 1,\n", "         # 调节列名和顶部之间的距离：\n", "         hjust = 0\n", "         ) + \n", "  theme(legend.position = \"right\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 6 × 1</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>CellType</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;fct&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>Sample1</th><td>A</td></tr>\n", "\t<tr><th scope=row>Sample2</th><td>B</td></tr>\n", "\t<tr><th scope=row>Sample3</th><td>C</td></tr>\n", "\t<tr><th scope=row>Sample4</th><td>D</td></tr>\n", "\t<tr><th scope=row>Sample5</th><td>E</td></tr>\n", "\t<tr><th scope=row>Sample6</th><td>F</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 6 × 1\n", "\\begin{tabular}{r|l}\n", "  & CellType\\\\\n", "  & <fct>\\\\\n", "\\hline\n", "\tSample1 & A\\\\\n", "\tSample2 & B\\\\\n", "\tSample3 & C\\\\\n", "\tSample4 & D\\\\\n", "\tSample5 & E\\\\\n", "\tSample6 & F\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 6 × 1\n", "\n", "| <!--/--> | CellType &lt;fct&gt; |\n", "|---|---|\n", "| Sample1 | A |\n", "| Sample2 | B |\n", "| Sample3 | C |\n", "| Sample4 | D |\n", "| Sample5 | E |\n", "| Sample6 | F |\n", "\n"], "text/plain": ["        CellType\n", "Sample1 A       \n", "Sample2 B       \n", "Sample3 C       \n", "Sample4 D       \n", "Sample5 E       \n", "Sample6 F       "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 6 × 1</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>GeneClass</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;fct&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>Gene1</th><td>Path1</td></tr>\n", "\t<tr><th scope=row>Gene2</th><td>Path1</td></tr>\n", "\t<tr><th scope=row>Gene3</th><td>Path1</td></tr>\n", "\t<tr><th scope=row>Gene4</th><td>Path1</td></tr>\n", "\t<tr><th scope=row>Gene5</th><td>Path1</td></tr>\n", "\t<tr><th scope=row>Gene6</th><td>Path1</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 6 × 1\n", "\\begin{tabular}{r|l}\n", "  & GeneClass\\\\\n", "  & <fct>\\\\\n", "\\hline\n", "\tGene1 & Path1\\\\\n", "\tGene2 & Path1\\\\\n", "\tGene3 & Path1\\\\\n", "\tGene4 & Path1\\\\\n", "\tGene5 & Path1\\\\\n", "\tGene6 & Path1\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 6 × 1\n", "\n", "| <!--/--> | GeneClass &lt;fct&gt; |\n", "|---|---|\n", "| Gene1 | Path1 |\n", "| Gene2 | Path1 |\n", "| Gene3 | Path1 |\n", "| Gene4 | Path1 |\n", "| Gene5 | Path1 |\n", "| Gene6 | Path1 |\n", "\n"], "text/plain": ["      GeneClass\n", "Gene1 Path1    \n", "Gene2 Path1    \n", "Gene3 Path1    \n", "Gene4 Path1    \n", "Gene5 Path1    \n", "Gene6 Path1    "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong>$CellType</strong> = <style>\n", ".dl-inline {width: auto; margin:0; padding: 0}\n", ".dl-inline>dt, .dl-inline>dd {float: none; width: auto; display: inline-block}\n", ".dl-inline>dt::after {content: \":\\0020\"; padding-right: .5ex}\n", ".dl-inline>dt:not(:first-of-type) {padding-left: .5ex}\n", "</style><dl class=dl-inline><dt>A</dt><dd>'#65B99F'</dd><dt>B</dt><dd>'#F08961'</dd><dt>C</dt><dd>'#8A9BC3'</dd><dt>D</dt><dd>'#DA85B5'</dd><dt>E</dt><dd>'#A1CC56'</dd><dt>F</dt><dd>'#F5D239'</dd><dt>G</dt><dd>'#7CC07B'</dd><dt>H</dt><dd>'#BAABD0'</dd><dt>I</dt><dd>'#3766A4'</dd><dt>J</dt><dd>'#DF3078'</dd></dl>\n"], "text/latex": ["\\textbf{\\$CellType} = \\begin{description*}\n", "\\item[A] '\\#65B99F'\n", "\\item[B] '\\#F08961'\n", "\\item[C] '\\#8A9BC3'\n", "\\item[D] '\\#DA85B5'\n", "\\item[E] '\\#A1CC56'\n", "\\item[F] '\\#F5D239'\n", "\\item[G] '\\#7CC07B'\n", "\\item[H] '\\#BAABD0'\n", "\\item[I] '\\#3766A4'\n", "\\item[J] '\\#DF3078'\n", "\\end{description*}\n"], "text/markdown": ["**$CellType** = A\n", ":   '#65B99F'B\n", ":   '#F08961'C\n", ":   '#8A9BC3'D\n", ":   '#DA85B5'E\n", ":   '#A1CC56'F\n", ":   '#F5D239'G\n", ":   '#7CC07B'H\n", ":   '#BAABD0'I\n", ":   '#3766A4'J\n", ":   '#DF3078'\n", "\n"], "text/plain": ["$CellType\n", "        A         B         C         D         E         F         G         H \n", "\"#65B99F\" \"#F08961\" \"#8A9BC3\" \"#DA85B5\" \"#A1CC56\" \"#F5D239\" \"#7CC07B\" \"#BAABD0\" \n", "        I         J \n", "\"#3766A4\" \"#DF3078\" \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["rm(list = ls())\n", "library(pheatmap)\n", "\n", "data <- as.matrix(read.table(\"K:/2020-2023HCC/579hcc/模型代码总结2/R-code/R代码修改版汇总/热图/data.txt\",row.names = 1,header = T,sep = \"\\t\"))\n", "\n", "# 构建列注释信息\n", "annotation_col = data.frame(CellType = factor(c(\"A\",\"B\",\"C\",\"D\",\"E\",\n", "                                                \"F\",\"G\",\"H\",\"I\",\"J\"))\n", "                            #Sex = factor(rep(c(\"F\",\"M\"),5))\n", "                            )\n", "\n", "rownames(annotation_col) <- colnames(data)\n", "head(annotation_col)\n", "\n", "# 构建行注释信息\n", "annotation_row = data.frame(\n", "  GeneClass = factor(rep(c(\"Path1\", \"Path2\", \"Path3\"), c(6, 6, 6)))\n", ")\n", "rownames(annotation_row) = rownames(data)\n", "head(annotation_row)\n", "\n", "\n", "# 自定注释信息的颜色列表\n", "ann_colors = list(\n", "  # Sex = c(F = \"red\", M = \"#016D06\"),\n", "  CellType = c(A = \"#65B99F\", B = \"#F08961\", C = \"#8A9BC3\", D = \"#DA85B5\", E = \"#A1CC56\",\n", "               F = \"#F5D239\", G = \"#7CC07B\", H = \"#BAABD0\", I = \"#3766A4\", J = \"#DF3078\")\n", "  # GeneClass = c(Path1 = \"#7570B3\", Path2 = \"#E7298A\", Path3 = \"#66A61E\")\n", ")\n", "head(ann_colors)\n", "\n", "pheatmap(data,\n", "         # 去掉聚类树：\n", "         cluster_cols = FALSE,\n", "         cluster_rows = FALSE,\n", "         # 加color bar：列注释信息；\n", "         annotation_col = annotation_col,\n", "         # 行注释信息：\n", "         # annotation_row = annotation_row,\n", "         # color bar 颜色设定：\n", "         annotation_colors = ann_colors,\n", "         # 设置单元格颜色渐变；(100)表示分100段渐变；\n", "         color = colorRampPalette(c(\"#FDEBEA\",\"#D5281F\"))(100), \n", "         # 行、列标签的字体大小\n", "         fontsize_col = 8,\n", "         fontsize_row = 10,\n", "         # 是否显示行、列名\n", "         show_colnames = F,\n", "         # 设置每个单元格的宽度和高度\n", "         cellwidth = 30, \n", "         cellheight = 24,\n", "         # 行、列聚类树的高度：\n", "         # treeheight_row = 50, \n", "         # treeheight_col = 30,\n", "         # display_numbers = TRUE参数设定在每个热图格子中显示相应的数值，\n", "         # number_color参数设置数值字体的颜色\n", "         display_numbers = FALSE,number_color = \"black\",\n", "         # 设置标题：\n", "         main = \"Heatmap\")\n"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}