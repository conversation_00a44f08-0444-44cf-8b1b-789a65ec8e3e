############## 富集分析分组柱状图 ##################
library(dplyr)
ego <- read.table("GO_enrichment_stat.txt",sep = "\t",header = T)

# 先根据BP、CC和MF按照q值选出前10个GO出来用于画图：用by实现
ego_filter <- by(ego, ego$ONTOLOGY, function(x) arrange(x, x[,8])[1:10,])
ego <- rbind(ego_filter$BP,ego_filter$CC,ego_filter$MF)

# 将数据按照ONTOLOGY分组，并按gene数目排序：
bp <- ego[which(ego$ONTOLOGY %in% "BP"),]
newbp <- arrange(bp, bp[,10])

cc <- ego[which(ego$ONTOLOGY %in% "CC"),]
newcc <- arrange(cc, cc[,10])

mf <- ego[which(ego$ONTOLOGY %in% "MF"),]
newmf <- arrange(mf, mf[,10])

go_enrich_df <- rbind(newbp,newcc,newmf)

# 如果我们熟练掌握apply系列函数的话，上面的过程完全可以用by快速实现；
go_list <- by(ego, ego$ONTOLOGY, function(x) arrange(x, x[,10]))
go_enrich_df2 <- rbind(go_list$BP,go_list$CC,go_list$MF)

go_enrich_df$number <- factor(rev(1:nrow(go_enrich_df)))

shorten_names <- function(x, n_word=10, n_char=40){
   if (length(strsplit(x, " ")[[1]]) > n_word || (nchar(x)>n_char))
     {
           if (nchar(x) >n_char) x <- substr(x, 1, n_char)
           x <- paste(paste(strsplit(x, " ")[[1]][1:min(length(strsplit(x," ")[[1]]), n_word)],
                                                                                collapse=" "), "...", sep="")
           return(x)
         } 
   else
     {
           return(x)
         }
}

labels=(sapply(
   levels(go_enrich_df$Description)[as.numeric(go_enrich_df$Description)],
   shorten_names))

# 排序：
names(labels) = rev(1:nrow(go_enrich_df))
CPCOLS <- c("#f0d182", "#c4d78b", "#80bbd9")

library(ggplot2)

# 垂直版本：
ggplot(data=go_enrich_df, aes(x=number, y=Count, fill=ONTOLOGY)) +
  geom_bar(stat="identity", width=0.8) + coord_flip() + 
  scale_fill_manual(values = CPCOLS) + theme_test() + 
  scale_x_discrete(labels=labels) +
  xlab("GO term") + ylab("Num of Genes") + 
  theme(axis.text=element_text(face = "plain"))+
  theme(axis.text.y=element_text(color=rep(rev(CPCOLS),rep(10,3)))) + 
  theme(legend.title=element_blank())

ggsave("vetical_GO_plot.pdf", height = 10,width = 8)

# 水平版本：
ggplot(data=go_enrich_df, aes(x=number, y=Count, fill=ONTOLOGY)) +
  geom_bar(stat="identity", width=0.8) +
  scale_fill_manual(values = CPCOLS) + theme_test() + 
  scale_x_discrete(labels=labels) +
  xlab("GO term") + ylab("Num of Genes") + 
  theme(axis.text =element_text(face = "plain")) + 
  theme(axis.text.x=element_text(angle=70, hjust = 1, color=rep(rev(CPCOLS),rep(10,3)))) + 
  theme(legend.title=element_blank(),legend.position = "top")

ggsave("horizontal_GO_plot.pdf", height = 6.5,width = 10)

################# 同样也可以绘制分组气泡图 ##################3
# 垂直版本：
ggplot(data=go_enrich_df, aes(x=number, y=Count, fill=ONTOLOGY)) +
  geom_point(aes(size=-log10(qvalue)), shape=21, stat="identity") + coord_flip() + 
  scale_fill_manual(values = CPCOLS) + theme_test() + 
  scale_x_discrete(labels=labels) +
  xlab("GO term") + ylab("Num of Genes") + 
  theme(axis.text=element_text(face = "plain"))+
  theme(axis.text.y=element_text(color=rep(rev(CPCOLS),rep(10,3)))) + 
  theme(legend.title=element_blank())

ggsave("vetical_GO_buble_plot.pdf", height = 10,width = 8)

# 水平版本：
ggplot(data=go_enrich_df, aes(x=number, y=Count, fill=ONTOLOGY)) +
  geom_point(aes(size=-log10(qvalue)), shape=21, stat="identity") +
  scale_fill_manual(values = CPCOLS) + theme_test() + 
  scale_x_discrete(labels=labels) +
  xlab("GO term") + ylab("Num of Genes") + 
  theme(axis.text =element_text(face = "plain")) + 
  theme(axis.text.x=element_text(angle=70, hjust = 1, color=rep(rev(CPCOLS),rep(10,3)))) + 
  guides(size=guide_legend("-log10(qvalue)"),
                 fill=guide_legend("ONTOLOGY"))+
  theme(legend.text = element_text(vjust = 0.5))

ggsave("horizontal_GO_buble_plot.pdf", height = 6.5,width = 10)




