{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#火山图3个类型，运行成功\n", "# install.packages(\"devtools\") \n", "# devtools::install_github(\"BioSenior/ggvolcano\")\n", "library(ggVolcano) \n", "# data(deg_data)\n", "data <- read.csv(\"D:/R-code/promics_data.csv\")\n", "colnames(data)\n", "\n", "data2 <- add_regulate(data, log2FC_name = \"log2FC\",              \n", "                     fdr_name = \"pvalue\",log2FC = 1, fdr =  0.05) #fdr为p值；这行代码会自动把log2FC和pvalue转换成log2FoldChange和padj\n", "colnames(data2)\n", "# str(data2)\n", "# write.csv(data2, file = \"D:/R-code/data2_export.csv\", row.names = FALSE)\n", "\n", "#图1\n", "p1 <-ggvolcano(data2, x = \"log2FoldChange\", y = \"padj\",     #x和y的名字不需要改，\n", "          #x_lab = \"log2FC\",\n", "          y_lab = \"-log10(P-value)\",  \n", "          log2FC_cut = 1, #FC>2 或 FC<0.5 对应的logFC为1和-1\n", "          FDR_cut = 0.05, #p<0.05\n", "          label = \"Genename\", label_number = 20, output = FALSE)   #只需要改label\n", "p1 + coord_cartesian(xlim = c(-6, 6))\n", "\n", "#图2\n", "library(patchwork)\n", "p2 <- ggvolcano(data2, x = \"log2FoldChange\", y = \"padj\",\n", "                fills = c(\"#e94234\",\"#b4b4d8\",\"#269846\"),\n", "                colors = c(\"#e94234\",\"#b4b4d8\",\"#269846\"),\n", "                label = \"Genename\", label_number = 20, output = FALSE)\n", "p2 + coord_cartesian(xlim = c(-6, 6))\n", "\n", "#图3\n", "library(RColorBrewer)\n", "library(patchwork)\n", "p3 <- gradual_volcano(data2, x = \"log2FoldChange\", y = \"padj\",\n", "                      label = \"Genename\", label_number = 20, output = FALSE)\n", "p3 + coord_cartesian(xlim = c(-6, 6))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# 富集气泡图已成功\n", "library(ggplot2)\n", "\n", "# 读取富集气泡图数据文件\n", "# df= read.delim(\"https://www.bioladder.cn/shiny/zyp/bioladder2/demoData/bubble/data.txt\")# 这里读取了网络上的demo数据，将此处换成你自己电脑里的文件\n", "df <- read.csv(\"D:/R-code/kegg.csv\")\n", "print(colnames(df))\n", "top20_terms <- df[1:20, ]# 筛选出前20个term的数据\n", "\n", "# 绘图\n", "ggplot(top20_terms,aes(x = richFactor, \n", "              y = reorder(Term,-log10(P_value),sum), # 按照富集度大小排序\n", "              size = Protein_number,\n", "              colour=-log10(P_value))) +\n", "  geom_point(shape = 16) +                    # 设置点的形状\n", "  labs(x = \"Rich factor\", y = \"KEGG Pathway(Top20)\")+           # 设置x，y轴的名称\n", "  scale_colour_continuous(                    # 设置颜色图例\n", "    name=\"-log10(P value)\",                        # 图例名称\n", "    low=\"green\",                              # 设置颜色范围\n", "    high=\"red\")+\n", "  scale_radius(                               # 设置点大小图例\n", "    range=c(2,5),                             # 设置点大小的范围\n", "    name=\"Protein number\")+                             # 图例名称\n", "  guides(   \n", "    color = guide_colorbar(order = 1),        # 决定图例的位置顺序\n", "    size = guide_legend(order = 2)\n", "  )+\n", "  theme_bw()                                  # 设置主题"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}