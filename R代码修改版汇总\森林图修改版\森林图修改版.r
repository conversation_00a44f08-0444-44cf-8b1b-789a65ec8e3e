#%%R语言进行AIC或BIC逐步回归方法用于多变量cox回归
# 安装并加载所需的包
# install.packages("survival")
# install.packages("MASS")
library(survival)
library(MASS)
library(readxl)
library(openxlsx)
library(ggplot2)
library(pec)

# 从Excel文件中读取数据
data <- read_excel('k:\\734HCC\\all-HCC\\578hcc\\clinical data\\data\\train30预后最终版.xlsx')#522HCC预后完整版
#data <- read.csv('K:/2020-2023HCC/579hcc/clinical data/data/test预后最终版.csv')#522HCC预后完整版

# 获取变量名
variable_names <- colnames(data)
print(variable_names)

# 取第4列及其后的数据
data_subset <- data[, 4:ncol(data)]

# 执行多变量Cox回归分析:enter法
# cox_model_multivar <- coxph(Surv(PFS2, status2==1) ~ ap_score +bingliscore+deep_score, data=data_subset)

# # 打印多变量回归结果
# summary(cox_model_multivar)

# 执行AIC逐步回归方法
cox_model_stepwise <- coxph(Surv(PFS2, status2==1) ~sex+AST+GGT+AFP+diameter+Tumor_number+ap_score+Handcrafted_pathomics_score+Deep_pathomics_score, data=data_subset)  # 使用所有变量进行初始拟合

# 使用stepAIC函数进行AIC逐步回归
AIC_model <- stepAIC(cox_model_stepwise, direction="both", trace=TRUE)  #both,forward和backward  

# 打印最终模型的摘要
summary(AIC_model)

# 提取模型回归结果的变量名
variable_names <- names(coef(AIC_model))
print(variable_names)

# 提取AIC模型结果的系数、标准误差、p值和置信区间
coef <- coef(AIC_model)
se <- sqrt(diag(vcov(AIC_model)))
hr <- exp(coef)
# print(hr)
p_values <- summary(AIC_model)$coefficients[,5]
print(p_values);

# 将summary(AIC_model)的结果存储为一个数据框
summary_df <- summary(AIC_model)

# 提取"lower .95"和"upper .95"的值
lower_95 <- summary_df$conf.int[,"lower .95"]
upper_95 <- summary_df$conf.int[,"upper .95"]

# 打印结果
print(lower_95)
print(upper_95)

# 创建包含这些结果的数据框
model_summary <- data.frame(
   Variables = variable_names,
   Coefficients = coef,
  `se` = se,  
  `est` = hr,
  `low` = lower_95,
  `hi` = upper_95,
  `P_Values` = p_values
)

data_path = "h:\\1.HCC-dataset\\850HCC\\all-HCC\\578hcc\\clinical data\\data\\regression_forest-ERFS.csv"

# 保存数据框到CSV文件
write.csv(model_summary, file = data_path, row.names = FALSE)

#%%##### forestploter包绘制 ######
# install.packages("forestploter")
library(grid)
library(forestploter)

# 读取数据：
# dt <- read.csv(system.file("extdata", "example_data.csv", package = "forestploter")) #这个数据集是包自带的
# write.csv(dt, file = 'D:/R-code/example_data.csv', row.names = FALSE)# # 将数据集dt保存为CSV文件
# dt <- read.csv('D:/R-code/example_data2.csv') #自己的数据

data_path = "h:\\1.HCC-dataset\\850HCC\\all-HCC\\578hcc\\clinical data\\data\\regression_forest-ERFS.csv"

dt <- read.csv(data_path) #自己的数据
print(colnames(dt))

# 添加空白列，显示CI
dt$` ` <- paste(rep(" ", 20), collapse = " ")

# 创建要显示的置信区间列
dt$`Hazard ratio (95% CI)` <- ifelse(is.na(dt$se), "",
                           sprintf("%.2f (%.2f to %.2f)",
                                   dt$est, dt$low, dt$hi))
                             
dt$P_Values <- ifelse(dt$P_Values < 0.001, "<0.001", round(dt$P_Values, digits = 2))# 将p值小于0.001的结果显示为"<0.001"
dt$P_Values

dt <- dt[, c(names(dt)[!names(dt) %in% "P_Values"], "P_Values")]# 将p值放到表格最后一列
head(dt)

# 设置图的字体
par(family = "Arial", font = 5)

# 绘制简单的森林图
# pdf("forest_plot04.pdf", height = 7, width = 10)
forest(dt[,c(1, 7:9)],
       est = dt$est,
       lower = dt$low, 
       upper = dt$hi,
       sizes = dt$se,
       ci_column = 2,#置信区间的列索引
       ref_line = 1,
       arrow_lab = c("Low risk", "High risk"),
       xlim = c(0,4),
       ticks_at = c(0.5, 2, 4)
       )
      
#%% 更改主题背景
# 增加汇总列并修改图形参数
# dt <- rbind(dt[-1, ], dt[1, ])  # 实际上就是把第一行放到最后一行
# dt[nrow(dt), 1] <- "AFP"

# 定义主题：
tm <- forest_theme(base_size = 12,
                   # 置信区间点形状，线类型/颜色/宽度
                   ci_pch = 16,  # 点形状
                   ci_col = "red",
                   ci_lty = 1, # 线条类型
                   ci_lwd = 1.5, # 线条宽度
                   ci_Theight = 0.4, # 在CI的末尾设置T形尾部
                   # 参考线宽/类型/颜色
                   refline_lwd = 1,
                   refline_lty = "dashed",
                   refline_col = "grey20",
                   # 垂直的线宽/类型/颜色
                   vertline_lwd = 1,
                   vertline_lty = "dashed",
                   vertline_col = "grey20",
                   # 更改填充和边框的摘要颜色
                   summary_fill = "red",
                   summary_col = "red",
                   # 脚注字体大小/字形/颜色
                   footnote_cex = 0.8,
                   footnote_fontface = "italic",
                   footnote_col = "red")


# 绘图：
# pdf("h:\\1.HCC-dataset\\850HCC\\all-HCC\\578hcc\\clinical data\\forest-PFS.pdf", height = 7, width = 10)

par(family = "Arial", font = 5)

forest(dt[,c(1, 7:9)],
       est = dt$est,
       lower = dt$low, 
       upper = dt$hi,
       sizes = dt$se,
       # is_summary = c(rep(FALSE, nrow(dt_tmp)-1), TRUE),
       ci_column = 2,#置信区间的列索引
       ref_line = 1,
       arrow_lab = c("Low risk", "High risk"),
       xlim = c(0, 6),
       ticks_at = c(0, 1,3,5),
       theme = tm)
# dev.off()
