{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["##R语言进行AIC或BIC逐步回归方法用于多变量cox回归\n", "# 安装并加载所需的包\n", "# install.packages(\"survival\")\n", "# install.packages(\"MASS\")\n", "library(survival)\n", "library(MASS)\n", "library(readxl)\n", "library(openxlsx)\n", "library(ggplot2)\n", "library(pec)\n", "\n", "# 从Excel文件中读取数据\n", "data <- read_excel('k:\\\\734HCC\\\\all-HCC\\\\578hcc\\\\clinical data\\\\data\\\\train30预后最终版.xlsx')#522HCC预后完整版\n", "#data <- read.csv('K:/2020-2023HCC/579hcc/clinical data/data/test预后最终版.csv')#522HCC预后完整版\n", "\n", "# 获取变量名\n", "variable_names <- colnames(data)\n", "print(variable_names)\n", "\n", "# 取第4列及其后的数据\n", "data_subset <- data[, 4:ncol(data)]\n", "\n", "# 执行多变量Cox回归分析:enter法\n", "# cox_model_multivar <- coxph(Surv(PFS2, status2==1) ~ ap_score +bingliscore+deep_score, data=data_subset)\n", "\n", "# # 打印多变量回归结果\n", "# summary(cox_model_multivar)\n", "\n", "# 执行AIC逐步回归方法\n", "cox_model_stepwise <- coxph(Surv(PFS, status==1) ~sex+AST+GGT+AFP+diameter+Tumor_number+ap_score+Handcrafted_pathomics_score+Deep_pathomics_score, data=data_subset)  # 使用所有变量进行初始拟合\n", "\n", "# 使用stepAIC函数进行AIC逐步回归\n", "AIC_model <- stepAIC(cox_model_stepwise, direction=\"both\", trace=TRUE)  #both,forward和backward  \n", "\n", "# 打印最终模型的摘要\n", "summary(AIC_model)\n", "\n", "# 提取模型回归结果的变量名\n", "variable_names <- names(coef(AIC_model))\n", "print(variable_names)\n", "\n", "# 提取AIC模型结果的系数、标准误差、p值和置信区间\n", "coef <- coef(AIC_model)\n", "se <- sqrt(diag(vcov(AIC_model)))\n", "hr <- exp(coef)\n", "# print(hr)\n", "p_values <- summary(AIC_model)$coefficients[,5]\n", "print(p_values);\n", "\n", "# 将summary(AIC_model)的结果存储为一个数据框\n", "summary_df <- summary(AIC_model)\n", "\n", "# 提取\"lower .95\"和\"upper .95\"的值\n", "lower_95 <- summary_df$conf.int[,\"lower .95\"]\n", "upper_95 <- summary_df$conf.int[,\"upper .95\"]\n", "\n", "# 打印结果\n", "print(lower_95)\n", "print(upper_95)\n", "\n", "# 创建包含这些结果的数据框\n", "model_summary <- data.frame(\n", "   Variables = variable_names,\n", "   Coefficients = coef,\n", "  `se` = se,  \n", "  `est` = hr,\n", "  `low` = lower_95,\n", "  `hi` = upper_95,\n", "  `P_Values` = p_values\n", ")\n", "\n", "data_path = \"h:\\\\1.HCC-dataset\\\\850HCC\\\\all-HCC\\\\578hcc\\\\regression_summary2.csv\"\n", "\n", "# 保存数据框到CSV文件\n", "write.csv(model_summary, file = data_path, row.names = FALSE)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["################### forestploter包绘制 #################\n", "# install.packages(\"forestploter\")\n", "library(grid)\n", "library(forestploter)\n", "\n", "# 读取数据：\n", "# dt <- read.csv(system.file(\"extdata\", \"example_data.csv\", package = \"forestploter\")) #这个数据集是包自带的\n", "# write.csv(dt, file = 'D:/R-code/example_data.csv', row.names = FALSE)# # 将数据集dt保存为CSV文件\n", "# dt <- read.csv('D:/R-code/example_data2.csv') #自己的数据\n", "\n", "data_path = \"D:/R-code/regression_summary2.csv\"\n", "\n", "dt <- read.csv(data_path) #自己的数据\n", "print(colnames(dt))\n", "\n", "# 添加空白列，显示CI\n", "dt$` ` <- paste(rep(\" \", 20), collapse = \" \")\n", "\n", "# 创建要显示的置信区间列\n", "dt$`Hazard ratio (95% CI)` <- ifelse(is.na(dt$se), \"\",\n", "                           sprintf(\"%.3f (%.3f to %.3f)\",\n", "                                   dt$est, dt$low, dt$hi))\n", "                             \n", "dt$P_Values <- ifelse(dt$P_Values < 0.001, \"<0.001\", round(dt$P_Values, digits = 3))# 将p值小于0.001的结果显示为\"<0.001\"\n", "dt$P_Values\n", "\n", "dt <- dt[, c(names(dt)[!names(dt) %in% \"P_Values\"], \"P_Values\")]# 将p值放到表格最后一列\n", "head(dt)\n", "\n", "# 设置图的字体\n", "par(family = \"Arial\", font = 5)\n", "\n", "# 绘制简单的森林图\n", "# pdf(\"forest_plot04.pdf\", height = 7, width = 10)\n", "forest(dt[,c(1, 7:9)],\n", "       est = dt$est,\n", "       lower = dt$low, \n", "       upper = dt$hi,\n", "       sizes = dt$se,\n", "       ci_column = 2,#置信区间的列索引\n", "       ref_line = 1,\n", "       arrow_lab = c(\"Low risk\", \"High risk\"),\n", "       xlim = c(0,4),\n", "       ticks_at = c(0.5, 2, 4),        \n", "       )"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# 更改主题背景\n", "# 增加汇总列并修改图形参数\n", "# dt <- rbind(dt[-1, ], dt[1, ])  # 实际上就是把第一行放到最后一行\n", "# dt[nrow(dt), 1] <- \"AFP\"\n", "\n", "# 定义主题：\n", "tm <- forest_theme(base_size = 12,\n", "                   # 置信区间点形状，线类型/颜色/宽度\n", "                   ci_pch = 16,  # 点形状\n", "                   ci_col = \"red\",\n", "                   ci_lty = 1, # 线条类型\n", "                   ci_lwd = 1.5, # 线条宽度\n", "                   ci_Theight = 0.4, # 在CI的末尾设置T形尾部\n", "                   # 参考线宽/类型/颜色\n", "                   refline_lwd = 1,\n", "                   refline_lty = \"dashed\",\n", "                   refline_col = \"grey20\",\n", "                   # 垂直的线宽/类型/颜色\n", "                   vertline_lwd = 1,\n", "                   vertline_lty = \"dashed\",\n", "                   vertline_col = \"grey20\",\n", "                   # 更改填充和边框的摘要颜色\n", "                   summary_fill = \"red\",\n", "                   summary_col = \"red\",\n", "                   # 脚注字体大小/字形/颜色\n", "                   footnote_cex = 0.8,\n", "                   footnote_fontface = \"italic\",\n", "                   footnote_col = \"red\")\n", "\n", "\n", "# 绘图：\n", "pdf(\"D:/R-code/forest-PFS.pdf\", height = 7, width = 10)\n", "\n", "par(family = \"Arial\", font = 5)\n", "\n", "forest(dt[,c(1, 7:9)],\n", "       est = dt$est,\n", "       lower = dt$low, \n", "       upper = dt$hi,\n", "       sizes = dt$se,\n", "       # is_summary = c(rep(FALSE, nrow(dt_tmp)-1), TRUE),\n", "       ci_column = 2,#置信区间的列索引\n", "       ref_line = 1,\n", "       arrow_lab = c(\"Low risk\", \"High risk\"),\n", "       xlim = c(0, 6),\n", "       ticks_at = c(0, 1,3,5),\n", "       theme = tm)\n", "dev.off()\n"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}