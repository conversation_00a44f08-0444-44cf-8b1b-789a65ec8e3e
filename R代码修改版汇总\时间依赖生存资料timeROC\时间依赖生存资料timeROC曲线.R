#安装rtools
#install.packages("timeROC", repos = "https://cloud.r-project.org/")
# install.packages("mets")
# install.packages("ranger")
# install.packages("riskRegression")
# install.packages("timeROC")
#remove.packages("readxl")
#install.packages("tidyverse")
#install.packages("readxl")  安装报错最终通过安装下载的readxl_1.4.3.zip解决的
# install.packages("openxlsx")
# library(openxlsx)
# data <- read.xlsx('D:/路径/文件名.xlsx')


library(readxl)
library(pec)
library(timeROC)
library(survival)
library(MASS)

#new_dat<- read_excel('D:/dataset/train30.xlsx')

new_dat <- read_excel('K:/2020-2023HCC/579hcc/clinical data/data/validation30预后最终版.xlsx')
head(new_dat)
nrow(new_dat) 
length(new_dat$PFS)
length(new_dat$nomogram)


timeROC1 <-with(new_dat, timeROC(T=PFS,
                               delta=status,
                               marker=nomogram,
                               cause=1,
                               times=c(12,24,36,48),
                               iid = TRUE))
#identical(c(timeROC1$TP[,1],timeROC1$TP[,2],timeROC1$TP[,3]),as.numeric(timeROC1$TP))
# 计算AUC值及其置信区间
timeROC1$AUC
confint(timeROC1,level = 0.95)$CI_AUC

dat = data.frame(fpr = as.numeric(timeROC1$FP),
                 tpr = as.numeric(timeROC1$TP),
                 time = rep(as.factor(c(12,24,36,48)),each = nrow(timeROC1$TP)))

library(ggplot2)
ggplot() + 
  geom_line(data = dat,aes(x = fpr, y = tpr,color = time),size = 1) + 
  scale_color_manual(name = NULL,values = c("#92C5DE", "#F4A582", "#66C2A5","#E78AC3"),# "#8DA0CB"
                     labels = paste0("AUC of ",c(1,2,3,4),"-year survival: ",
                                     format(round(timeROC1$AUC,3),nsmall = 2)))+
  geom_line(aes(x=c(0,1),y=c(0,1)),color = "grey")+
  theme_bw()+
  theme(panel.grid = element_blank(),
        legend.background = element_rect(linetype = 1, size = 0.2, colour = "black"),
        legend.position = c(0.765,0.125))+
  scale_x_continuous(expand = c(0.005,0.005))+
  scale_y_continuous(expand = c(0.005,0.005))+
  labs(title = "Time-dependent ROC for training set", # Training set  External test set  internal test set
       x = "1 - Specificity",
       y = "Sensitivity")+
  coord_fixed()


#----早期复发的ROC
library(readxl)
library(pec)
library(timeROC)
library(survival)
library(MASS)

#new_dat<- read_excel('D:/dataset/train30.xlsx')

new_dat <- read_excel('K:/2020-2023HCC/579hcc/clinical data/data/train30预后最终版.xlsx')
head(new_dat)
nrow(new_dat) 
length(new_dat$PFS2)
length(new_dat$nomogram2)


timeROC1 <-with(new_dat, timeROC(T=PFS2,
                                 delta=status2,
                                 marker=nomogram2,
                                 cause=1,
                                 times=c(12,24),
                                 iid = TRUE))
#identical(c(timeROC1$TP[,1],timeROC1$TP[,2],timeROC1$TP[,3]),as.numeric(timeROC1$TP))
# 计算AUC值及其置信区间
timeROC1$AUC
confint(timeROC1,level = 0.95)$CI_AUC

dat = data.frame(fpr = as.numeric(timeROC1$FP),
                 tpr = as.numeric(timeROC1$TP),
                 time = rep(as.factor(c(12,24)),each = nrow(timeROC1$TP)))

library(ggplot2)
ggplot() + 
  geom_line(data = dat,aes(x = fpr, y = tpr,color = time),size = 1) + 
  scale_color_manual(name = NULL,values = c("#92C5DE", "#F4A582", "#66C2A5","#E78AC3"),# "#8DA0CB"
                     labels = paste0("AUC of ",c(1,2,3,4),"-year survival: ",
                                     format(round(timeROC1$AUC,3),nsmall = 2)))+
  geom_line(aes(x=c(0,1),y=c(0,1)),color = "grey")+
  theme_bw()+
  theme(panel.grid = element_blank(),
        legend.background = element_rect(linetype = 1, size = 0.2, colour = "black"),
        legend.position = c(0.765,0.125))+
  scale_x_continuous(expand = c(0.005,0.005))+
  scale_y_continuous(expand = c(0.005,0.005))+
  labs(title = "Time-dependent ROC for external test set", # Training set  external test set  internal test set
       x = "1 - Specificity",
       y = "Sensitivity")+
  coord_fixed()



#-------比较两个time-dependent AUC  已运行过

timeroc2 <- with(new_dat,timeROC(T = PFS,delta = status,marker = nomogram,
  cause = 1,weighting="marginal", times = c(12,24,36,48),ROC = TRUE,iid = TRUE))
  #times = c(12,24,36),ROC = TRUE,iid = TRUE)

timeroc2$AUC
confint(timeroc2,level = 0.95)$CI_AUC

compare(timeROC1,timeroc2,adjusted = TRUE)$p_values_AUC

plotAUCcurve(timeROC1, conf.int=TRUE, col="red")
plotAUCcurve(timeroc2, conf.int=TRUE, col="blue", add=TRUE)
legend("bottomright",c("VETC", "RFinter"), col = c("red","blue"), lty=1, lwd=2)

