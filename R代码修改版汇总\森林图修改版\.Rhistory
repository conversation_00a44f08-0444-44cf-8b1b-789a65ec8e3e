dt$Treatment <- ifelse(is.na(dt$Treatment), "", dt$Treatment)
dt$Placebo <- ifelse(is.na(dt$Placebo), "", dt$Placebo)
dt$se <- (log(dt$hi) - log(dt$est))/1.96
# 添加空白列，显示CI
dt$` ` <- paste(rep(" ", 20), collapse = " ")
# 创建要显示的置信区间列
dt$`HR (95% CI)` <- ifelse(is.na(dt$se), "",
sprintf("%.2f (%.2f to %.2f)",
dt$est, dt$low, dt$hi))
head(dt)
#>          Subgroup Treatment Placebo      est        low       hi        se
#> 1    All Patients       781     780 1.869694 0.13245636 3.606932 0.3352463
#> 2             Sex                         NA         NA       NA        NA
#> 3            Male       535     548 1.449472 0.06834426 2.830600 0.3414741
#> 4          Female       246     232 2.275120 0.50768005 4.042560 0.2932884
#> 5             Age                         NA         NA       NA        NA
#> 6          <65 yr       297     333 1.509242 0.67029394 2.348190 0.2255292
#>                                                   HR (95% CI)
#> 1                                         1.87 (0.13 to 3.61)
#> 2
#> 3                                         1.45 (0.07 to 2.83)
#> 4                                         2.28 (0.51 to 4.04)
#> 5
#> 6                                         1.51 (0.67 to 2.35)
# 绘制简单的森林图
pdf("forest_plot04.pdf", height = 7, width = 10)
forest(dt[,c(1:3, 20:21)],
est = dt$est,
lower = dt$low,
upper = dt$hi,
sizes = dt$se,
ci_column = 4,
ref_line = 1,
arrow_lab = c("Placebo Better", "Treatment Better"),
xlim = c(0, 4),
ticks_at = c(0.5, 1, 2, 3),
footnote = "This is the demo data. Please feel free to change\nanything you want.")
dev.off()
# 更改主题背景
# 增加汇总列并修改图形参数
dt_tmp <- rbind(dt[-1, ], dt[1, ])  # 实际上就是把第一行放到最后一行
dt_tmp[nrow(dt_tmp), 1] <- "Overall"
# 定义主题：
tm <- forest_theme(base_size = 10,
# 置信区间点形状，线类型/颜色/宽度
ci_pch = 16,  # 点形状
ci_col = "#324c63",
ci_lty = 1, # 线条类型
ci_lwd = 1.5, # 线条宽度
ci_Theight = 0.4, # 在CI的末尾设置T形尾部
# 参考线宽/类型/颜色
refline_lwd = 1,
refline_lty = "dashed",
refline_col = "grey20",
# 垂直的线宽/类型/颜色
vertline_lwd = 1,
vertline_lty = "dashed",
vertline_col = "grey20",
# 更改填充和边框的摘要颜色
summary_fill = "#324c63",
summary_col = "#324c63",
# 脚注字体大小/字形/颜色
footnote_cex = 0.6,
footnote_fontface = "italic",
footnote_col = "#324c63")
# 绘图：
pdf("forest_plot05.pdf", height = 7, width = 10)
forest(dt_tmp[,c(1:3, 20:21)],
est = dt_tmp$est,
lower = dt_tmp$low,
upper = dt_tmp$hi,
sizes = dt_tmp$se,
is_summary = c(rep(FALSE, nrow(dt_tmp)-1), TRUE),
ci_column = 4,
ref_line = 1,
arrow_lab = c("Placebo Better", "Treatment Better"),
xlim = c(0, 4),
ticks_at = c(0.5, 1, 2, 3),
footnote = "This is the demo data. Please feel free to change\nanything you want.",
theme = tm)
dev.off()
# 修改森林图：
p <- forest(dt_tmp[,c(1:3, 20:21)],
est = dt_tmp$est,
lower = dt_tmp$low,
upper = dt_tmp$hi,
sizes = dt_tmp$se,
is_summary = c(rep(FALSE, nrow(dt_tmp)-1), TRUE),
ci_column = 4,
ref_line = 1,
arrow_lab = c("Placebo Better", "Treatment Better"),
xlim = c(0, 4),
ticks_at = c(0.5, 1, 2, 3),
footnote = "This is the demo data. Please feel free to change\nanything you want.",
theme = tm)
# 改变第三行文字的颜色：
g <- edit_plot(p, row = 3, gp = gpar(col = "red", fontface = "italic"))
# 加粗分组变量的名称：
g <- edit_plot(g,
row = c(2, 5, 10, 13, 17, 20),
gp = gpar(fontface = "bold"))
# 改变第五行的背景色：
g <- edit_plot(g, row = 5, which = "background",
gp = gpar(fill = "#4ca9be"))
# 在表头插入文字
g <- insert_text(g,
text = "Treatment group",
col = 2:3,
part = "header",
gp = gpar(fontface = "bold"))
# 在标题的底部添加下划线
g <- add_underline(g, part = "header")
# 在表格内部插入文字：
g <- insert_text(g,
text = "This is a long text. Age and gender summarised above.\nBMI is next",
row = 10,
just = "left",
gp = gpar(cex = 0.6, col = "#324c63", fontface = "italic"))
pdf("forest_plot06.pdf", height = 7, width = 10)
plot(g)
dev.off()
################### forestploter包绘制 #################
library(grid)
library(forestploter)
# 读取数据：
dt <- read.csv(system.file("extdata", "example_data.csv", package = "forestploter"))
dt$Subgroup <- ifelse(is.na(dt$Placebo),
dt$Subgroup,
paste0("   ", dt$Subgroup))
# 替换缺失值：
dt$Treatment <- ifelse(is.na(dt$Treatment), "", dt$Treatment)
dt$Placebo <- ifelse(is.na(dt$Placebo), "", dt$Placebo)
dt$se <- (log(dt$hi) - log(dt$est))/1.96
# 添加空白列，显示CI
dt$` ` <- paste(rep(" ", 20), collapse = " ")
# 创建要显示的置信区间列
dt$`HR (95% CI)` <- ifelse(is.na(dt$se), "",
sprintf("%.2f (%.2f to %.2f)",
dt$est, dt$low, dt$hi))
head(dt)
#>          Subgroup Treatment Placebo      est        low       hi        se
#> 1    All Patients       781     780 1.869694 0.13245636 3.606932 0.3352463
#> 2             Sex                         NA         NA       NA        NA
#> 3            Male       535     548 1.449472 0.06834426 2.830600 0.3414741
#> 4          Female       246     232 2.275120 0.50768005 4.042560 0.2932884
#> 5             Age                         NA         NA       NA        NA
#> 6          <65 yr       297     333 1.509242 0.67029394 2.348190 0.2255292
#>                                                   HR (95% CI)
#> 1                                         1.87 (0.13 to 3.61)
#> 2
#> 3                                         1.45 (0.07 to 2.83)
#> 4                                         2.28 (0.51 to 4.04)
#> 5
#> 6                                         1.51 (0.67 to 2.35)
# 绘制简单的森林图
pdf("forest_plot04.pdf", height = 7, width = 10)
forest(dt[,c(1:3, 20:21)],
est = dt$est,
lower = dt$low,
upper = dt$hi,
sizes = dt$se,
ci_column = 4,
ref_line = 1,
arrow_lab = c("Placebo Better", "Treatment Better"),
xlim = c(0, 4),
ticks_at = c(0.5, 1, 2, 3),
footnote = "This is the demo data. Please feel free to change\nanything you want.")
dev.off()
# 更改主题背景
# 增加汇总列并修改图形参数
dt_tmp <- rbind(dt[-1, ], dt[1, ])  # 实际上就是把第一行放到最后一行
dt_tmp[nrow(dt_tmp), 1] <- "Overall"
# 定义主题：
tm <- forest_theme(base_size = 10,
# 置信区间点形状，线类型/颜色/宽度
ci_pch = 16,  # 点形状
ci_col = "red",
ci_lty = 1, # 线条类型
ci_lwd = 1.5, # 线条宽度
ci_Theight = 0.4, # 在CI的末尾设置T形尾部
# 参考线宽/类型/颜色
refline_lwd = 1,
refline_lty = "dashed",
refline_col = "grey20",
# 垂直的线宽/类型/颜色
vertline_lwd = 1,
vertline_lty = "dashed",
vertline_col = "grey20",
# 更改填充和边框的摘要颜色
summary_fill = "red",
summary_col = "red",
# 脚注字体大小/字形/颜色
footnote_cex = 0.6,
footnote_fontface = "italic",
footnote_col = "red")
# 绘图：
pdf("forest_plot05.pdf", height = 7, width = 10)
forest(dt_tmp[,c(1:3, 20:21)],
est = dt_tmp$est,
lower = dt_tmp$low,
upper = dt_tmp$hi,
sizes = dt_tmp$se,
is_summary = c(rep(FALSE, nrow(dt_tmp)-1), TRUE),
ci_column = 4,
ref_line = 1,
arrow_lab = c("Placebo Better", "Treatment Better"),
xlim = c(0, 4),
ticks_at = c(0.5, 1, 2, 3),
footnote = "This is the demo data. Please feel free to change\nanything you want.",
theme = tm)
dev.off()
# 修改森林图：
p <- forest(dt_tmp[,c(1:3, 20:21)],
est = dt_tmp$est,
lower = dt_tmp$low,
upper = dt_tmp$hi,
sizes = dt_tmp$se,
is_summary = c(rep(FALSE, nrow(dt_tmp)-1), TRUE),
ci_column = 4,
ref_line = 1,
arrow_lab = c("Placebo Better", "Treatment Better"),
xlim = c(0, 4),
ticks_at = c(0.5, 1, 2, 3),
footnote = "This is the demo data. Please feel free to change\nanything you want.",
theme = tm)
# 改变第三行文字的颜色：
g <- edit_plot(p, row = 3, gp = gpar(col = "red", fontface = "italic"))
# 加粗分组变量的名称：
g <- edit_plot(g,
row = c(2, 5, 10, 13, 17, 20),
gp = gpar(fontface = "bold"))
# 改变第五行的背景色：
g <- edit_plot(g, row = 5, which = "background",
gp = gpar(fill = "#4ca9be"))
# 在表头插入文字
g <- insert_text(g,
text = "Treatment group",
col = 2:3,
part = "header",
gp = gpar(fontface = "bold"))
# 在标题的底部添加下划线
g <- add_underline(g, part = "header")
# 在表格内部插入文字：
g <- insert_text(g,
text = "This is a long text. Age and gender summarised above.\nBMI is next",
row = 10,
just = "left",
gp = gpar(cex = 0.6, col = "red", fontface = "italic"))
pdf("forest_plot06.pdf", height = 7, width = 10)
plot(g)
dev.off()
################### forestploter包绘制 #################
library(grid)
library(forestploter)
# 读取数据：
dt <- read.csv(system.file("extdata", "example_data.csv", package = "forestploter"))
dt$Subgroup <- ifelse(is.na(dt$Placebo),
dt$Subgroup,
paste0("   ", dt$Subgroup))
# 替换缺失值：
dt$Treatment <- ifelse(is.na(dt$Treatment), "", dt$Treatment)
dt$Placebo <- ifelse(is.na(dt$Placebo), "", dt$Placebo)
dt$se <- (log(dt$hi) - log(dt$est))/1.96
# 添加空白列，显示CI
dt$` ` <- paste(rep(" ", 20), collapse = " ")
# 创建要显示的置信区间列
dt$`HR (95% CI)` <- ifelse(is.na(dt$se), "",
sprintf("%.2f (%.2f to %.2f)",
dt$est, dt$low, dt$hi))
head(dt)
#>          Subgroup Treatment Placebo      est        low       hi        se
#> 1    All Patients       781     780 1.869694 0.13245636 3.606932 0.3352463
#> 2             Sex                         NA         NA       NA        NA
#> 3            Male       535     548 1.449472 0.06834426 2.830600 0.3414741
#> 4          Female       246     232 2.275120 0.50768005 4.042560 0.2932884
#> 5             Age                         NA         NA       NA        NA
#> 6          <65 yr       297     333 1.509242 0.67029394 2.348190 0.2255292
#>                                                   HR (95% CI)
#> 1                                         1.87 (0.13 to 3.61)
#> 2
#> 3                                         1.45 (0.07 to 2.83)
#> 4                                         2.28 (0.51 to 4.04)
#> 5
#> 6                                         1.51 (0.67 to 2.35)
# 绘制简单的森林图
pdf("forest_plot04.pdf", height = 7, width = 10)
forest(dt[,c(1:3, 20:21)],
est = dt$est,
lower = dt$low,
upper = dt$hi,
sizes = dt$se,
ci_column = 4,
ref_line = 1,
arrow_lab = c("Placebo Better", "Treatment Better"),
xlim = c(0, 4),
ticks_at = c(0.5, 1, 2, 3),
footnote = "This is the demo data. Please feel free to change\nanything you want.")
dev.off()
# 更改主题背景
# 增加汇总列并修改图形参数
dt_tmp <- rbind(dt[-1, ], dt[1, ])  # 实际上就是把第一行放到最后一行
dt_tmp[nrow(dt_tmp), 1] <- "Overall"
# 定义主题：
tm <- forest_theme(base_size = 10,
# 置信区间点形状，线类型/颜色/宽度
ci_pch = 16,  # 点形状
ci_col = "#e8452a",
ci_lty = 1, # 线条类型
ci_lwd = 1.5, # 线条宽度
ci_Theight = 0.4, # 在CI的末尾设置T形尾部
# 参考线宽/类型/颜色
refline_lwd = 1,
refline_lty = "dashed",
refline_col = "grey20",
# 垂直的线宽/类型/颜色
vertline_lwd = 1,
vertline_lty = "dashed",
vertline_col = "grey20",
# 更改填充和边框的摘要颜色
summary_fill = "#e8452a",
summary_col = "#e8452a",
# 脚注字体大小/字形/颜色
footnote_cex = 0.6,
footnote_fontface = "italic",
footnote_col = "#e8452a")
# 绘图：
pdf("forest_plot05.pdf", height = 7, width = 10)
forest(dt_tmp[,c(1:3, 20:21)],
est = dt_tmp$est,
lower = dt_tmp$low,
upper = dt_tmp$hi,
sizes = dt_tmp$se,
is_summary = c(rep(FALSE, nrow(dt_tmp)-1), TRUE),
ci_column = 4,
ref_line = 1,
arrow_lab = c("Placebo Better", "Treatment Better"),
xlim = c(0, 4),
ticks_at = c(0.5, 1, 2, 3),
footnote = "This is the demo data. Please feel free to change\nanything you want.",
theme = tm)
dev.off()
# 修改森林图：
p <- forest(dt_tmp[,c(1:3, 20:21)],
est = dt_tmp$est,
lower = dt_tmp$low,
upper = dt_tmp$hi,
sizes = dt_tmp$se,
is_summary = c(rep(FALSE, nrow(dt_tmp)-1), TRUE),
ci_column = 4,
ref_line = 1,
arrow_lab = c("Placebo Better", "Treatment Better"),
xlim = c(0, 4),
ticks_at = c(0.5, 1, 2, 3),
footnote = "This is the demo data. Please feel free to change\nanything you want.",
theme = tm)
# 改变第三行文字的颜色：
g <- edit_plot(p, row = 3, gp = gpar(col = "#e8452a", fontface = "italic"))
# 加粗分组变量的名称：
g <- edit_plot(g,
row = c(2, 5, 10, 13, 17, 20),
gp = gpar(fontface = "bold"))
# 改变第五行的背景色：
g <- edit_plot(g, row = 5, which = "background",
gp = gpar(fill = "#4ca9be"))
# 在表头插入文字
g <- insert_text(g,
text = "Treatment group",
col = 2:3,
part = "header",
gp = gpar(fontface = "bold"))
# 在标题的底部添加下划线
g <- add_underline(g, part = "header")
# 在表格内部插入文字：
g <- insert_text(g,
text = "This is a long text. Age and gender summarised above.\nBMI is next",
row = 10,
just = "left",
gp = gpar(cex = 0.6, col = "#e8452a", fontface = "italic"))
pdf("forest_plot06.pdf", height = 7, width = 10)
plot(g)
dev.off()
################### forestploter包绘制 #################
library(grid)
library(forestploter)
# 读取数据：
dt <- read.csv(system.file("extdata", "example_data.csv", package = "forestploter"))
dt$Subgroup <- ifelse(is.na(dt$Placebo),
dt$Subgroup,
paste0("   ", dt$Subgroup))
# 替换缺失值：
dt$Treatment <- ifelse(is.na(dt$Treatment), "", dt$Treatment)
dt$Placebo <- ifelse(is.na(dt$Placebo), "", dt$Placebo)
dt$se <- (log(dt$hi) - log(dt$est))/1.96
# 添加空白列，显示CI
dt$` ` <- paste(rep(" ", 20), collapse = " ")
# 创建要显示的置信区间列
dt$`HR (95% CI)` <- ifelse(is.na(dt$se), "",
sprintf("%.2f (%.2f to %.2f)",
dt$est, dt$low, dt$hi))
head(dt)
#>          Subgroup Treatment Placebo      est        low       hi        se
#> 1    All Patients       781     780 1.869694 0.13245636 3.606932 0.3352463
#> 2             Sex                         NA         NA       NA        NA
#> 3            Male       535     548 1.449472 0.06834426 2.830600 0.3414741
#> 4          Female       246     232 2.275120 0.50768005 4.042560 0.2932884
#> 5             Age                         NA         NA       NA        NA
#> 6          <65 yr       297     333 1.509242 0.67029394 2.348190 0.2255292
#>                                                   HR (95% CI)
#> 1                                         1.87 (0.13 to 3.61)
#> 2
#> 3                                         1.45 (0.07 to 2.83)
#> 4                                         2.28 (0.51 to 4.04)
#> 5
#> 6                                         1.51 (0.67 to 2.35)
# 绘制简单的森林图
pdf("forest_plot04.pdf", height = 7, width = 10)
forest(dt[,c(1:3, 20:21)],
est = dt$est,
lower = dt$low,
upper = dt$hi,
sizes = dt$se,
ci_column = 4,
ref_line = 1,
arrow_lab = c("Placebo Better", "Treatment Better"),
xlim = c(0, 4),
ticks_at = c(0.5, 1, 2, 3),
footnote = "This is the demo data. Please feel free to change\nanything you want.")
dev.off()
# 更改主题背景
# 增加汇总列并修改图形参数
dt_tmp <- rbind(dt[-1, ], dt[1, ])  # 实际上就是把第一行放到最后一行
dt_tmp[nrow(dt_tmp), 1] <- "Overall"
# 定义主题：
tm <- forest_theme(base_size = 10,
# 置信区间点形状，线类型/颜色/宽度
ci_pch = 16,  # 点形状
ci_col = "red",
ci_lty = 1, # 线条类型
ci_lwd = 1.5, # 线条宽度
ci_Theight = 0.4, # 在CI的末尾设置T形尾部
# 参考线宽/类型/颜色
refline_lwd = 1,
refline_lty = "dashed",
refline_col = "grey20",
# 垂直的线宽/类型/颜色
vertline_lwd = 1,
vertline_lty = "dashed",
vertline_col = "grey20",
# 更改填充和边框的摘要颜色
summary_fill = "red",
summary_col = "red",
# 脚注字体大小/字形/颜色
footnote_cex = 0.6,
footnote_fontface = "italic",
footnote_col = "red")
# 绘图：
pdf("forest_plot05.pdf", height = 7, width = 10)
forest(dt_tmp[,c(1:3, 20:21)],
est = dt_tmp$est,
lower = dt_tmp$low,
upper = dt_tmp$hi,
sizes = dt_tmp$se,
is_summary = c(rep(FALSE, nrow(dt_tmp)-1), TRUE),
ci_column = 4,
ref_line = 1,
arrow_lab = c("Placebo Better", "Treatment Better"),
xlim = c(0, 4),
ticks_at = c(0.5, 1, 2, 3),
footnote = "This is the demo data. Please feel free to change\nanything you want.",
theme = tm)
dev.off()
# 修改森林图：
p <- forest(dt_tmp[,c(1:3, 20:21)],
est = dt_tmp$est,
lower = dt_tmp$low,
upper = dt_tmp$hi,
sizes = dt_tmp$se,
is_summary = c(rep(FALSE, nrow(dt_tmp)-1), TRUE),
ci_column = 4,
ref_line = 1,
arrow_lab = c("Placebo Better", "Treatment Better"),
xlim = c(0, 4),
ticks_at = c(0.5, 1, 2, 3),
footnote = "This is the demo data. Please feel free to change\nanything you want.",
theme = tm)
# 改变第三行文字的颜色：
g <- edit_plot(p, row = 3, gp = gpar(col = "red", fontface = "italic"))
# 加粗分组变量的名称：
g <- edit_plot(g,
row = c(2, 5, 10, 13, 17, 20),
gp = gpar(fontface = "bold"))
# 改变第五行的背景色：
g <- edit_plot(g, row = 5, which = "background",
gp = gpar(fill = "#4ca9be"))
# 在表头插入文字
g <- insert_text(g,
text = "Treatment group",
col = 2:3,
part = "header",
gp = gpar(fontface = "bold"))
# 在标题的底部添加下划线
g <- add_underline(g, part = "header")
# 在表格内部插入文字：
g <- insert_text(g,
text = "This is a long text. Age and gender summarised above.\nBMI is next",
row = 10,
just = "left",
gp = gpar(cex = 0.6, col = "red", fontface = "italic"))
pdf("forest_plot06.pdf", height = 7, width = 10)
plot(g)
dev.off()
4.73*500+4*346+3.07*347+2.26*263+2.21*144+2.06*944+1.65*87+1.58*232
