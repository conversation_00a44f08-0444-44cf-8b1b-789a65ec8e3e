# 跟着高分SCI学作图 -- 聚类热图 + 注释

> 已经付费加群的小伙伴无需二次付费，等待师兄后续更新即可！

![封面](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20230416161932157.png)

从这个系列开始，师兄就带着大家从各大顶级期刊中的Figuer入手，从仿照别人的作图风格到最后实现自己游刃有余的套用在自己的分析数据上！这一系列绝对是高质量！还不赶紧**点赞+在看**，学起来！

插入公众号

> 本期分享的是期刊：**Science Advances（IF = 14.96）**上面一篇文章中的一个**分类变量环形热图+环形柱状图注释**！
>
> 本系列所有代码和示例数据将会和生信常用图形系列绘图放在一起，扫描下方二维码添加师兄微信，**付费179元（随着群内绘图资源的增加，入群费用也会随之增加，1群￥99 -- 已满，2群￥149 -- 已满，3群￥169 -- 已满）**，即可加入生信绘图交流群。**群内不仅提供生信常用图形系列的代码，还会提供本系列后续所有Figure的实例数据和代码，我会在文章更新后第一时间上传。**

![师兄微信](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20230202225733533.png)

> 与此同时，本系列也配套推出了**[R语言生信绘图进阶版视频课程](https://mp.weixin.qq.com/mp/appmsgalbum?__biz=MzkzMDE4NTc5NA==&action=getalbum&album_id=2878209887242141696#wechat_redirect)**，师兄在课程中**详细介绍了各种图形的理解，数据构建过程，以及绘图的整个思路，从零带着大家敲代码**！良心好课！有需要的可以通过以下链接购买哦！

[![R语言生信绘图进阶版视频教程](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20230416163251381.png)](https://mp.weixin.qq.com/mp/appmsgalbum?__biz=MzkzMDE4NTc5NA==&action=getalbum&album_id=2878209887242141696#wechat_redirect)

> 当然了！如果你还想白嫖，师兄的文章中代码已经写的很清楚了！但是师兄还是希望你点个赞再走呗！
>
> **优惠方式：点赞+在看，并转发这两个系列任意一篇文章至朋友圈，集赞30个，即可享受￥149入群！**

![参考文献](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20230416163620894.png)

话不多说，直接上图！

### 读图

![原图](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20230416163638745.png)

### 效果展示

![效果展示](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20230416173247214.png)

> 这张图难度不大，所用技巧基本都在之前的文章中提到过，但是熟能生巧哈！多练习总是没错的！
>
> 制作不易，欢迎大家看完给个**免费的赞和在看！**让更多的小伙伴看见我们的教程吧！



### 绘图群附加福利

凡是**已经加群**的小伙伴，你们在看文献的时候如果看到好看的Figure，可以发到群里！师兄会及时关注的，**如果被师兄选中，就会在推文中更新！**

#### Figure的要求如下（被选中的前提）：

- 首先肯定是要符合大众审美的，在图形样式上要过关。
- 新颖独特，有与常见图形不一样的地方。
- 有一定难度，太简单的大家都会做，没什么挑战性哈！

#### 往期选中案例

![往期选中案例01](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220828213019162.png)

![往期选中案例](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20221001194829401.png)

### R包载入、数据处理

```R
# 载入R包：
library(ComplexHeatmap)
library(tidyverse)

# 构造数据：创建4组相关系数；
data <- data.frame(Control = runif(44, min = -1, max = 1),
                  PD = runif(44, min = -1, max = 1),
                  AD_PD = runif(44, min = -1, max = 1),
                  AD = runif(44, min = -1, max = 1))
rownames(data) <- paste0("M", 1:44)
data <- t(data)
data[,1:5]
#                  M1          M2           M3         M4          M5
# Control  0.48586439  0.81613777  0.341043832  0.4810714 -0.19630149
# PD       0.22637275 -0.61408027 -0.846040122  0.5593672  0.05330654
# AD_PD    0.41983811  0.06117967  0.004657003 -0.1992623  0.36452314
# AD      -0.03745337 -0.23319219  0.063517675  0.7082907 -0.41313420

# p值矩阵：随机创建一个逻辑型矩阵就好；
p_mat <- as.data.frame(matrix(sample(c(T,F), size = 44*4, replace = T), 4, 44))
p_mat[,1:5]
#      V1    V2    V3    V4    V5
# 1  TRUE FALSE  TRUE FALSE  TRUE
# 2 FALSE FALSE FALSE  TRUE FALSE
# 3  TRUE FALSE FALSE  TRUE  TRUE
# 4  TRUE  TRUE FALSE FALSE FALSE
```

### 绘图

```R
##### 绘图 ---------------
# 设置颜色：
library(circlize)
library(paletteer)
col_fun <- colorRamp2(c(-1, 0, 1), c("#5296cc", "#ffffff","#c7462b"))

# colorbar的颜色：
d_palettes <- palettes_d_names
col_anno <- as.character(paletteer_d("ggsci::default_igv", n=44)) #随机选一个查看
names(col_anno) <- colnames(data)

# colorbar:
top_anno <- HeatmapAnnotation(Module = colnames(data),
                              col = list(Module = col_anno),
                              border = T,show_annotation_name = F,
                              # 去除图例：
                              show_legend = F)

# 绘图：
pdf("plots.pdf", height = 3, width = 10)
Heatmap(data,
        # 行不聚类：
        cluster_rows = F,
        # 列聚类树高度：
        column_dend_height = unit(35, "mm"),
        # 设置颜色：
        col = col_fun,
        # 调整热图格子的边框颜色和粗细：
        rect_gp = gpar(col = "black", lwd = 1),
        # 行名放左边：
        row_names_side = "left",
        # 列名放上边：
        column_names_side = "top",
        # colorbar:
        top_annotation = top_anno,
        # 加星号：
        cell_fun = function(j, i, x, y, width, height, fill) {
          if (p_mat[i,j]) {
            grid.text(sprintf("*"), x, y, gp = gpar(fontsize = 10))
          } else {
            grid.text(sprintf(""), x, y, gp = gpar(fontsize = 10))
          }
        },
        # 修改图例标题：
        heatmap_legend_param = list(title = "Correlation\n(BiCor)"))
dev.off()
```

![效果展示](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20230416173247214.png)



### 往期优秀图形目录

[![渐变火山图](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220617170353925.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247485551&idx=2&sn=f7ccb50f688b3172b55cebbca8cca270&chksm=c27f5f42f508d65498dc1501d27accac139dd2ff7b287f0f088fca77defde6441eb042ceac67&scene=21#wechat_redirect)

[![气泡图+相关性热图](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220617170203902.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247485632&idx=1&sn=6f3aeacc5f7415134b069d24d3b4732d&chksm=c27f5fedf508d6fb0947dc7b31272290ed117bb95958447054cf16d4297ce085ba8d233141f5&scene=21#wechat_redirect)

[![复杂提琴图](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220617170139222.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247485697&idx=1&sn=1ab8e4ebe840fdb34d82f69dda44e99c&chksm=c27f5e2cf508d73a95acb16af6cea08e55b64cd85bd8a9668969789f1e1f25be7b885b2bfb96&scene=21#wechat_redirect)

[![复杂热图](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220617165308156.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247485759&idx=1&sn=222e575d225c6696a9a4566eaea481e6&chksm=c27f5e12f508d7049c92ac254f05a729937ff1ddd791b5d7dcb3e594d4ff68f50be7817a9020&scene=21#wechat_redirect)

[![复杂散点图](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220617165350350.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247485993&idx=1&sn=904eab14bd60ad408cc444679413aebd&chksm=c27f5d04f508d4129359aa4cf6e3d775d4acad26f0f9af5a85c5f4608207d4c4379767fe91da&scene=21#wechat_redirect)

[![复杂热图02](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220617165858168.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247486439&idx=1&sn=6b8e9b003076a514158d2c3ad4586057&chksm=c27f5ccaf508d5dc6c954d8e3bdafef94708f0ad7c538245d11fa583cac7b1aa0bdaeb16adfb&scene=21#wechat_redirect)

[![甘特图](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220617165220466.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247487326&idx=1&sn=5b440cbaa970bb1bc7562231973f48e5&chksm=c27f5873f508d165dc39d083140b1953dca44935984cc28534d163ac38b49509598ae46b3040&scene=21#wechat_redirect)

[![百分比柱状图](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220617165452451.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247488111&idx=1&sn=191da67c064db254edfa99c119a28b59&chksm=c27f4542f508cc5441a60e8d7eb63ec387a69a8ee6052dbaa47250cfed75a59ff983d2bde8cc&scene=21#wechat_redirect)

[![箱线图美化](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220617165515928.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247487891&idx=1&sn=3c299b31a0ab423e9e61b04d63876dbd&chksm=c27f46bef508cfa8b72d8d23400ae55523eda343c183610933e266d522c15523be99b624a226&scene=21#wechat_redirect)

[![弦图](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220617164818884.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247485791&idx=1&sn=015a62410d5b7cad1a014e2ed42d0210&chksm=c27f5e72f508d7644931dcb14bf0ae334f6ec2df635a26673fa49882264765493738b969c727&scene=21#wechat_redirect)

[![mantel test图](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220617165736663.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247486814&idx=1&sn=5d22f637d2f8276a8b27df6873730561&chksm=c27f5a73f508d365f1409c29513ff30c4ac7f46d4832c960d12da74e6e8bf89f63d298696d30&scene=21#wechat_redirect)

[![瀑布图](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220617165801088.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247487076&idx=1&sn=1472c945ae4dc52fb341ba5db3f38888&chksm=c27f5949f508d05f2c3a67bc1c88e3dc32a102533b7cb74044d37c78b6b08279140dcf3866a0&scene=21#wechat_redirect)

[![曼哈顿图](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220617164954104.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247487224&idx=1&sn=8e4baad85758c4138c212e887f53b535&chksm=c27f59d5f508d0c35fb14aef1be0c31db0c7be48f0cf809119b6b01c0d7205c3d786f44ed64f&scene=21#wechat_redirect)

[![复杂热图+堆积柱状图](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20221020210954942.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247492095&idx=1&sn=8a1f69b5173a94a15b5f3c8cbc5748ec&chksm=c27cb6d2f50b3fc40c3d1be432d70a41315ac1b3dd98d6953284f48f08048f77396e97a5557b&scene=21#wechat_redirect)

[![KEGG富集图](https://picgo-1312459003.cos.ap-shanghai.myqcloud.com/img/image-20220617164917318.png)](http://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247487275&idx=1&sn=83a2e66296e6d5b6967e52e29e5ba516&chksm=c27f5806f508d110ee549339f10a513b006ac0f4ba251eb3ebe6dfee79b142862580a75b234d&scene=21#wechat_redirect)

> 以上内容仅为群内部分内容，不代表全部。详细目录请看下方列表！已经入群的小伙伴，无需付费购买推文，群内都会及时更新！

### 示例数据和代码获取

> 本系列所有代码和示例数据将会和生信常用图形系列绘图放在一起，公众号右下角添加师兄微信，**付费179元**，即可加入生信绘图交流群。**群内不仅提供生信常用图形系列的代码，还会提供本系列后续所有Figure的实例数据和代码，我会在文章更新后第一时间上传。**
>
> 当然了！如果你还想白嫖，师兄的文章中代码已经写的很清楚了！但是师兄还是希望你点个赞再走呗！
>
> 以上就是本期的全部内容啦！**欢迎点赞，点在看！**师兄会尽快更新哦！制作不易，你的打赏将成为师兄继续更新的十足动力！
>
> **优惠方式：点赞+在看，并转发这两个系列任意一篇文章至朋友圈，集赞30个，即可享受￥149入群！**


### 往期文章

1. [【合集】生信常用分析图形+跟着高分SCI学作图](https://mp.weixin.qq.com/s?__biz=MzkzMDE4NTc5NA==&mid=2247497171&idx=1&sn=7549a5076f72ce273e507c5a17ba4004&chksm=c27ca2fef50b2be82bd699a7711818eb3f35a8f0330b1ece6d9c18072712d3b336836218632d#rd)

