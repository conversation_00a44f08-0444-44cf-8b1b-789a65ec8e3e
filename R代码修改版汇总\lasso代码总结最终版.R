#getwd() 得到工作目录
#setwd("D:/data/") # 设置工作目录，要用正斜杠/
#install.packages("lars") 
library(rattle)
rattle()  #调用Rattle软件可以进行数据降维

# install.packages("Rcmdr")
#library(Rcmdr) 调用R Commander软件进行菜单操作???
#install.packages(c("JGR","Deducer","DeducerExtras"))
#library(JGR)
#JGR()
#remove.packages("ezr")

# 直接设置 CRAN 镜像
# options(repos = c(CRAN = "https://cloud.r-project.org/")) # nolint
options(repos = c(CRAN = "https://mirrors.tuna.tsinghua.edu.cn/CRAN/"))
# install.packages("ElemStatLearn")  # nolint
install.packages("car") 
install.packages("corrplot") 
install.packages("glmnet") 
install.packages("caret") 
install.packages("leaps")


#%% 读取数据
library(readxl)
library(foreign) #导入外部数据
# hcc <- read.csv("H:\\1.HCC-VETC\\734HCC\\all-HCC\\734hcctumor\\data\\radiomics模型结果\\瘤内radiomics\\train2-radiomics最新.xlsx") # nolint

hcc <- read_excel("H:\\1.HCC-VETC\\734HCC\\all-HCC\\734hcctumor\\data\\radiomics模型结果\\瘤内radiomics\\train2-radiomics最新.xlsx") # nolint
dim(hcc)
# 标准化第4列后的变量
x <- scale(hcc[, 4:ncol(hcc)], center = TRUE, scale = TRUE)

# 组合PHCC列和标准化后的数据
hcc2 <- cbind(PHCC = hcc[, 3], x)
str(hcc2)
# head(hcc2)

# write.csv(data2,file = "D:/data/data11.csv") # nolint

#read.spss("hcctest.csv") # nolint
#write.csv(test,file = "C:/Users/<USER>/Desktop/ch05/paperdftest.csv")#导出验证组数??? # nolint: commented_code_linter, line_length_linter.

#%% #第1种随机分组方法：训练集：验证集=7：3
library(car) #package to calculate Variance Inflation Factor
library(corrplot) #correlation plots
# library(leaps) #best subsets regression
library(glmnet) #allows ridge regression, LASSO and elastic net
library(caret) #this will help identify the appropriate parameters

set.seed(123)#123
t<-createDataPartition(hcc2$PHCC,p=0.70,list=FALSE)
train<-hcc2[t,]
test<-hcc2[-t,]
dim(train)
dim(test)

#response_train<-as.factor(train[,1])
#response_test<-as.factor(test[,1])

x <- as.matrix(train[, 2:3397])
y <- train[,1]

# write.csv(train,file = "E:/Rcode/pyradiomics/HCC-spectral CT/data/CD105/train2.csv")
# write.csv(test,file = "E:/Rcode/pyradiomics/HCC-spectral CT/data/CD105/test2.csv")

#%% 第2种随机分组方法：分层抽样，训练集和验证集内阳性比阴性7：3.
library(MASS)
set.seed(123) #random number generator#123
ind <- sample(2, nrow(hcc2), replace = TRUE, prob = c(0.7, 0.3))#设置7比3分训练组和验证组
train <- hcc2[ind==1, ] #the training data set
test <- hcc2[ind==2, ] #the test data set
dim(test)
dim(train)
x <- as.matrix(train[,2:47])
y <- train[,1]

#第3种设定分组方法：
train <- subset(hcc2, train == TRUE)[, 1:851]#设置分训练组和验证组
str(train)
test = subset(hcc2, train==FALSE)[,1:851]
str(test)
#说明[,1:851]表示所有的行，1到851列

#%% 岭回归
x <- as.matrix(train[, 1:850])
y <- as.matrix(train[, 851])

ridge <- glmnet(x, y, family = "binomial", alpha = 0)
print(ridge)
plot(ridge, label = TRUE)
plot(ridge, xvar = "lambda", label = TRUE)
ridge.coef <- predict(ridge, s=4.714, type = "coefficients") # nolint
ridge.coef
plot(ridge, xvar = "dev", label = TRUE)

newx <- as.matrix(test[, 1:850])  #验证组,850为x的列数
ridge.y = predict(ridge, newx = newx, type = "response", s=4.714)
plot(ridge.y, test$group, xlab = "Predicted", 
     ylab = "Actual", main = "Ridge Regression")
ridge.resid <- ridge.y - test$group 
mean(ridge.resid^2)

#%% lasso回归
lasso <- glmnet(x, y, family = "binomial", alpha = 1)

# lasso <- glmnet(x, y, family = "multinomial", alpha = 1)

print(lasso)
plot(lasso, xvar = "lambda", label = TRUE)

# lasso.coef <- predict(lasso, s = 0.002191, type = "coefficients")
# lasso.coef
# newx <- as.matrix(test[, 1:850])
# lasso.y <- predict(lasso, newx = newx, 
#                    type = "response", s = 0.004714)
# plot(lasso.y, test$group, xlab = "Predicted", ylab = "Actual", 
#      main = "LASSO")
# lasso.resid <- lasso.y - test$group
# mean(lasso.resid^2)

#%%lasso回归交叉验证模型：
#二分类lasso
set.seed(317)
lasso.cv = cv.glmnet(x, y,family = "binomial",
                     type.measure = "mse",nfolds = 10) #deviance, class, mse, mae
plot(lasso.cv)
lasso.cv$lambda.min #minimum
lasso.cv$lambda.1se
coef(lasso.cv, s = "lambda.min")

# newx <- as.matrix(test[, 1:850])
# lasso.y.cv = predict(lasso.cv, newx=newx, type = "response", 
#                      s = "lambda.1se")
# lasso.cv.resid = lasso.y.cv - test$group
# mean(lasso.cv.resid^2)

#%%多分类lasso
set.seed(317)
lasso.cv = cv.glmnet(x, y,family = "multinomial",
                     type.multinomial ="grouped",#grouped 或者 ungrouped
                     type.measure = "deviance",nfolds = 10)#deviance, class, mse, mae

plot(lasso.cv)
lasso.cv$lambda.1se
lasso.cv$lambda.min

options(max.print=2000, width = 10) #调整显示的行数
lassocoef <- coef(lasso.cv, s = "lambda.1se")
typeof(lassocoef)
capture.output(lassocoef, file = "C:/Users/<USER>/Desktop/coef.csv")
#根据csv表格把筛选出的特征的Index写进去,需要包括label对应的index
Active.Index  <- c(1,71,89,191,212,224,228,260,377,383,399,410,
                   414,446,468,470,539,697,979,1044,1084,1185,1257,1327,1443,1448,1604,1704) 

#%%lasso回归交叉验证模型：可计算AUC

set.seed(320) #320
lasso.cv <- cv.glmnet(x, y, family = "binomial",
                   type.measure = "auc",
                   nfolds = 5)
plot(lasso.cv)
lasso.cv$lambda.min #minimum
lasso.cv$lambda.1se #one standard error away

#%%
options(repos = c(CRAN = "https://mirrors.tuna.tsinghua.edu.cn/CRAN/"))
install.packages("InformationValue")

newx <- as.matrix(test[, 2:3397])
lasso.y.cv = predict(lasso.cv, newx=newx, type = "response", 
                     s = "lambda.min")
lasso.cv.resid = lasso.y.cv - test$PHCC
mean(lasso.cv.resid^2)

library(InformationValue)
#验证集
predCV <- predict(lasso.cv, newx = as.matrix(test[,2:3397]),
                  s = "lambda.min",type = "response")        #计算验证组的预测值，可作为影像组学score
#predCV <- predict(lasso.cv, newx = as.matrix(train[, 1:1301]),
                 # s = "lambda.1se",
                #  type = "response")#计算训练组的预测值，可作为影像组学score
test=data.frame(test)
class(test)
actuals <- ifelse(test$PHCC == "1",1,0) # $符号只能用于数据框
misClassError(actuals,predCV)
plotROC(actuals, predCV)

#训练集
predCV <- predict(lasso.cv, newx = as.matrix(train[,2:1219]),
s = "lambda.min",type = "response")#计算训练组的预测值，可作为影像组学score
# train=data.frame(train)
# class(train)
actuals <- ifelse(train[,1] == "1", 1, 0)  
#报错Error: $ operator is invalid for atomic vectors，因为$符号只能用于数据框
misClassError(actuals,predCV)
plotROC(actuals,predCV)

#%% lasso.cv 基于lambda.1se筛选的特征
lasso.cv$lambda.1se
Coefficients <- coef(lasso.cv, s = lasso.cv$lambda.1se)
Active.Index <- which(Coefficients != 0)
Active.Coefficients <- Coefficients[Active.Index]
Active.Index
Active.Coefficients
row.names(Coefficients)[Active.Index]

#lasso.cv 基于lambda.min 筛选的特征
lasso.cv$lambda.min
Coefficients <- coef(lasso.cv, s = lasso.cv$lambda.min)
Active.Index <- which(Coefficients != 0)
Active.Coefficients <- Coefficients[Active.Index]
Active.Index
Active.Coefficients
row.names(Coefficients)[Active.Index]


#方法二：提取“非O”系数特征
# coefPara <- coef(object=lasso.cv,s="lambda.min")
# lasso_values <- as.data.frame(which(coefPara!=0,arr.ind=T)
# lasso_names <- rownames(lasso_values)[-1]
# lasso_coef <- data.frame(Feature=rownames(lasso_values), 
#                          Coef=coefPara[which(coefPara!=0,arr.ind=T)])
# lasso_coef
   
#利用代码得到lasso筛选后的变量数据集

trainlasso<-data.frame(train[1:32,Active.Index]) #1：82表示82行
testlasso<-data.frame(test[1:53,Active.Index])  #1:35表示35行
write.csv(trainlasso,file = "E:/Rcode/pyradiomics/HCC-spectral CT/data/MVI/trainlasso1.csv")
write.csv(testlasso,file = "E:/Rcode/DPHCC/testlassopp.csv")

#删除共线性变量，剔除高度相关性变量
cortrain= cor(trainlasso)
highlycor= findCorrelation(cortrain, cutoff = 0.7, verbose = T, names = FALSE)
filtertrain= trainlasso[,-highlycor]
filtertest= testlasso[,-highlycor]
write.csv(filtertrain,file = "E:/Rcode/pyradiomics/HCC-spectral CT/data/CD105/PP/filtertrain.csv")
write.csv(filtertest,file = "E:/Rcode/pyradiomics/HCC-spectral CT/data/CD105/PP/filtertest.csv")

#%%计算影像组学分数
#11.1 information preparation 
# train_set_lasso <- data.frame(lasso.cv) [Lasso_names]
# valid_set_lasso <-validation_set[names(train_set_lasso)]

trainlasso<-data.frame(train[1:64,Active.Index]) #1：82表示82行
testlasso<-data.frame(test[1:28,Active.Index])  #1:35表示35行

Data_all = as.matrix(rbind(trainlasso,testlasso))
Data_all = Data_all[,-1]  #注意这里需要把金标准label去掉
xn =nrow(Data_all) #row 
yn=ncol(Data_all) #column 

#11.2 get beta and calculate 
beta = as.matrix(Coefficients[which(Coefficients!=0),]) #get beta=Coefficients 
betai_Matrix = as.matrix(beta[-1]) 
betai_Matrix
beta0_Matrix = matrix(beta[1],xn,1)#get beta_0 # nolint
beta0_Matrix
Radcore_Matrix = Data_all %*% betai_Matrix + beta0_Matrix #get Rad-score  # nolint
radscore_all = as.numeric(Radcore_Matrix) 
#将系数矩阵化，并进行矩阵运算。PS：①β0和βi需要分别提取，再线性运算。

#11.3 get radiomics score 
Radscore_train = radscore_all[1:nrow(trainlasso)]#train rads 
Radscore_valid = radscore_all[(nrow(trainlasso)+1):xn] #validation rads

#11.4 show Rad-score 
Radscore_train 
Radscore_valid 

write.csv(Radscore_train,file = "E:/Rcode/pyradiomics/HCC-spectral CT/data/MVI/Radscore_train.csv") # nolint
write.csv(Radscore_valid,file = "E:/Rcode/pyradiomics/HCC-spectral CT/data/MVI/Radscore_valid.csv")
