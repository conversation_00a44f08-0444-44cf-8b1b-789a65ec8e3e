{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["install.packages(\"caret\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"slideshow": {"slide_type": "subslide"}, "vscode": {"languageId": "r"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" [1] \"姓名\"                        \"name\"                       \n", " [3] \"ID\"                          \"sex\"                        \n", " [5] \"age\"                         \"HBCV\"                       \n", " [7] \"Cirrosis\"                    \"ALT\"                        \n", " [9] \"AST\"                         \"GGT\"                        \n", "[11] \"AFP\"                         \"diameter\"                   \n", "[13] \"Tumor_number\"                \"VETC\"                       \n", "[15] \"status\"                      \"PFS\"                        \n", "[17] \"status2\"                     \"PFS2\"                       \n", "[19] \"DRSAP\"                       \"pp_score\"                   \n", "[21] \"hbp_score\"                   \"cp_score\"                   \n", "[23] \"DLRap\"                       \"Handcrafted_pathomics_score\"\n", "[25] \"Deep_pathomics_score\"        \"ap_score\"                   \n", "[27] \"bingliscore\"                 \"deep_score\"                 \n", "[29] \"nomogram\"                    \"nomogram2\"                  \n", " [1] \"sex\"                         \"age\"                        \n", " [3] \"HBCV\"                        \"Cirrosis\"                   \n", " [5] \"ALT\"                         \"AST\"                        \n", " [7] \"GGT\"                         \"AFP\"                        \n", " [9] \"diameter\"                    \"Tumor_number\"               \n", "[11] \"VETC\"                        \"status\"                     \n", "[13] \"PFS\"                         \"status2\"                    \n", "[15] \"PFS2\"                        \"DRSAP\"                      \n", "[17] \"pp_score\"                    \"hbp_score\"                  \n", "[19] \"cp_score\"                    \"DLRap\"                      \n", "[21] \"Handcrafted_pathomics_score\" \"Deep_pathomics_score\"       \n", "[23] \"ap_score\"                    \"bingliscore\"                \n", "[25] \"deep_score\"                  \"nomogram\"                   \n", "[27] \"nomogram2\"                  \n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in coxph.fit(X, Y, istrat, offset, init, control, weights = weights, :\n", "\"Loglik converged before variable  1 ; coefficient may be infinite. \"\n", "Warning message in coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), :\n", "\"a variable appears on both the left and right sides of the formula\"\n", "Warning message in coxph.fit(X, Y, istrat, offset, init, control, weights = weights, :\n", "\"Ran out of iterations and did not converge\"\n", "Warning message in coxph.fit(X, Y, istrat, offset, init, control, weights = weights, :\n", "\"Loglik converged before variable  1 ; coefficient may be infinite. \"\n", "Warning message in coxph.fit(X, Y, istrat, offset, init, control, weights = weights, :\n", "\"Ran out of iterations and did not converge\"\n", "Warning message in coxph.fit(X, Y, istrat, offset, init, control, weights = weights, :\n", "\"one or more coefficients may be infinite\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Variable: sex \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "      coef exp(coef) se(coef)     z Pr(>|z|)  \n", "sex 0.5291    1.6975   0.2510 2.108    0.035 *\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "    exp(coef) exp(-coef) lower .95 upper .95\n", "sex     1.697     0.5891     1.038     2.776\n", "\n", "Concordance= 0.531  (se = 0.02 )\n", "Likelihood ratio test= 5  on 1 df,   p=0.03\n", "Wald test            = 4.44  on 1 df,   p=0.04\n", "Score (logrank) test = 4.54  on 1 df,   p=0.03\n", "\n", "Variable: age \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "        coef exp(coef) se(coef)     z Pr(>|z|)\n", "age 0.001283  1.001284 0.008924 0.144    0.886\n", "\n", "    exp(coef) exp(-coef) lower .95 upper .95\n", "age     1.001     0.9987    0.9839     1.019\n", "\n", "Concordance= 0.488  (se = 0.03 )\n", "Likelihood ratio test= 0.02  on 1 df,   p=0.9\n", "Wald test            = 0.02  on 1 df,   p=0.9\n", "Score (logrank) test = 0.02  on 1 df,   p=0.9\n", "\n", "Variable: HBCV \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "        coef exp(coef) se(coef)      z Pr(>|z|)\n", "HBCV -0.1992    0.8194   0.2101 -0.948    0.343\n", "\n", "     exp(coef) exp(-coef) lower .95 upper .95\n", "HBCV    0.8194       1.22    0.5428     1.237\n", "\n", "Concordance= 0.509  (se = 0.022 )\n", "Likelihood ratio test= 0.87  on 1 df,   p=0.4\n", "Wald test            = 0.9  on 1 df,   p=0.3\n", "Score (logrank) test = 0.9  on 1 df,   p=0.3\n", "\n", "Variable: Cirrosis \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "           coef exp(coef) se(coef)     z Pr(>|z|)\n", "Cirrosis 0.1494    1.1612   0.1771 0.844    0.399\n", "\n", "         exp(coef) exp(-coef) lower .95 upper .95\n", "Cirrosis     1.161     0.8612    0.8207     1.643\n", "\n", "Concordance= 0.52  (se = 0.025 )\n", "Likelihood ratio test= 0.71  on 1 df,   p=0.4\n", "Wald test            = 0.71  on 1 df,   p=0.4\n", "Score (logrank) test = 0.71  on 1 df,   p=0.4\n", "\n", "Variable: ALT \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "      coef exp(coef) se(coef)     z Pr(>|z|)\n", "ALT 0.1014    1.1067   0.2231 0.454     0.65\n", "\n", "    exp(coef) exp(-coef) lower .95 upper .95\n", "ALT     1.107     0.9036    0.7147     1.714\n", "\n", "Concordance= 0.515  (se = 0.022 )\n", "Likelihood ratio test= 0.2  on 1 df,   p=0.7\n", "Wald test            = 0.21  on 1 df,   p=0.6\n", "Score (logrank) test = 0.21  on 1 df,   p=0.6\n", "\n", "Variable: AST \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "      coef exp(coef) se(coef)     z Pr(>|z|)  \n", "AST 0.3753    1.4554   0.1829 2.052   0.0402 *\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "    exp(coef) exp(-coef) lower .95 upper .95\n", "AST     1.455     0.6871     1.017     2.083\n", "\n", "Concordance= 0.571  (se = 0.025 )\n", "Likelihood ratio test= 4.06  on 1 df,   p=0.04\n", "Wald test            = 4.21  on 1 df,   p=0.04\n", "Score (logrank) test = 4.26  on 1 df,   p=0.04\n", "\n", "Variable: GGT \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "      coef exp(coef) se(coef)     z Pr(>|z|)   \n", "GGT 0.5660    1.7612   0.1775 3.189  0.00143 **\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "    exp(coef) exp(-coef) lower .95 upper .95\n", "GGT     1.761     0.5678     1.244     2.494\n", "\n", "Concordance= 0.588  (se = 0.024 )\n", "Likelihood ratio test= 10.07  on 1 df,   p=0.002\n", "Wald test            = 10.17  on 1 df,   p=0.001\n", "Score (logrank) test = 10.44  on 1 df,   p=0.001\n", "\n", "Variable: AFP \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "      coef exp(coef) se(coef)     z Pr(>|z|)   \n", "AFP 0.5392    1.7146   0.1798 2.999  0.00271 **\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "    exp(coef) exp(-coef) lower .95 upper .95\n", "AFP     1.715     0.5832     1.205     2.439\n", "\n", "Concordance= 0.575  (se = 0.024 )\n", "Likelihood ratio test= 8.63  on 1 df,   p=0.003\n", "Wald test            = 9  on 1 df,   p=0.003\n", "Score (logrank) test = 9.21  on 1 df,   p=0.002\n", "\n", "Variable: diameter \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "            coef exp(coef) se(coef)    z Pr(>|z|)    \n", "diameter 0.12147   1.12916  0.02185 5.56  2.7e-08 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "         exp(coef) exp(-coef) lower .95 upper .95\n", "diameter     1.129     0.8856     1.082     1.179\n", "\n", "Concordance= 0.627  (se = 0.028 )\n", "Likelihood ratio test= 25.63  on 1 df,   p=4e-07\n", "Wald test            = 30.91  on 1 df,   p=3e-08\n", "Score (logrank) test = 31.98  on 1 df,   p=2e-08\n", "\n", "Variable: Tumor_number \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "               coef exp(coef) se(coef)     z Pr(>|z|)   \n", "Tumor_number 0.5589    1.7488   0.1890 2.958   0.0031 **\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "             exp(coef) exp(-coef) lower .95 upper .95\n", "Tumor_number     1.749     0.5718     1.207     2.533\n", "\n", "Concordance= 0.547  (se = 0.023 )\n", "Likelihood ratio test= 8.26  on 1 df,   p=0.004\n", "Wald test            = 8.75  on 1 df,   p=0.003\n", "Score (logrank) test = 8.97  on 1 df,   p=0.003\n", "\n", "Variable: VETC \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "       coef exp(coef) se(coef)     z Pr(>|z|)    \n", "VETC 0.7583    2.1346   0.1851 4.096  4.2e-05 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "     exp(coef) exp(-coef) lower .95 upper .95\n", "VETC     2.135     0.4685     1.485     3.068\n", "\n", "Concordance= 0.617  (se = 0.023 )\n", "Likelihood ratio test= 17.41  on 1 df,   p=3e-05\n", "Wald test            = 16.78  on 1 df,   p=4e-05\n", "Score (logrank) test = 17.55  on 1 df,   p=3e-05\n", "\n", "Variable: status \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "            coef exp(coef)  se(coef)     z Pr(>|z|)\n", "status 2.081e+01 1.091e+09 2.278e+03 0.009    0.993\n", "\n", "       exp(coef) exp(-coef) lower .95 upper .95\n", "status 1.091e+09  9.163e-10         0       Inf\n", "\n", "Concordance= 0.803  (se = 0.016 )\n", "Likelihood ratio test= 246.8  on 1 df,   p=<2e-16\n", "Wald test            = 0  on 1 df,   p=1\n", "Score (logrank) test = 206.9  on 1 df,   p=<2e-16\n", "\n", "Variable: PFS \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "          coef  exp(coef)   se(coef)      z Pr(>|z|)    \n", "PFS -2.520e+01  1.136e-11  5.467e+00 -4.609 4.04e-06 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "    exp(coef) exp(-coef) lower .95 upper .95\n", "PFS 1.136e-11  8.804e+10 2.521e-16 5.117e-07\n", "\n", "Concordance= 0.992  (se = 0.002 )\n", "Likelihood ratio test= 980.8  on 1 df,   p=<2e-16\n", "Wald test            = 21.25  on 1 df,   p=4e-06\n", "Score (logrank) test = 201.9  on 1 df,   p=<2e-16\n", "\n", "Variable: status2 \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "             coef exp(coef)  se(coef)    z Pr(>|z|)\n", "status2 2.184e+01 3.056e+09 2.285e+03 0.01    0.992\n", "\n", "        exp(coef) exp(-coef) lower .95 upper .95\n", "status2 3.056e+09  3.273e-10         0       Inf\n", "\n", "Concordance= 0.853  (se = 0.009 )\n", "Likelihood ratio test= 315.6  on 1 df,   p=<2e-16\n", "Wald test            = 0  on 1 df,   p=1\n", "Score (logrank) test = 346.1  on 1 df,   p=<2e-16\n", "\n", "Variable: PFS2 \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "           coef  exp(coef)   se(coef)      z Pr(>|z|)   \n", "PFS2 -4.412e+01  6.869e-20  1.375e+01 -3.209  0.00133 **\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "     exp(coef) exp(-coef) lower .95 upper .95\n", "PFS2 6.869e-20  1.456e+19 1.358e-31 3.475e-08\n", "\n", "Concordance= 0.949  (se = 0.01 )\n", "Likelihood ratio test= 790.7  on 1 df,   p=<2e-16\n", "Wald test            = 10.3  on 1 df,   p=0.001\n", "Score (logrank) test = 303.1  on 1 df,   p=<2e-16\n", "\n", "Variable: DRSAP \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "        coef exp(coef) se(coef)     z Pr(>|z|)    \n", "DRSAP 2.2574    9.5587   0.3758 6.008 1.88e-09 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "      exp(coef) exp(-coef) lower .95 upper .95\n", "DRSAP     9.559     0.1046     4.577     19.96\n", "\n", "Concordance= 0.658  (se = 0.026 )\n", "Likelihood ratio test= 34.9  on 1 df,   p=3e-09\n", "Wald test            = 36.09  on 1 df,   p=2e-09\n", "Score (logrank) test = 37.91  on 1 df,   p=7e-10\n", "\n", "Variable: pp_score \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "           coef exp(coef) se(coef)    z Pr(>|z|)    \n", "pp_score 1.9409    6.9647   0.3937 4.93 8.21e-07 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "         exp(coef) exp(-coef) lower .95 upper .95\n", "pp_score     6.965     0.1436      3.22     15.07\n", "\n", "Concordance= 0.634  (se = 0.028 )\n", "Likelihood ratio test= 24.26  on 1 df,   p=8e-07\n", "Wald test            = 24.31  on 1 df,   p=8e-07\n", "Score (logrank) test = 25.1  on 1 df,   p=5e-07\n", "\n", "Variable: hbp_score \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "            coef exp(coef) se(coef)   z Pr(>|z|)    \n", "hbp_score 1.7613    5.8199   0.3829 4.6 4.23e-06 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "          exp(coef) exp(-coef) lower .95 upper .95\n", "hbp_score      5.82     0.1718     2.748     12.33\n", "\n", "Concordance= 0.639  (se = 0.027 )\n", "Likelihood ratio test= 20.98  on 1 df,   p=5e-06\n", "Wald test            = 21.16  on 1 df,   p=4e-06\n", "Score (logrank) test = 21.84  on 1 df,   p=3e-06\n", "\n", "Variable: cp_score \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "           coef exp(coef) se(coef)     z Pr(>|z|)    \n", "cp_score 1.9507    7.0334   0.3877 5.031 4.88e-07 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "         exp(coef) exp(-coef) lower .95 upper .95\n", "cp_score     7.033     0.1422      3.29     15.04\n", "\n", "Concordance= 0.64  (se = 0.027 )\n", "Likelihood ratio test= 24.81  on 1 df,   p=6e-07\n", "Wald test            = 25.31  on 1 df,   p=5e-07\n", "Score (logrank) test = 26.31  on 1 df,   p=3e-07\n", "\n", "Variable: DLRap \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "        coef exp(coef) se(coef)    z Pr(>|z|)    \n", "DLRap 0.9728    2.6453   0.1864 5.22 1.79e-07 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "      exp(coef) exp(-coef) lower .95 upper .95\n", "DLRap     2.645      0.378     1.836     3.812\n", "\n", "Concordance= 0.63  (se = 0.022 )\n", "Likelihood ratio test= 28.46  on 1 df,   p=1e-07\n", "Wald test            = 27.25  on 1 df,   p=2e-07\n", "Score (logrank) test = 29.28  on 1 df,   p=6e-08\n", "\n", "Variable: Handcrafted_pathomics_score \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "                               coef exp(coef) se(coef)    z Pr(>|z|)   \n", "Handcrafted_pathomics_score  2.5774   13.1631   0.8451 3.05  0.00229 **\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "                            exp(coef) exp(-coef) lower .95 upper .95\n", "Handcrafted_pathomics_score     13.16    0.07597     2.512     68.97\n", "\n", "Concordance= 0.595  (se = 0.03 )\n", "Likelihood ratio test= 8.53  on 1 df,   p=0.004\n", "Wald test            = 9.3  on 1 df,   p=0.002\n", "Score (logrank) test = 9.34  on 1 df,   p=0.002\n", "\n", "Variable: Deep_pathomics_score \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "                       coef exp(coef) se(coef)     z Pr(>|z|)    \n", "Deep_pathomics_score 2.0619    7.8606   0.4579 4.503 6.69e-06 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "                     exp(coef) exp(-coef) lower .95 upper .95\n", "Deep_pathomics_score     7.861     0.1272     3.204     19.28\n", "\n", "Concordance= 0.635  (se = 0.028 )\n", "Likelihood ratio test= 20.1  on 1 df,   p=7e-06\n", "Wald test            = 20.28  on 1 df,   p=7e-06\n", "Score (logrank) test = 20.59  on 1 df,   p=6e-06\n", "\n", "Variable: ap_score \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "           coef exp(coef) se(coef)     z Pr(>|z|)    \n", "ap_score 2.2574    9.5587   0.3758 6.008 1.88e-09 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "         exp(coef) exp(-coef) lower .95 upper .95\n", "ap_score     9.559     0.1046     4.577     19.96\n", "\n", "Concordance= 0.658  (se = 0.026 )\n", "Likelihood ratio test= 34.9  on 1 df,   p=3e-09\n", "Wald test            = 36.09  on 1 df,   p=2e-09\n", "Score (logrank) test = 37.91  on 1 df,   p=7e-10\n", "\n", "Variable: bingliscore \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "               coef exp(coef) se(coef)    z Pr(>|z|)   \n", "bingliscore  2.5774   13.1631   0.8451 3.05  0.00229 **\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "            exp(coef) exp(-coef) lower .95 upper .95\n", "bingliscore     13.16    0.07597     2.512     68.97\n", "\n", "Concordance= 0.595  (se = 0.03 )\n", "Likelihood ratio test= 8.53  on 1 df,   p=0.004\n", "Wald test            = 9.3  on 1 df,   p=0.002\n", "Score (logrank) test = 9.34  on 1 df,   p=0.002\n", "\n", "Variable: deep_score \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "             coef exp(coef) se(coef)     z Pr(>|z|)    \n", "deep_score 2.0619    7.8606   0.4579 4.503 6.69e-06 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "           exp(coef) exp(-coef) lower .95 upper .95\n", "deep_score     7.861     0.1272     3.204     19.28\n", "\n", "Concordance= 0.635  (se = 0.028 )\n", "Likelihood ratio test= 20.1  on 1 df,   p=7e-06\n", "Wald test            = 20.28  on 1 df,   p=7e-06\n", "Score (logrank) test = 20.59  on 1 df,   p=6e-06\n", "\n", "Variable: nomogram \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "           coef exp(coef) se(coef)     z Pr(>|z|)    \n", "nomogram 0.9996    2.7173   0.1385 7.218 5.26e-13 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "         exp(coef) exp(-coef) lower .95 upper .95\n", "nomogram     2.717      0.368     2.071     3.565\n", "\n", "Concordance= 0.688  (se = 0.024 )\n", "Likelihood ratio test= 52.49  on 1 df,   p=4e-13\n", "Wald test            = 52.1  on 1 df,   p=5e-13\n", "Score (logrank) test = 55.06  on 1 df,   p=1e-13\n", "\n", "Variable: nomogram2 \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "            coef exp(coef) se(coef)     z Pr(>|z|)    \n", "nomogram2 0.9539    2.5959   0.1382 6.903  5.1e-12 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "          exp(coef) exp(-coef) lower .95 upper .95\n", "nomogram2     2.596     0.3852      1.98     3.403\n", "\n", "Concordance= 0.686  (se = 0.025 )\n", "Likelihood ratio test= 47.38  on 1 df,   p=6e-12\n", "Wald test            = 47.65  on 1 df,   p=5e-12\n", "Score (logrank) test = 49.72  on 1 df,   p=2e-12\n", "\n", "Variable: sex \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "      coef exp(coef) se(coef)     z Pr(>|z|)  \n", "sex 0.5291    1.6975   0.2510 2.108    0.035 *\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "    exp(coef) exp(-coef) lower .95 upper .95\n", "sex     1.697     0.5891     1.038     2.776\n", "\n", "Concordance= 0.531  (se = 0.02 )\n", "Likelihood ratio test= 5  on 1 df,   p=0.03\n", "Wald test            = 4.44  on 1 df,   p=0.04\n", "Score (logrank) test = 4.54  on 1 df,   p=0.03\n", "\n", "Variable: AST \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "      coef exp(coef) se(coef)     z Pr(>|z|)  \n", "AST 0.3753    1.4554   0.1829 2.052   0.0402 *\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "    exp(coef) exp(-coef) lower .95 upper .95\n", "AST     1.455     0.6871     1.017     2.083\n", "\n", "Concordance= 0.571  (se = 0.025 )\n", "Likelihood ratio test= 4.06  on 1 df,   p=0.04\n", "Wald test            = 4.21  on 1 df,   p=0.04\n", "Score (logrank) test = 4.26  on 1 df,   p=0.04\n", "\n", "Variable: GGT \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "      coef exp(coef) se(coef)     z Pr(>|z|)   \n", "GGT 0.5660    1.7612   0.1775 3.189  0.00143 **\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "    exp(coef) exp(-coef) lower .95 upper .95\n", "GGT     1.761     0.5678     1.244     2.494\n", "\n", "Concordance= 0.588  (se = 0.024 )\n", "Likelihood ratio test= 10.07  on 1 df,   p=0.002\n", "Wald test            = 10.17  on 1 df,   p=0.001\n", "Score (logrank) test = 10.44  on 1 df,   p=0.001\n", "\n", "Variable: AFP \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "      coef exp(coef) se(coef)     z Pr(>|z|)   \n", "AFP 0.5392    1.7146   0.1798 2.999  0.00271 **\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "    exp(coef) exp(-coef) lower .95 upper .95\n", "AFP     1.715     0.5832     1.205     2.439\n", "\n", "Concordance= 0.575  (se = 0.024 )\n", "Likelihood ratio test= 8.63  on 1 df,   p=0.003\n", "Wald test            = 9  on 1 df,   p=0.003\n", "Score (logrank) test = 9.21  on 1 df,   p=0.002\n", "\n", "Variable: diameter \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "            coef exp(coef) se(coef)    z Pr(>|z|)    \n", "diameter 0.12147   1.12916  0.02185 5.56  2.7e-08 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "         exp(coef) exp(-coef) lower .95 upper .95\n", "diameter     1.129     0.8856     1.082     1.179\n", "\n", "Concordance= 0.627  (se = 0.028 )\n", "Likelihood ratio test= 25.63  on 1 df,   p=4e-07\n", "Wald test            = 30.91  on 1 df,   p=3e-08\n", "Score (logrank) test = 31.98  on 1 df,   p=2e-08\n", "\n", "Variable: Tumor_number \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "               coef exp(coef) se(coef)     z Pr(>|z|)   \n", "Tumor_number 0.5589    1.7488   0.1890 2.958   0.0031 **\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "             exp(coef) exp(-coef) lower .95 upper .95\n", "Tumor_number     1.749     0.5718     1.207     2.533\n", "\n", "Concordance= 0.547  (se = 0.023 )\n", "Likelihood ratio test= 8.26  on 1 df,   p=0.004\n", "Wald test            = 8.75  on 1 df,   p=0.003\n", "Score (logrank) test = 8.97  on 1 df,   p=0.003\n", "\n", "Variable: VETC \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "       coef exp(coef) se(coef)     z Pr(>|z|)    \n", "VETC 0.7583    2.1346   0.1851 4.096  4.2e-05 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "     exp(coef) exp(-coef) lower .95 upper .95\n", "VETC     2.135     0.4685     1.485     3.068\n", "\n", "Concordance= 0.617  (se = 0.023 )\n", "Likelihood ratio test= 17.41  on 1 df,   p=3e-05\n", "Wald test            = 16.78  on 1 df,   p=4e-05\n", "Score (logrank) test = 17.55  on 1 df,   p=3e-05\n", "\n", "Variable: PFS \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "          coef  exp(coef)   se(coef)      z Pr(>|z|)    \n", "PFS -2.520e+01  1.136e-11  5.467e+00 -4.609 4.04e-06 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "    exp(coef) exp(-coef) lower .95 upper .95\n", "PFS 1.136e-11  8.804e+10 2.521e-16 5.117e-07\n", "\n", "Concordance= 0.992  (se = 0.002 )\n", "Likelihood ratio test= 980.8  on 1 df,   p=<2e-16\n", "Wald test            = 21.25  on 1 df,   p=4e-06\n", "Score (logrank) test = 201.9  on 1 df,   p=<2e-16\n", "\n", "Variable: PFS2 \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "           coef  exp(coef)   se(coef)      z Pr(>|z|)   \n", "PFS2 -4.412e+01  6.869e-20  1.375e+01 -3.209  0.00133 **\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "     exp(coef) exp(-coef) lower .95 upper .95\n", "PFS2 6.869e-20  1.456e+19 1.358e-31 3.475e-08\n", "\n", "Concordance= 0.949  (se = 0.01 )\n", "Likelihood ratio test= 790.7  on 1 df,   p=<2e-16\n", "Wald test            = 10.3  on 1 df,   p=0.001\n", "Score (logrank) test = 303.1  on 1 df,   p=<2e-16\n", "\n", "Variable: DRSAP \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "        coef exp(coef) se(coef)     z Pr(>|z|)    \n", "DRSAP 2.2574    9.5587   0.3758 6.008 1.88e-09 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "      exp(coef) exp(-coef) lower .95 upper .95\n", "DRSAP     9.559     0.1046     4.577     19.96\n", "\n", "Concordance= 0.658  (se = 0.026 )\n", "Likelihood ratio test= 34.9  on 1 df,   p=3e-09\n", "Wald test            = 36.09  on 1 df,   p=2e-09\n", "Score (logrank) test = 37.91  on 1 df,   p=7e-10\n", "\n", "Variable: pp_score \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "           coef exp(coef) se(coef)    z Pr(>|z|)    \n", "pp_score 1.9409    6.9647   0.3937 4.93 8.21e-07 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "         exp(coef) exp(-coef) lower .95 upper .95\n", "pp_score     6.965     0.1436      3.22     15.07\n", "\n", "Concordance= 0.634  (se = 0.028 )\n", "Likelihood ratio test= 24.26  on 1 df,   p=8e-07\n", "Wald test            = 24.31  on 1 df,   p=8e-07\n", "Score (logrank) test = 25.1  on 1 df,   p=5e-07\n", "\n", "Variable: hbp_score \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "            coef exp(coef) se(coef)   z Pr(>|z|)    \n", "hbp_score 1.7613    5.8199   0.3829 4.6 4.23e-06 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "          exp(coef) exp(-coef) lower .95 upper .95\n", "hbp_score      5.82     0.1718     2.748     12.33\n", "\n", "Concordance= 0.639  (se = 0.027 )\n", "Likelihood ratio test= 20.98  on 1 df,   p=5e-06\n", "Wald test            = 21.16  on 1 df,   p=4e-06\n", "Score (logrank) test = 21.84  on 1 df,   p=3e-06\n", "\n", "Variable: cp_score \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "           coef exp(coef) se(coef)     z Pr(>|z|)    \n", "cp_score 1.9507    7.0334   0.3877 5.031 4.88e-07 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "         exp(coef) exp(-coef) lower .95 upper .95\n", "cp_score     7.033     0.1422      3.29     15.04\n", "\n", "Concordance= 0.64  (se = 0.027 )\n", "Likelihood ratio test= 24.81  on 1 df,   p=6e-07\n", "Wald test            = 25.31  on 1 df,   p=5e-07\n", "Score (logrank) test = 26.31  on 1 df,   p=3e-07\n", "\n", "Variable: DLRap \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "        coef exp(coef) se(coef)    z Pr(>|z|)    \n", "DLRap 0.9728    2.6453   0.1864 5.22 1.79e-07 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "      exp(coef) exp(-coef) lower .95 upper .95\n", "DLRap     2.645      0.378     1.836     3.812\n", "\n", "Concordance= 0.63  (se = 0.022 )\n", "Likelihood ratio test= 28.46  on 1 df,   p=1e-07\n", "Wald test            = 27.25  on 1 df,   p=2e-07\n", "Score (logrank) test = 29.28  on 1 df,   p=6e-08\n", "\n", "Variable: Handcrafted_pathomics_score \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "                               coef exp(coef) se(coef)    z Pr(>|z|)   \n", "Handcrafted_pathomics_score  2.5774   13.1631   0.8451 3.05  0.00229 **\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "                            exp(coef) exp(-coef) lower .95 upper .95\n", "Handcrafted_pathomics_score     13.16    0.07597     2.512     68.97\n", "\n", "Concordance= 0.595  (se = 0.03 )\n", "Likelihood ratio test= 8.53  on 1 df,   p=0.004\n", "Wald test            = 9.3  on 1 df,   p=0.002\n", "Score (logrank) test = 9.34  on 1 df,   p=0.002\n", "\n", "Variable: Deep_pathomics_score \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "                       coef exp(coef) se(coef)     z Pr(>|z|)    \n", "Deep_pathomics_score 2.0619    7.8606   0.4579 4.503 6.69e-06 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "                     exp(coef) exp(-coef) lower .95 upper .95\n", "Deep_pathomics_score     7.861     0.1272     3.204     19.28\n", "\n", "Concordance= 0.635  (se = 0.028 )\n", "Likelihood ratio test= 20.1  on 1 df,   p=7e-06\n", "Wald test            = 20.28  on 1 df,   p=7e-06\n", "Score (logrank) test = 20.59  on 1 df,   p=6e-06\n", "\n", "Variable: ap_score \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "           coef exp(coef) se(coef)     z Pr(>|z|)    \n", "ap_score 2.2574    9.5587   0.3758 6.008 1.88e-09 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "         exp(coef) exp(-coef) lower .95 upper .95\n", "ap_score     9.559     0.1046     4.577     19.96\n", "\n", "Concordance= 0.658  (se = 0.026 )\n", "Likelihood ratio test= 34.9  on 1 df,   p=3e-09\n", "Wald test            = 36.09  on 1 df,   p=2e-09\n", "Score (logrank) test = 37.91  on 1 df,   p=7e-10\n", "\n", "Variable: bingliscore \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "               coef exp(coef) se(coef)    z Pr(>|z|)   \n", "bingliscore  2.5774   13.1631   0.8451 3.05  0.00229 **\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "            exp(coef) exp(-coef) lower .95 upper .95\n", "bingliscore     13.16    0.07597     2.512     68.97\n", "\n", "Concordance= 0.595  (se = 0.03 )\n", "Likelihood ratio test= 8.53  on 1 df,   p=0.004\n", "Wald test            = 9.3  on 1 df,   p=0.002\n", "Score (logrank) test = 9.34  on 1 df,   p=0.002\n", "\n", "Variable: deep_score \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "             coef exp(coef) se(coef)     z Pr(>|z|)    \n", "deep_score 2.0619    7.8606   0.4579 4.503 6.69e-06 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "           exp(coef) exp(-coef) lower .95 upper .95\n", "deep_score     7.861     0.1272     3.204     19.28\n", "\n", "Concordance= 0.635  (se = 0.028 )\n", "Likelihood ratio test= 20.1  on 1 df,   p=7e-06\n", "Wald test            = 20.28  on 1 df,   p=7e-06\n", "Score (logrank) test = 20.59  on 1 df,   p=6e-06\n", "\n", "Variable: nomogram \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "           coef exp(coef) se(coef)     z Pr(>|z|)    \n", "nomogram 0.9996    2.7173   0.1385 7.218 5.26e-13 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "         exp(coef) exp(-coef) lower .95 upper .95\n", "nomogram     2.717      0.368     2.071     3.565\n", "\n", "Concordance= 0.688  (se = 0.024 )\n", "Likelihood ratio test= 52.49  on 1 df,   p=4e-13\n", "Wald test            = 52.1  on 1 df,   p=5e-13\n", "Score (logrank) test = 55.06  on 1 df,   p=1e-13\n", "\n", "Variable: nomogram2 \n", "Call:\n", "coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), \n", "    data = data_subset)\n", "\n", "  n= 278, number of events= 128 \n", "\n", "            coef exp(coef) se(coef)     z Pr(>|z|)    \n", "nomogram2 0.9539    2.5959   0.1382 6.903  5.1e-12 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "\n", "          exp(coef) exp(-coef) lower .95 upper .95\n", "nomogram2     2.596     0.3852      1.98     3.403\n", "\n", "Concordance= 0.686  (se = 0.025 )\n", "Likelihood ratio test= 47.38  on 1 df,   p=6e-12\n", "Wald test            = 47.65  on 1 df,   p=5e-12\n", "Score (logrank) test = 49.72  on 1 df,   p=2e-12\n", "\n", " [1] \"sex\"                         \"AST\"                        \n", " [3] \"GGT\"                         \"AFP\"                        \n", " [5] \"diameter\"                    \"Tumor_number\"               \n", " [7] \"VETC\"                        \"PFS\"                        \n", " [9] \"PFS2\"                        \"DRSAP\"                      \n", "[11] \"pp_score\"                    \"hbp_score\"                  \n", "[13] \"cp_score\"                    \"DLRap\"                      \n", "[15] \"Handcrafted_pathomics_score\" \"Deep_pathomics_score\"       \n", "[17] \"ap_score\"                    \"bingliscore\"                \n", "[19] \"deep_score\"                  \"nomogram\"                   \n", "[21] \"nomogram2\"                  \n", "[1] \"sex+AST+GGT+AFP+diameter+Tumor_number+VETC+PFS+PFS2+DRSAP+pp_score+hbp_score+cp_score+DLRap+Handcrafted_pathomics_score+Deep_pathomics_score+ap_score+bingliscore+deep_score+nomogram+nomogram2\"\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message in coxph.fit(X, Y, istrat, offset, init, control, weights = weights, :\n", "\"Loglik converged before variable  1 ; coefficient may be infinite. \"\n", "Warning message in coxph.fit(X, Y, istrat, offset, init, control, weights = weights, :\n", "\"Loglik converged before variable  1 ; coefficient may be infinite. \"\n", "Warning message in coxph(formula = as.formula(paste(\"Surv(PFS2, status2==1) ~\", :\n", "\"a variable appears on both the left and right sides of the formula\"\n", "Warning message in coxph.fit(X, Y, istrat, offset, init, control, weights = weights, :\n", "\"Ran out of iterations and did not converge\"\n", "Warning message in coxph.fit(X, Y, istrat, offset, init, control, weights = weights, :\n", "\"one or more coefficients may be infinite\"\n"]}], "source": ["#COX代码总结\n", "# 安装并加载所需的包\n", "# install.packages(\"readxl\")\n", "# install.packages(\"survival\")\n", "library(readxl)\n", "library(survival)\n", "library(MASS)\n", "# 从Excel文件中读取数据\n", "data <- read_excel('K:/2020-2023HCC/579hcc/clinical data/data/train30预后最终版.xlsx') #有时不能带中文 # nolint\n", "print(colnames(data))\n", "\n", "# 取第4列及其后的数据\n", "data_subset <- data[, 4:ncol(data)]\n", "variables <- colnames(data_subset)\n", "print(variables)\n", "\n", "# 要进行单变量Cox回归的变量列表\n", "# variables <- c(\"sex\",\"age\", \"HBCV\",\"Cirrosis\",\"ALT\",\"AST\",\"GGT\",\"AFP\",\"diameter\",\n", "#                \"number\",\"ap_score\",\"pp_score\", \"hbp_score\",\"cp_score\",\"bingliscore\",\"deep_score\")\n", "\n", "# 使用lapply批量进行单变量Cox回归\n", "cox_models_univar <- lapply(variables, function(var) {\n", "  coxph(formula = as.formula(paste(\"Surv(PFS, status==1) ~\", var)), data=data_subset )\n", "})\n", "\n", "# 打印每个单变量回归结果\n", "for (i in 1:length(cox_models_univar)) {\n", "  cat(\"Variable:\", variables[i], \"\\n\")\n", "  print(summary(cox_models_univar[[i]]))\n", "}\n", "\n", "# 创建一个用于存储P值小于0.05的变量的向量\n", "significant_vars <- c()\n", "\n", "# 循环遍历每个单变量回归结果\n", "for (i in 1:length(cox_models_univar)) {\n", "  # 获取回归模型的摘要\n", "  model_summary <- summary(cox_models_univar[[i]])\n", "  \n", "  # 若变量的P值小于0.05，则打印该变量名并将其添加到significant_vars向量中\n", "  if (model_summary$coefficients[1, \"Pr(>|z|)\"] < 0.1) {\n", "    cat(\"Variable:\", variables[i], \"\\n\")\n", "    print(model_summary)\n", "    significant_vars <- c(significant_vars, variables[i])\n", "  }\n", "}\n", "\n", "# 打印出P值小于0.05的所有变量\n", "print(significant_vars)\n", "formula_vars <- paste(significant_vars, collapse=\"+\")\n", "print(formula_vars)\n", "\n", "\n", "#保存单变量回归结果到excel\n", "library(openxlsx)  # Load the library for writing Excel files\n", "\n", "# Get variable names\n", "variables <- colnames(data_subset)\n", "\n", "# Create an empty dataframe to store results\n", "result_df <- data.frame(Variable = character(),\n", "                        HR = numeric(),\n", "                        CI_lower = numeric(),\n", "                        CI_upper = numeric(),\n", "                        P_value = numeric(),\n", "                        Coefficient = numeric(),  # Add a column for coefficient values\n", "                        stringsAsFactors = FALSE)\n", "\n", "# Loop through each variable and perform single-variable Cox regression\n", "for (i in 1:length(variables)) {\n", "  var <- variables[i]\n", "  \n", "  # Fit Cox model\n", "  cox_model <- coxph(formula = as.formula(paste(\"Surv(PFS2, status2==1) ~\", var)), data = data_subset)\n", "  \n", "  # Extract HR, CI, and P-value\n", "  cox_summary <- summary(cox_model)\n", "  hr <- round(cox_summary$coefficients[1, \"exp(coef)\"], 3)\n", "  ci_lower <- round(cox_summary$conf.int[3], 3)\n", "  ci_upper <- round(cox_summary$conf.int[4], 3)\n", "  p_value <- round(cox_summary$coefficients[1, \"Pr(>|z|)\"], 3)\n", "  coefficient <- round(cox_summary$coefficients[1, \"coef\"], 3)\n", "  \n", "  # Append results to the dataframe\n", "  result_df <- rbind(result_df, data.frame(Variable = var,\n", "                                           HR = hr,\n", "                                           CI_lower = ci_lower,\n", "                                           CI_upper = ci_upper,\n", "                                           P_value = p_value,\n", "                                           Coefficient = coefficient))\n", "}\n", "\n", "# Save results to an Excel file\n", "# write.xlsx(result_df, file = \"K:/2020-2023HCC/579hcc/clinical data/data/早期复发.xlsx\", sheetName = \"Results\", row.names = FALSE)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["\n", "# 执行多变量Cox回归分析:enter法\n", "cox_model_multivar <- coxph(Surv(PFS2, status2==1) ~ AST+GGT+AFP+diameter+Tumor_number+DRSAP+pp_score+hbp_score+cp_score+Handcrafted_pathomics_score+Deep_pathomics_score, data=data_subset)\n", "\n", "# 打印多变量回归结果\n", "summary(cox_model_multivar)\n", "\n", "\n", "# 计算相关系数矩阵\n", "significant_vars <- setdiff(significant_vars, c(\"VETC\", \"PFS\")) #去掉VETC和PFS\n", "print(significant_vars)\n", "correlation_matrix <- cor(data[significant_vars])\n", "correlation_matrix\n", "\n", "# 绘制相关性矩阵的热力图\n", "heatmap(correlation_matrix, \n", "        annot = TRUE, \n", "        cmap = colorRampPalette(c(\"blue\", \"white\", \"red\"))(100), \n", "        cexRow = 0.8, \n", "        cexCol = 0.8, \n", "        main = \"Correlation Matrix\")\n", "\n", "#删除共线性变量，剔除高度相关性变量\n", "library(caret)\n", "highlycor= findCorrelation(correlation_matrix, cutoff = 0.90, verbose = T, names =TRUE)\n", "print(highlycor)# 获取需要删除的变量名"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["##R语言进行AIC或BIC逐步回归方法用于多变量cox回归\n", "# 安装并加载所需的包\n", "# install.packages(\"survival\")\n", "# install.packages(\"MASS\")\n", "library(survival)\n", "library(MASS)\n", "library(readxl)\n", "library(openxlsx)\n", "library(ggplot2)\n", "library(pec)\n", "\n", "# 从Excel文件中读取数据\n", "data <- read_excel('K:/2020-2023HCC/579hcc/clinical data/data/train30预后最终版.xlsx')#522HCC预后完整版\n", "#data <- read.csv('K:/2020-2023HCC/579hcc/clinical data/data/test预后最终版.csv')#522HCC预后完整版\n", "\n", "# 获取变量名\n", "variable_names <- colnames(data)\n", "print(variable_names)\n", "\n", "# 取第4列及其后的数据\n", "data_subset <- data[, 4:ncol(data)]\n", "\n", "# 执行多变量Cox回归分析:enter法\n", "# cox_model_multivar <- coxph(Surv(PFS2, status2==1) ~ ap_score +bingliscore+deep_score, data=data_subset)\n", "\n", "# # 打印多变量回归结果\n", "# summary(cox_model_multivar)\n", "\n", "# # 执行AIC逐步回归方法\n", "cox_model_stepwise <- coxph(Surv(PFS, status==1) ~AST+GGT+AFP+diameter+Tumor_number+ap_score+pp_score+Handcrafted_pathomics_score+Deep_pathomics_score, data=data_subset)  # 使用所有变量进行初始拟合\n", "\n", "# 使用stepAIC函数进行AIC逐步回归\n", "AIC_model <- stepAIC(cox_model_stepwise, direction=\"both\", trace=TRUE)  #both,forward和backward  \n", "\n", "# 打印最终模型的摘要\n", "summary(AIC_model)\n", "\n", "# # 使用step函数进行BIC逐步回归\n", "# BIC_model <- step(cox_model_stepwise, direction=\"both\", trace=TRUE, k=log(nrow(data_subset)))\n", "# summary(BIC_model)\n", "\n", "# # 向前逐步选择变量，基于似然比检验\n", "# stepwise_model <- step(cox_model_stepwise, direction = \"backward\")\n", "# summary(stepwise_model)\n", "# stepwise_model <- step(cox_model_stepwise, direction = \"forward\", test = \"wald\")\n", "# summary(stepwise_model)\n", "# 计算Concordance指数和95% CI\n", "\n", "c_index <- summary(cox_model_multivar)$concordance\n", "\n", "# 设置bootstrap重复抽样次数\n", "n_bootstrap <- 1000\n", "\n", "# 创建一个空向量，用于存储bootstrap抽样得到的Concordance值\n", "bootstrap_concordance <- numeric(n_bootstrap)\n", "\n", "# 进行bootstrap重复抽样计算Concordance\n", "for (i in 1:n_bootstrap) {\n", "  # 对原始数据进行有放回抽样\n", "  bootstrap_sample <- sample(1:nrow(data_subset), replace = TRUE) # nolint\n", "  \n", "  # 根据bootstrap样本计算Cox回归模型\n", "  bootstrap_model <- coxph(Surv(PFS, status==1) ~ ap_score + bingliscore + deep_score, data=data_subset[bootstrap_sample, ])\n", "  \n", "  # 计算bootstrap样本的Concordance指数\n", "  bootstrap_concordance[i] <- summary(bootstrap_model)$concordance\n", "}\n", "\n", "# 计算bootstrap抽样得到的Concordance的标准误差\n", "bootstrap_se <- sd(bootstrap_concordance)\n", "\n", "# 计算95%置信区间\n", "lower_ci <- c_index - 1.96 * bootstrap_se\n", "upper_ci <- c_index + 1.96 * bootstrap_se\n", "\n", "# 打印结果\n", "cat(\"Concordance:\", c_index, \"\\n\")\n", "cat(\"lower_ci:\", lower_ci)\n", "cat(\"upper_ci:\", upper_ci)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#cox回归模型的线性预测值，可用于绘制ROC和DCA\n", "data_subset$nomogram2<-cox_model_multivar$linear.predictors\n", "head(data_subset$nomogram2)\n", "\n", "# data_subset$linear.predictors<-predict(cox_model_multivar,type=\"lp\",newdata=data_subset)\n", "# head(data_subset$linear.predictors)\n", "\n", "write.xlsx(data_subset, file = \"K:/2020-2023HCC/579hcc/clinical data/data/test预后最终版2.xlsx\", sheetName = \"Results\", rowNames = TRUE, colNames = TRUE)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["\n", "# DCA决策曲线绘制\n", "\n", "# 安装并加载dca.R软件包\n", "install.packages(\"dca.R\")\n", "library(dca.R)\n", "\n", "# 使用dca()函数分别计算Cox模型和变量的决策曲线\n", "coxm1_dca <- dca(coxm1, data = data)\n", "var1_dca <- dca(coxm1, data = data, variables = \"var1\")\n", "var2_dca <- dca(coxm1, data = data, variables = \"var2\")\n", "var3_dca <- dca(coxm1, data = data, variables = \"var3\")\n", "\n", "# 绘制决策曲线\n", "plot(coxm1_dca, main = \"Cox Model\")\n", "plot(var1_dca, main = \"Variable 1\")\n", "plot(var2_dca, main = \"Variable 2\")\n", "plot(var3_dca, main = \"Variable 3\")\n", "\n", "\n", "\n", "#绘制cox多因素回归的森林图\n", "# 加载库\n", "library(survival)\n", "library(survminer)\n", "library(ggsci)\n", "# 创建一个多因素回归森林图\n", "forest_plot <- ggforest(AIC_model, data = data_subset)\n", "\n", "# 创建一个多因素回归森林图，并进行美化\n", "forest_plot <- ggforest(\n", "  AIC_model,\n", "  data = data_subset\n", ") +\n", "  ggtitle(\"Cox Regression Forest Plot\") +  # 添加标题\n", "  xlab(\"Hazard Ratio\") +  # x 轴标签\n", "  scale_fill_jco() +  # 使用 R 包 \"ggsci\" 中的 \"jco\" 调色板\n", "  theme( # 使用主题修改图形外观\n", "    legend.position = \"right\",  # 图例位置\n", "    legend.title = element_text(face = \"bold\", size = 12),  # 图例标题样式\n", "    legend.text = element_text(size = 11),  # 图例文本样式\n", "    plot.title = element_text(hjust = 0.5, size = 14, face = \"bold\"),  # 标题样式\n", "    axis.title.x = element_text(face = \"bold\", size = 12),  # x 轴标签样式\n", "    axis.text = element_text(size = 11)  # 轴文本样式\n", "  )\n", "\n", "# 显示图形\n", "print(forest_plot)\n", "\n", "\n", "#绘制cox多因素回归的森林图\n", "library(ggplot2)\n", "library(broom)\n", "\n", "# 将模型结果转换为数据框\n", "tidy_model <- tidy(AIC_model)\n", "\n", "# 计算置信区间的宽度\n", "tidy_model$conf.low <- tidy_model$estimate - 1.96 * tidy_model$std.error\n", "tidy_model$conf.high <- tidy_model$estimate + 1.96 * tidy_model$std.error\n", "\n", "# 绘制森林图\n", "# 绘制森林图并添加文本标签\n", "ggplot(tidy_model, aes(x = estimate, y = term, color = term)) +\n", "  geom_point() +  # 绘制点\n", "  geom_errorbarh(aes(xmin = conf.low, xmax = conf.high), height = 0.2) +  # 绘制水平误差线\n", "  geom_vline(xintercept = 0, linetype = \"dashed\", color = \"grey\") +  # 绘制垂直参考线\n", "  geom_text(aes(label = paste0(\"HR=\", round(estimate, 2), \" (95% CI \", round(conf.low, 2), \"-\", round(conf.high, 2), \")\")),\n", "            hjust = 1.1, size = 3, color = \"black\") +  # 添加文本标签\n", "  labs(title = \"Forest Plot of AIC Model\", x = \"Hazard Ratio\", y = \"Variables\") +\n", "  theme_minimal() +  # 使用简洁的主题\n", "  theme(legend.position = \"none\",  # 不显示图例\n", "        axis.text.y = element_text(color = \"black\"))  # 设置y轴文本颜色为黑色"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["## 第6章代码开始\n", "\n", "#Cox回归案例1\n", "library(foreign)\n", "library(survival)\n", "\n", "pancer <- read.spss('pancer.sav')\n", "pancer <- as.data.frame(pancer)\n", "head(pancer)\n", "\n", "pancer$censor <- ifelse(pancer$censor=='死亡',1,0)\n", "pancer$Gender <- as.factor(ifelse(pancer$sex=='男',\"Male\",\"Female\"))\n", "pancer$ch <- as.factor(ifelse(pancer$ch=='CH3', \"ch\",\"nonch\"))\n", "\n", "#pancer$ch <- relevel(pancer$ch,ref=\"CH0\") #设置因子的参照水平\n", "#pancer$ch<- factor(pancer$ch,order=TRUE) #设置为等级变量\n", "#options(contrasts=c(\"contr.treatment\", \"contr.treatment\")) #指定等级变量的参照水平\n", "#pancer$Gender <- relevel(pancer$Gender,ref='Female')\n", "\n", "f<-coxph(Surv(time,censor==1)~age+Gender+trt+bui+ch+p+stage,data=pancer)\n", "summary(f)\n", "sum.surv<-summary(f)\n", "c_index<-sum.surv$concordance\n", "c_index\n", "\n", "#Cox回归案例2\n", "library(survival)\n", "example15_4  <- read.table (\"example15_4.csv\", header=TRUE, sep=\",\")\n", "attach(example15_4)\n", "coxmodel  <- coxph(Surv(days, censor)~group)\n", "summary(coxmodel)\n", "coxmode2  <- coxph(Surv(days, censor)~group+renal)\n", "summary(coxmode2)\n", "anova(coxmodel,coxmode2)\n", "detach(example15_4)\n", "\n", "#Cox回归案例3\n", "data('GBSG2',package = 'TH.data')\n", "head(GBSG2)\n", "plot(survfit(Surv(time, cens)~horTh,data = GBSG2),lty = c(2,1), col = c(2,1), mark.time = T)\n", "legend('bottomright', legend = c('yes','no'), lty = c(2,1), col = c(2,1))\n", "\n", "coxreg <- coxph(Surv(time,cens)~.,data = GBSG2)\n", "summary(coxreg)\n", "\n", "#install.packages(\"party\")\n", "library(party)\n", "tree <- ctree(Surv(time,cens)~.,data = GBSG2)\n", "plot(tree)\n", "\n", "\n", "# <PERSON>-<PERSON><PERSON>法估计\n", "library(survival)\n", "library(ISwR)\n", "attach(melanom)\n", "names(melanom)\n", "\n", "#Surv(days, status==1)\n", "#survfit(Surv(days, status==1)~1)\n", "surv.all <- survfit(Surv(days,status==1)~1)\n", "summary(surv.all)\n", "plot(surv.all,col=\"blue\")\n", "\n", "surv.bysex <- survfit(Surv(days,status==1)~sex)\n", "summary(surv.bysex)\n", "plot(surv.by<PERSON>)\n", "plot(surv.by<PERSON>, conf.int=T, col=c(\"red\",\"blue\"))\n", "legend(locator(n=1),legend=c(\"male\",\"female\"),lty=1,col=c(\"blue\",\"red\"))\n", "\n", "\n", "## log-rank test\n", "f1<-survdiff(Surv(days,status==1)~sex,rho=0)\n", "f1\n", "f2<-survdiff(Surv(days,status==1)~sex,rho=1)\n", "f2\n", "f3<-survdiff(Surv(days,status==1)~sex+strata(ulc)) # With rho = 0 this is the log-rank or Mantel-Haenszel test, and with rho = 1 it is equivalent to the Pet<PERSON> & Peto modification of the <PERSON><PERSON>-<PERSON> test.\n", "f3\n", "\n", "## cox regression\n", "f4<-coxph(Surv(days,status==1)~sex)\n", "summary(f4)\n", "#summary(coxph(Surv(days,status==1)~sex))\n", "f5<-coxph(Surv(days,status==1)~sex+log(thick)+ulc)\n", "summary(f5)\n", "#summary(coxph(Surv(days,status==1)~sex+log(thick)+strata(ulc)))\n", "plot(survfit(coxph(Surv(days,status==1)~\n", "                     log(thick)+sex+ulc)),col=c(\"red\",\"blue\"))\n", "#plot(survfit(coxph(Surv(days,status==1)~\n", "                       #log(thick)+sex+strata(ulc))),col=c(\"red\",\"blue\"))\n", "legend(locator(n=1),legend=c(\"ulceration present\",\"ulceration absent\"),lty=1,col=c(\"red\",\"blue\"))\n", "\n", "detach(melanom)\n", "\n", "# LifeTable寿命表法\n", "hmohiv<-read.table(\"hmohiv.csv\", sep=\",\", header = TRUE)\n", "attach(hmohiv)\n", "head(hmohiv)\n", "library(KMsurv)\n", "library(nlme)\n", "t6m<-floor(time/6)\n", "tall<-data.frame(t6m, censor)\n", "die<-gsummary(tall, sum, groups=t6m)\n", "total<-gsummary(tall, length, groups=t6m)\n", "rm(t6m)\n", "ltab.data<-cbind(die[,1:2], total[,2])\n", "detach(hmohiv)\n", "attach(ltab.data)\n", "\n", "lt=length(t6m)\n", "t6m[lt+1]=NA\n", "nevent=censor\n", "nlost=total[,2] - censor\n", "mytable<-lifetab(t6m, 100, nlost, nevent)\n", "mytable[,1:5]\n", "plot(t6m[1:11], mytable[,5], type=\"s\", xlab=\"Survival time in every 6 month\", \n", "     ylab=\"Proportion Surviving\")\n", "detach(ltab.data)\n", "\n", "#logrank检验案例1\n", "#install.packages(\"survival\")\n", "library(survival)\n", "example15_3 <- read.table (\"example15_3.csv\", header=TRUE, sep=\",\")\n", "attach(example15_3)\n", "total <- survfit(Surv(t, censor==1)~1)\n", "summary(total)\n", "plot(total,conf.int=F)\n", "separate <- survfit(Surv(t, censor==1)~group)\n", "summary(separate)\n", "plot(separate, lty = c('solid','dashed'), col=c('black','blue'),\n", "     xlab='survival time in days',ylab='survival probabilities')\n", "legend('topright', c('Group A',' Group B'), lty=c('solid','dashed'),\n", "       col=c('black','blue'))\n", "survdiff(Surv(t, censor)~group)\n", "survdiff(Surv(t, censor)~group,rho=1) # rho = 1 it is equivalent to the <PERSON><PERSON> & <PERSON>o modification of the <PERSON><PERSON><PERSON><PERSON> test.\n", "detach(example15_3)\n", "\n", "#logrank检验案例2\n", "library(coin)\n", "data(glioma)\n", "library(survival)\n", "g3 <- subset(glioma, histology =='Grade3')\n", "fit <- survfit(Surv(time, event)~group,data = g3)\n", "plot(fit, lty = c(2,1), col = c(2,1))\n", "legend('bottomright', legend = c('Control','Treatment'), lty = c(2,1), col = c(2,1))\n", "survdiff(Surv(time, event)~group,data = g3) \n", "logrank_test(Surv(time, event)~group,data = g3, distribution =\"exact\")\n", "logrank_test(Surv(time, event)~group|histology,data = glioma, distribution = approximate(B = 1000)) #两组比较,coin包 logrank_test函数#SurvivalTests {coin}\n", "\n", "#画一幅高水准的生存曲线\n", "library(survival)\n", "library(survminer)\n", "fit <- survfit(Surv(time, status) ~ sex, data = lung)\n", "\n", "ggsurvplot(fit,\n", "           pval = TRUE, # 在图上添加log rank检验的p值\n", "           conf.int = TRUE,# 添加置信区间\n", "           risk.table = TRUE, # 在图下方添加风险表\n", "           risk.table.col = \"strata\", # 根据数据分组为风险表添加颜色\n", "           linetype = \"strata\", # 改变不同组别的生存曲线的线型\n", "           surv.median.line = \"hv\", # 标注出中位生存时间\n", "           ggtheme = theme_bw(), # 改变图形风格\n", "           palette = c(\"#E7B800\", \"#2E9FDF\")) # 图形颜色风格\n", "\n", "\n", "ggsurvplot(\n", "  fit,                    \n", "  pval = FALSE,             \n", "  conf.int = TRUE, \n", "  fun = \"cumhaz\",\n", "  conf.int.style = \"ribbon\",  # 设置置信区间的风格\n", "  xlab = \"Time in days\",   # 设置x轴标签\n", "  break.time.by = 200,     # 将x轴按照200为间隔进行切分\n", "  ggtheme = theme_light(), # 设置图形风格\n", "  risk.table = \"abs_pct\",  # 在风险表中添加绝对数和相对数\n", "  risk.table.y.text.col = TRUE,# 设置风险表的文字颜色\n", "  risk.table.y.text = FALSE,# 以条柱展示风险表的标签，而非文字\n", "  ncensor.plot = TRUE,      # 展示随访过程中不同时间点死亡和删失的情况\n", "  surv.median.line = \"hv\",  # 添加中位生存时间\n", "  legend.labs = \n", "    c(\"Male\", \"Female\"),    # 改变图例标签\n", "  palette = \n", "    c(\"#E7B800\", \"#2E9FDF\") # 设置颜色\n", ")\n", "\n", "\n", "ggsurvplot(fit,\n", "           conf.int = TRUE,\n", "           risk.table.col = \"strata\", \n", "           ggtheme = theme_bw(), \n", "           palette = c(\"#E7B800\", \"#2E9FDF\"),\n", "           fun = \"cumhaz\")\n", "dev.off()\n", "\n", "\n", "## 第6章代码结束\n", "R.home()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}