{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["remove.packages(\"lintr\")"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#方法1，根据分组变量绘制生存曲线\n", "#install.packages(\"readxl\")\n", "# install.packages(\"tidyr\")\n", "# install.packages(\"survival\", repos = \"https://cloud.r-project.org/\")\n", "\n", "library(readxl)\n", "library(survival)\n", "library(tidyr)\n", "# install.packages(\"survminer\")\n", "library(survminer)\n", "library(foreign) #导入外部数据\n", "#survivalHCC<- read.csv(\"K:/2020-2023HCC/579hcc/模型代码总结/R-code/survivalHCC.csv\")\n", "survivalHCC <- read_excel(\"K:/2020-2023HCC/579hcc/clinical data/data/test预后最终版.xlsx\")\n", "head(survivalHCC)\n", "\n", "\n", "#方法1 修改因子水平标签，时间变量为time，状态变量为status，分组变量为VETC\n", "#survivalHCC$DLRap <- factor(survivalHCC$DLRap, levels = c(0, 1), labels = c(\"Low DRS in AP\", \"High DRS in AP\"))  # 分组变量\n", "\n", "\n", "# 创建生存对象\n", "surv_obj <- Surv(time = survivalHCC$PFS, event = survivalHCC$status)\n", "\n", "# 进行生存分析\n", "fit <- survfit(surv_obj ~ DLRap, data = survivalHCC)\n", "print(fit)\n", "\n", "# 绘制生存曲线和风险表\n", "ggsurvplot(fit,\n", "           pval = TRUE, #在图上添加log rank检验的p值\n", "           conf.int = TRUE,#添加置信区间\n", "           break.time.by = 10,\n", "           risk.table = TRUE, #在图下方添加风险表\n", "           risk.table.col = \"strata\", # 根据数据分组为风险表添加颜色\n", "           linetype = \"strata\", # 改变不同组别的生存曲线的线型\n", "           surv.median.line = \"hv\", # 标注出中位生存时间\n", "           ggtheme = theme_bw(), # 改变图形风格\n", "           legend.labs =  c(\"Low risk score\", \"High risk score\"),    # 改变图例标签\n", "           # xlim = c(0, 60), #x轴显示的时间范围,可不设置\n", "           palette = c(\"#E7B800\", \"#2E9FDF\"),\n", "           risk.table.title = \"Number at risk\",\n", "           title = \"Progression-free survival \")  # 修改图的标题\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#方法2根据cox回归列线图模型的线性预测值绘制生存曲线，surv_categorize函数自动分组\n", "library(readxl)\n", "library(survival)\n", "library(tidyr)\n", "# install.packages(\"survminer\") # nolint: commented_code_linter.\n", "library(survminer)\n", "library(foreign) #导入外部数据\n", "# survivalHCC<- read.csv(\"K:/2020-2023HCC/579hcc/模型代码总结/R-code/survivalHCC.csv\") # nolint\n", "survivalHCC <- read_excel(\"K:/2020-2023HCC/579hcc/clinical data/data/train30预后最终版.xlsx\")\n", "head(survivalHCC)\n", "# 连续性变量进行分层\n", "res.cut <- surv_cutpoint(survivalHCC, time = \"PFS\", event = \"status\",\n", "                         variables = \"nomogram\"\n", ")\n", "res.cut #最佳的cutpoint\n", "\n", "res.cat <- surv_categorize(res.cut, labels = c(\"Low risk score\", \"High risk score\")) # nolint\n", "res.cat\n", "\n", "# fit <- survfit(Surv(PFS, status) ~nomogram, data = res.cat)\n", "# ggsurvplot(fit, data = res.cat, pval = T)\n", "\n", "\n", "# 创建生存对象\n", "surv_obj <- Surv(time = survivalHCC$PFS, event = survivalHCC$status)\n", "\n", "# 进行生存分析\n", "fit <- survfit(surv_obj ~nomogram, data = res.cat)\n", "print(fit)\n", "\n", "# 绘制生存曲线和风险表\n", "ggsurvplot(fit,\n", "           pval = TRUE, #在图上添加log rank检验的p值\n", "           conf.int = TRUE,#添加置信区间\n", "           break.time.by = 10,\n", "           risk.table = TRUE, #在图下方添加风险表\n", "           risk.table.col = \"strata\", # 根据数据分组为风险表添加颜色\n", "           linetype = \"strata\", # 改变不同组别的生存曲线的线型\n", "           surv.median.line = \"hv\", # 标注出中位生存时间\n", "           ggtheme = theme_bw(), # 改变图形风格\n", "           legend.labs =  c(\"Low risk score\", \"High risk score\"),    # 改变图例标签\n", "           # xlim = c(0, 60), #x轴显示的时间范围,可不设置\n", "           palette = c(\"#E7B800\", \"#2E9FDF\"),\n", "           risk.table.title = \"Number at risk\",\n", "           title = \"Progression-free survival \")  # 修改图的标题\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A tibble: 6 × 27</caption>\n", "<thead>\n", "\t<tr><th scope=col>姓名</th><th scope=col>name</th><th scope=col>ID</th><th scope=col>sex</th><th scope=col>age</th><th scope=col>HBCV</th><th scope=col>Cirrosis</th><th scope=col>ALT</th><th scope=col>AST</th><th scope=col>GGT</th><th scope=col>⋯</th><th scope=col>PFS2</th><th scope=col>ap_score</th><th scope=col>pp_score</th><th scope=col>hbp_score</th><th scope=col>cp_score</th><th scope=col>bingliscore</th><th scope=col>deep_score</th><th scope=col>DLRap</th><th scope=col>nomogram</th><th scope=col>nomogram2</th></tr>\n", "\t<tr><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>⋯</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th><th scope=col>&lt;dbl&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>倪正其</td><td>nizhen<PERSON>qi  </td><td>1</td><td>1</td><td>57</td><td>0</td><td>1</td><td>0</td><td>0</td><td>0</td><td>⋯</td><td>13.00</td><td>0.5472056</td><td>0.4893531</td><td>0.48174422</td><td>0.4662353</td><td>0.3463909</td><td>0.4325708</td><td>1</td><td> 0.30443766</td><td> 0.28607239</td></tr>\n", "\t<tr><td>潘群美</td><td>panq<PERSON><PERSON>  </td><td>2</td><td>0</td><td>64</td><td>1</td><td>1</td><td>0</td><td>0</td><td>0</td><td>⋯</td><td>18.00</td><td>0.5207324</td><td>0.3999922</td><td>0.29535785</td><td>0.3640607</td><td>0.4990969</td><td>0.7492169</td><td>1</td><td> 0.61747112</td><td> 0.50068901</td></tr>\n", "\t<tr><td>彭金玲</td><td>pengjinling</td><td>3</td><td>0</td><td>71</td><td>0</td><td>0</td><td>0</td><td>0</td><td>1</td><td>⋯</td><td>24.00</td><td>0.2613853</td><td>0.3555642</td><td>0.49943317</td><td>0.3534022</td><td>0.1897187</td><td>0.1652099</td><td>0</td><td>-0.79603888</td><td>-0.60764246</td></tr>\n", "\t<tr><td>祁菊美</td><td>qijumei    </td><td>6</td><td>0</td><td>64</td><td>1</td><td>0</td><td>1</td><td>1</td><td>0</td><td>⋯</td><td>24.00</td><td>0.2304951</td><td>0.3996372</td><td>0.18132716</td><td>0.2218082</td><td>0.2961971</td><td>0.7500128</td><td>0</td><td>-0.50477923</td><td>-0.22003144</td></tr>\n", "\t<tr><td>祁陆  </td><td>qilu       </td><td>7</td><td>1</td><td>54</td><td>1</td><td>1</td><td>0</td><td>0</td><td>0</td><td>⋯</td><td>11.09</td><td>0.3581947</td><td>0.2551871</td><td>0.20380018</td><td>0.2278994</td><td>0.1897187</td><td>0.5888630</td><td>0</td><td>-0.03284438</td><td>-0.07626289</td></tr>\n", "\t<tr><td>钱金贞</td><td>qianjinzhen</td><td>4</td><td>0</td><td>69</td><td>1</td><td>1</td><td>0</td><td>0</td><td>0</td><td>⋯</td><td>24.00</td><td>0.2562699</td><td>0.2418609</td><td>0.08303961</td><td>0.1761094</td><td>0.3108137</td><td>0.1652099</td><td>0</td><td>-0.57958377</td><td>-0.58490449</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A tibble: 6 × 27\n", "\\begin{tabular}{lllllllllllllllllllll}\n", " 姓名 & name & ID & sex & age & HBCV & Cirrosis & ALT & AST & GGT & ⋯ & PFS2 & ap\\_score & pp\\_score & hbp\\_score & cp\\_score & bingliscore & deep\\_score & DLRap & nomogram & nomogram2\\\\\n", " <chr> & <chr> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & ⋯ & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl> & <dbl>\\\\\n", "\\hline\n", "\t 倪正其 & nizhengqi   & 1 & 1 & 57 & 0 & 1 & 0 & 0 & 0 & ⋯ & 13.00 & 0.5472056 & 0.4893531 & 0.48174422 & 0.4662353 & 0.3463909 & 0.4325708 & 1 &  0.30443766 &  0.28607239\\\\\n", "\t 潘群美 & panqunmei   & 2 & 0 & 64 & 1 & 1 & 0 & 0 & 0 & ⋯ & 18.00 & 0.5207324 & 0.3999922 & 0.29535785 & 0.3640607 & 0.4990969 & 0.7492169 & 1 &  0.61747112 &  0.50068901\\\\\n", "\t 彭金玲 & pengjinling & 3 & 0 & 71 & 0 & 0 & 0 & 0 & 1 & ⋯ & 24.00 & 0.2613853 & 0.3555642 & 0.49943317 & 0.3534022 & 0.1897187 & 0.1652099 & 0 & -0.79603888 & -0.60764246\\\\\n", "\t 祁菊美 & qijumei     & 6 & 0 & 64 & 1 & 0 & 1 & 1 & 0 & ⋯ & 24.00 & 0.2304951 & 0.3996372 & 0.18132716 & 0.2218082 & 0.2961971 & 0.7500128 & 0 & -0.50477923 & -0.22003144\\\\\n", "\t 祁陆   & qilu        & 7 & 1 & 54 & 1 & 1 & 0 & 0 & 0 & ⋯ & 11.09 & 0.3581947 & 0.2551871 & 0.20380018 & 0.2278994 & 0.1897187 & 0.5888630 & 0 & -0.03284438 & -0.07626289\\\\\n", "\t 钱金贞 & qianjinzhen & 4 & 0 & 69 & 1 & 1 & 0 & 0 & 0 & ⋯ & 24.00 & 0.2562699 & 0.2418609 & 0.08303961 & 0.1761094 & 0.3108137 & 0.1652099 & 0 & -0.57958377 & -0.58490449\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A tibble: 6 × 27\n", "\n", "| 姓名 &lt;chr&gt; | name &lt;chr&gt; | ID &lt;dbl&gt; | sex &lt;dbl&gt; | age &lt;dbl&gt; | HBCV &lt;dbl&gt; | Cirrosis &lt;dbl&gt; | ALT &lt;dbl&gt; | AST &lt;dbl&gt; | GGT &lt;dbl&gt; | ⋯ ⋯ | PFS2 &lt;dbl&gt; | ap_score &lt;dbl&gt; | pp_score &lt;dbl&gt; | hbp_score &lt;dbl&gt; | cp_score &lt;dbl&gt; | bingliscore &lt;dbl&gt; | deep_score &lt;dbl&gt; | DLRap &lt;dbl&gt; | nomogram &lt;dbl&gt; | nomogram2 &lt;dbl&gt; |\n", "|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|\n", "| 倪正其 | nizhengqi   | 1 | 1 | 57 | 0 | 1 | 0 | 0 | 0 | ⋯ | 13.00 | 0.5472056 | 0.4893531 | 0.48174422 | 0.4662353 | 0.3463909 | 0.4325708 | 1 |  0.30443766 |  0.28607239 |\n", "| 潘群美 | panqunmei   | 2 | 0 | 64 | 1 | 1 | 0 | 0 | 0 | ⋯ | 18.00 | 0.5207324 | 0.3999922 | 0.29535785 | 0.3640607 | 0.4990969 | 0.7492169 | 1 |  0.61747112 |  0.50068901 |\n", "| 彭金玲 | pengjinling | 3 | 0 | 71 | 0 | 0 | 0 | 0 | 1 | ⋯ | 24.00 | 0.2613853 | 0.3555642 | 0.49943317 | 0.3534022 | 0.1897187 | 0.1652099 | 0 | -0.79603888 | -0.60764246 |\n", "| 祁菊美 | qijumei     | 6 | 0 | 64 | 1 | 0 | 1 | 1 | 0 | ⋯ | 24.00 | 0.2304951 | 0.3996372 | 0.18132716 | 0.2218082 | 0.2961971 | 0.7500128 | 0 | -0.50477923 | -0.22003144 |\n", "| 祁陆   | qilu        | 7 | 1 | 54 | 1 | 1 | 0 | 0 | 0 | ⋯ | 11.09 | 0.3581947 | 0.2551871 | 0.20380018 | 0.2278994 | 0.1897187 | 0.5888630 | 0 | -0.03284438 | -0.07626289 |\n", "| 钱金贞 | qianjinzhen | 4 | 0 | 69 | 1 | 1 | 0 | 0 | 0 | ⋯ | 24.00 | 0.2562699 | 0.2418609 | 0.08303961 | 0.1761094 | 0.3108137 | 0.1652099 | 0 | -0.57958377 | -0.58490449 |\n", "\n"], "text/plain": ["  姓名   name        ID sex age HBCV Cirrosis ALT AST GGT ⋯ PFS2  ap_score \n", "1 倪正其 nizhengqi   1  1   57  0    1        0   0   0   ⋯ 13.00 0.5472056\n", "2 潘群美 panqunmei   2  0   64  1    1        0   0   0   ⋯ 18.00 0.5207324\n", "3 彭金玲 peng<PERSON>ling 3  0   71  0    0        0   0   1   ⋯ 24.00 0.2613853\n", "4 祁菊美 qijumei     6  0   64  1    0        1   1   0   ⋯ 24.00 0.2304951\n", "5 祁陆   qilu        7  1   54  1    1        0   0   0   ⋯ 11.09 0.3581947\n", "6 钱金贞 qianjinzhen 4  0   69  1    1        0   0   0   ⋯ 24.00 0.2562699\n", "  pp_score  hbp_score  cp_score  bingliscore deep_score DLRap nomogram   \n", "1 0.4893531 0.48174422 0.4662353 0.3463909   0.4325708  1      0.30443766\n", "2 0.3999922 0.29535785 0.3640607 0.4990969   0.7492169  1      0.61747112\n", "3 0.3555642 0.49943317 0.3534022 0.1897187   0.1652099  0     -0.79603888\n", "4 0.3996372 0.18132716 0.2218082 0.2961971   0.7500128  0     -0.50477923\n", "5 0.2551871 0.20380018 0.2278994 0.1897187   0.5888630  0     -0.03284438\n", "6 0.2418609 0.08303961 0.1761094 0.3108137   0.1652099  0     -0.57958377\n", "  nomogram2  \n", "1  0.28607239\n", "2  0.50068901\n", "3 -0.60764246\n", "4 -0.22003144\n", "5 -0.07626289\n", "6 -0.58490449"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Call: survfit(formula = surv_obj ~ group, data = survivalHCC)\n", "\n", "                       n events median 0.95LCL 0.95UCL\n", "group=High risk score 62     32     21    17.1      NA\n", "group=Low risk score  60     12     NA      NA      NA\n"]}, {"data": {"image/png": "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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["#方法3  根据线性预测值中位数进行分组\n", "library(readxl)\n", "library(survival)\n", "library(tidyr)\n", "library(ggplot2)\n", "# install.packages(\"survminer\")\n", "library(survminer)\n", "library(foreign) #导入外部数据\n", "# survivalHCC<- read.csv(\"K:/2020-2023HCC/579hcc/模型代码总结/R-code/survivalHCC.csv\")\n", "survivalHCC <- read_excel(\"K:/2020-2023HCC/579hcc/clinical data/data/test预后最终版.xlsx\")\n", "head(survivalHCC)\n", "\n", "# 计算nomogram的中位数或平均数\n", "# median_nomogram <- median(survivalHCC$nomogram) #median   mean\n", "median_nomogram = -0.088\n", "\n", "# 根据中位数将线性预测值分割为离散的分类变量\n", "survivalHCC$group <- ifelse(survivalHCC$nomogram2 <= median_nomogram, \"Low risk score\", \"High risk score\")\n", "\n", "# 创建生存对象\n", "surv_obj <- Surv(time = survivalHCC$PFS2, event = survivalHCC$status2)\n", "\n", "# 进行生存分析\n", "fit <- survfit(surv_obj ~ group, data = survivalHCC)\n", "print(fit)\n", "\n", "# # 绘制生存曲线和风险表\n", "ggsurvplot(fit,\n", "           pval = TRUE, #在图上添加log rank检验的p值\n", "           conf.int = TRUE,#添加置信区间\n", "           break.time.by = 5,\n", "           risk.table = TRUE, #在图下方添加风险表\n", "           risk.table.col = \"strata\", # 根据数据分组为风险表添加颜色          \n", "           tables.height = 0.25,# Adjust the height of the risk table     \n", "           linetype = \"strata\", # 改变不同组别的生存曲线的线型\n", "           surv.median.line = \"hv\", # 标注出中位生存时间\n", "           ggtheme = theme_bw(), # 改变图形风格           \n", "           legend.labs =  c(\"High risk score\", \"Low risk score\"),    # 改变图例标签\n", "           # xlim = c(0, 24), #x轴显示的时间范围,可不设置\n", "           palette = c(\"#E7B800\", \"#2E9FDF\"),\n", "           risk.table.title = \"Number at risk\",\n", "           title = \"Progression-free survival \")  # 修改图的标题\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["#方法2  不修改标签\n", "library(survival)\n", "library(survminer)\n", "library(foreign) #导入外部数据\n", "# survivalHCC<- read.csv(\"K:/2020-2023HCC/579hcc/模型代码总结/R-code/survivalHCC.csv\")\n", "survivalHCC <- read_excel(\"K:/2020-2023HCC/579hcc/clinical data/data/train30预后.xlsx\")\n", "\n", "fit <- survfit(Surv(PFS, status) ~ VETC, data = survivalHCC)\n", "fit\n", "ggsurvplot(fit,\n", "           pval = TRUE, #在图上添加log rank检验的p值\n", "           conf.int = TRUE,#添加置信区间\n", "           break.time.by = 10,\n", "           risk.table = TRUE, #在图下方添加风险表\n", "           risk.table.col = \"strata\", # 根据数据分组为风险表添加颜色\n", "           linetype = \"strata\", # 改变不同组别的生存曲线的线型\n", "           surv.median.line = \"hv\", # 标注出中位生存时间\n", "           ggtheme = theme_bw(), # 改变图形风格\n", "           # xlim = c(0, 60), #x轴显示的时间范围,可不设置\n", "           palette = c(\"#E7B800\", \"#2E9FDF\"))#图形颜色风格\n", "\n", "\n", "\n", "##survivalHCC.csv数据集\n", "library(foreign) #导入外部数据\n", "survivalHCC<- read.csv(\"K:/2020-2023HCC/579hcc/模型代码总结/R-code/survivalHCC.csv\")\n", "head(survivalHCC)\n", "\n", "fit <- survfit(Surv(time1, status1) ~ VETC, data = survivalHCC)\n", "fit\n", "ggsurvplot(fit,\n", "           pval = TRUE, #在图上添加log rank检验的p值\n", "           conf.int = TRUE,#添加置信区间\n", "           break.time.by = 10,\n", "           risk.table = TRUE, #在图下方添加风险表\n", "           risk.table.col = \"strata\", # 根据数据分组为风险表添加颜色\n", "           linetype = \"strata\", # 改变不同组别的生存曲线的线型\n", "           surv.median.line = \"hv\", # 标注出中位生存时间\n", "           ggtheme = theme_bw(), # 改变图形风格\n", "           palette = c(\"#E7B800\", \"#2E9FDF\"))#图形颜色风格\n", "\n", "\n", "#R自带数据集\n", "library(survminer)\n", "fit <- survfit(Surv(time, status) ~ sex, data = lung)\n", "ggsurvplot(fit,\n", "           pval = TRUE, #在图上添加log rank检验的p值\n", "           conf.int = TRUE,#添加置信区间\n", "           risk.table = TRUE, #在图下方添加风险表\n", "           risk.table.col = \"strata\", # 根据数据分组为风险表添加颜色\n", "           linetype = \"strata\", # 改变不同组别的生存曲线的线型\n", "           surv.median.line = \"hv\", # 标注出中位生存时间\n", "           ggtheme = theme_bw(), # 改变图形风格\n", "           palette = c(\"#E7B800\", \"#2E9FDF\"))#图形颜色风格\n", "\n", "\n", "ggsurvplot(\n", "  fit,                    \n", "  pval = FALSE,             \n", "  conf.int = TRUE, \n", "  fun = \"cumhaz\",\n", "  conf.int.style = \"ribbon\",  # 设置置信区间的风格\n", "  xlab = \"Time in days\",   # 设置x轴标签\n", "  break.time.by = 200,     # 将x轴按照200为间隔进行切分\n", "  ggtheme = theme_light(), # 设置图形风格\n", "  risk.table = \"abs_pct\",  # 在风险表中添加绝对数和相对数\n", "  risk.table.y.text.col = TRUE,# 设置风险表的文字颜色\n", "  risk.table.y.text = FALSE,# 以条柱展示风险表的标签，而非文字\n", "  ncensor.plot = TRUE,      # 展示随访过程中不同时间点死亡和删失的情况\n", "  surv.median.line = \"hv\",  # 添加中位生存时间\n", "  legend.labs = \n", "    c(\"Male\", \"Female\"),    # 改变图例标签\n", "  palette = \n", "    c(\"#E7B800\", \"#2E9FDF\") # 设置颜色\n", ")\n", "\n", "ggsurvplot(fit,\n", "           conf.int = TRUE,\n", "           risk.table.col = \"strata\", \n", "           ggtheme = theme_bw(), \n", "           palette = c(\"#E7B800\", \"#2E9FDF\"),\n", "           fun = \"cumhaz\")\n", "dev.off()\n"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.3"}}, "nbformat": 4, "nbformat_minor": 2}