#%%多变量logistic回归基于AIC和BIC
# install.packages("broom")
# install.packages("writexl")
library(readxl)
library(dplyr)
library(MASS)
library(broom)
library(writexl)

data <- read_excel("G:\\1.课题和论文\\2023-2024写文章\\2024SCI2投稿\\data\\radiomics模型结果\\new\\train_clinical_radiomics_PCC.xlsx")
# 获取列名
columns <- colnames(data)
# 打印列名
print(columns)

# 从第三列开始选择
data <- data[, 3:ncol(data)]

# 查看结果
print(data)

#设置因子变量
data <- data %>%
  mutate(across(c(PHCC,ALT,AST,GGT,AFP,number,LIRADS), as.factor))

#%% 使用 enter 方法拟合模型
# 对数据进行标准化，只对数值型特征
data_scaled <- data %>%
  mutate(across(where(is.numeric), scale))

# 使用标准化后的数据拟合模型
glm1 <- glm(PHCC ~ ., family = binomial(link = logit), data = data_scaled) # 二分类，对应Logit模型

summary_model <- summary(glm1)
print(summary_model)

#%%AIC逐步回归模型
step_model <- stepAIC(glm1,direction = 'backward') #both,backward结果R与python一致，forward不同

# 获取逐步回归模型结果
summary_model <- summary(step_model)
print(summary_model)

#%% 使用 BIC 进行逐步回归
step_model <- stepBIC(glm1, direction = 'backward', k = log(nrow(data))) # 使用 BIC

# 获取逐步回归模型结果
summary_model <- summary(step_model)
print(summary_model)

#%% 提取系数和其他信息
model_summary <- data.frame(
  term = rownames(summary_model$coefficients),
  estimate = summary_model$coefficients[, "Estimate"],
  std.error = summary_model$coefficients[, "Std. Error"],
  statistic = summary_model$coefficients[, "z value"],
  p.value = summary_model$coefficients[, "Pr(>|z|)"]
)

# 计算 OR 和 95% CI
model_summary <- model_summary %>%
  mutate(
    OR = exp(estimate),
    lower_CI = exp(estimate - 1.96 * std.error),
    upper_CI = exp(estimate + 1.96 * std.error)
  )

# 获取 AIC 值
aic_value <- AIC(step_model)

# 将 AIC 值添加到数据框中
model_summary <- rbind(model_summary, data.frame(term = "AIC", estimate = aic_value, std.error = NA, statistic = NA, p.value = NA, OR = NA, lower_CI = NA, upper_CI = NA))

# 保存为Excel文件
# write_xlsx(model_summary, 'G:\\1.课题和论文\\2023-2024写文章\\2024SCI2投稿\\data\\radiomics模型结果\\new\\train_clinical_radiomics_AIC筛选结果.xlsx')


#%% 根据AIC筛选后的特征，选择train，val和test数据集中的特征
#train数据集
# data <- read_excel("G:\\1.课题和论文\\2023-2024写文章\\2024SCI2投稿\\data\\radiomics模型结果\\new\\train_clinical_radiomics_PCC.xlsx")

#val数据集
data <- read_excel("G:\\1.课题和论文\\2023-2024写文章\\2024SCI2投稿\\data\\radiomics模型结果\\new\\test_clinical_radiomics.xlsx")


selected_features <- names(coef(step_model))[-1] # 去掉截距项

# 去除反引号
selected_features <- gsub("`", "", selected_features)

# 去除多余的空格
selected_features <- trimws(selected_features)

# 创建特征名称映射表
feature_mapping <- c(
  "AFP1" = "AFP",
  "AST1" = "AST"
)

# 使用映射表将新的特征名称映射回原始特征名称
selected_features <- ifelse(selected_features %in% names(feature_mapping), feature_mapping[selected_features], selected_features)

# 验证特征名称是否存在于原始数据集中
selected_features <- selected_features[selected_features %in% colnames(data)]

# 从原始数据集中提取这些特征的值
selected_data <- data[, c("name","PHCC", selected_features)]

# 查看提取后的数据
print(selected_data)

# 保存为Excel文件
write_xlsx(selected_data, 'G:\\1.课题和论文\\2023-2024写文章\\2024SCI2投稿\\data\\radiomics模型结果\\new\\test_clinical_radiomics_AIC筛选特征.xlsx')
