
#热图方法1：
data0 <- read.csv("K:/2020-2023HCC/579hcc/578hcc/data/promics/train_image.csv")
data <- data0[, 18:ncol(data0)]
# data0 = read.table("C:/Users/<USER>/Desktop/ch05/heatmap.txt", header=T)
# data = data0[,-1]
rownames(data) = data0[,1]

library(gplots)
heatmap.2(as.matrix(data))

##调整参数和颜色 #greenred  or  redblue  or bluered
library(RColorBrewer)
color = c(rep("orange",49),rep("blue",45))  #3和3分别为组别变量的例数
#color2 = c(rep("red",1300),rep("green",23))
heatmap.2(as.matrix(data),col = greenred(75), 
          scale = "row",dendrogram = 'both',
          key = TRUE, symkey = FALSE, density.info = "none", 
          trace = "none", cexRow = 0.5,
          main = "Heatmap",xlab = "MVI",ylab = "radiomics", 
          ColSideColors = color,#RowSideColors= color2 #ColSideColors就按照样本读入的顺序分配颜色
          ) 

#相关性热图
#安装corrplot包并加载
# install.package("corrplot") 
library(corrplot) 
data0 <- read.csv("K:/2020-2023HCC/579hcc/578hcc/data/promics/train_features_min删除共线性变量.csv")
data <- data0[, 3:ncol(data0)]
# rownames(data) = data0[,1]

M <- cor(data)
M

#删除共线性变量，剔除高度相关性变量
library(caret)
highlycor= findCorrelation(M, cutoff = 0.80, verbose = T, names =TRUE)
print(highlycor)# 获取需要删除的变量名

# corrplot(M, method = "circle")
# corrplot(M, method = "square")
# corrplot(M, method = "number")
# corrplot(M, method = "color")
# corrplot(M,method="color",addCoef.col="grey")
# corrplot.mixed(M)

#相关性热图2  常用这个方法
# install.packages("ggcorrplot") 
library(ggplot2) 
library(ggcorrplot) 

ggcorrplot(cor(data),method="circle")
ggcorrplot(cor(data),method="square")
ggcorrplot(cor(data),method="square",lab = TRUE,colors = c("blue", "white", "red"))


#热图方法2
installed.packages("pheatmap")
library(pheatmap)

data0 = read.csv("C:/Users/<USER>/Desktop/ch05/train2.csv", header=T)
data = data0[,-1]
rownames(data) = data0[,1]
head(data)
data<-as.matrix(data)
pheatmap(data)
color.key<-c("#3300CC","#3399FF","white","#FF3333","#CC0000")
pheatmap(data,color=colorRampPalette(color.key)(50),border_color = NA,scale = "row")
pheatmap(data,color=colorRampPalette(color.key)(50),border_color = NA)
#设置cluster_row=FALSE)
pheatmap(data,cluster_row=FALSE,color=colorRampPalette(color.key)(50),border_color = NA,scale = "row")

#给样本添加标注信息
#分组信息( Group)：M1-M5为Case组，M6-M为 Control组
#用药信息(Dose)：M1-M5梯度分别为1-5，M6-M1梯度同样为1-5
annotation_col=data.frame(Group = rep(c("MVI+", "MVI-"), c(5, 5)))
#annotation_col=data.frame(Group = rep(c("HCC", "FNH"), c(3, 3)),Dose = c(1:5,1:5))
rownames(annotation_col)= colnames(data)

#给基因添加标注信息
#设定前20个基因属于 Cancer通路，后17个基因属于 Metabolism通路
annotation_row = data.frame(Pathway = rep(c("Cancer","Metabolism"), c(20,17)))
rownames(annotation_row) = rownames(data)
#设置 annotation col和 annotation row，分别对样本和基因添加附注
#pheatmap(data, color=colorRampPalette(color.key)(50),border_color = NA,
#           annotation_col = annotation_col, annotation_row = annotation_row,scale = "row")
  
pheatmap(data, color=colorRampPalette(color.key)(50),border_color = NA,
         annotation_col = annotation_col,scale = "row")
pheatmap(data, color=colorRampPalette(color.key)(50),border_color = NA,
         annotation_col = annotation_col,scale = "row",cluster_row=FALSE)
